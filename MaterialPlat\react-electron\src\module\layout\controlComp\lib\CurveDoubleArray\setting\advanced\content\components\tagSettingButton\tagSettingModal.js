import React, {
    useMemo, useState, useEffect, useRef
} from 'react'
import {
    Select, Form, Row, Col, Checkbox, Tree, Switch
} from 'antd'
import { v4 as uuidv4 } from 'uuid'
import { useTranslation } from 'react-i18next'
import ColorSelector from '@/components/colorSelector/index'
import VButton from '@/components/vButton'
import { useSelector } from 'react-redux'
import useResult from '@/hooks/useResult'
import VTransfer from '@/components/vTransfer'
import VModal from '@/components/vModal'
import cloneDeep from 'lodash/cloneDeep'
import { StepTagsModalContainer } from './style'
import { SOURCE_TYPE } from '../../../../../constants/constants'

const TAG_INIT_RESULT = {
    resultVariableId: '', // 结果id
    isAll: false,
    isAbbr: true,
    isName: true,
    isVal: true,
    isChunk: true,
    isLine: true,
    isSample: false,
    sampleCode: '',
    color: '#000000'
}

const { Item } = Form

const TagSettingModal = ({
    open, setOpen, value: curveGroup, onChange, treeData, axis, setAxis, isBufferCurve, sourceType
}) => {
    const { t } = useTranslation()

    const resultData = useSelector(state => state.template.resultData)
    const resultTestData = useSelector(state => state.template.resultTestData)
    const { getCurveResults } = useResult()

    const [form] = Form.useForm()

    const data = useRef()

    // 曲线上要显示的结果变量
    const resultsVarsList = useMemo(() => getCurveResults(), [resultData, resultTestData])

    const [lineTagData, setLineTagData] = useState()
    const [tagIndex, setTagIndex] = useState()
    const [currentCurvePosition, setCurrentCurvePosition] = useState(null) // 当前选中的曲线位置
    const [selectedTreeKey, setSelectedTreeKey] = useState(null) // 树选中状态
    const [selectedResult, setSelectedResult] = useState(null) // 穿梭框选中的结果变量ID

    useEffect(() => {
        data.current = cloneDeep(curveGroup)
        // 重置所有状态
        setSelectedTreeKey(null)
        setCurrentCurvePosition(null)
        setLineTagData(null)
        setTagIndex(undefined)
        setSelectedResult(null)
        form.resetFields()
    }, [open])

    const handleModalClose = () => {
        setOpen(false)
    }

    /**
     * 确认提交
     */
    const handleClickConfirm = async () => {
        // 将修改后的数据传递给父组件
        onChange(data.current)
        setOpen(false)
    }

    // 处理表单值变化
    const handleFormValuesChange = (changedValues, allValues) => {
        if (tagIndex !== undefined && lineTagData) {
            // 更新当前选中的tag数据
            const updatedTags = [...lineTagData]
            updatedTags[tagIndex] = {
                ...updatedTags[tagIndex],
                ...allValues
            }
            setLineTagData(updatedTags)

            // 同步更新到data.current中
            if (currentCurvePosition && data.current && data.current[axis] && data.current[axis].curves) {
                const { dataSourceKey, index } = currentCurvePosition
                if (data.current[axis].curves[dataSourceKey] && data.current[axis].curves[dataSourceKey].lines[index]) {
                    data.current[axis].curves[dataSourceKey].lines[index].pointTags = updatedTags
                }
            }

            // 如果勾选了"适用于所有结果"，则只同步发生变化的字段
            if (allValues.isAll && currentCurvePosition) {
                // 需要排除的字段
                const excludeFields = ['isAll', 'resultVariableId', 'id']

                // 获取需要同步的字段（只同步发生变化的字段）
                const fieldsToSync = Object.keys(changedValues).filter(field => !excludeFields.includes(field))

                if (fieldsToSync.length > 0) {
                    const syncedTags = updatedTags.map(tag => {
                        const syncedTag = { ...tag }
                        // 只同步发生变化的字段
                        fieldsToSync.forEach(field => {
                            syncedTag[field] = changedValues[field]
                        })
                        return syncedTag
                    })

                    setLineTagData(syncedTags)

                    // 同步更新到data.current中
                    const { dataSourceKey, index } = currentCurvePosition
                    if (data.current[axis].curves[dataSourceKey] && data.current[axis].curves[dataSourceKey].lines[index]) {
                        data.current[axis].curves[dataSourceKey].lines[index].pointTags = syncedTags
                    }
                }
            }
        }
    }

    // 选择结果变量
    const handleResultChange = (keys) => {
        setLineTagData((prev) => {
            const updatedTags = keys.map(key => ({
                ...TAG_INIT_RESULT,
                resultVariableId: key,
                id: uuidv4(),
                ...prev.find(result => result.resultVariableId === key)
            }))

            // 同步更新到data.current中
            if (currentCurvePosition && data.current && data.current[axis] && data.current[axis].curves) {
                const { dataSourceKey, index } = currentCurvePosition
                if (data.current[axis].curves[dataSourceKey] && data.current[axis].curves[dataSourceKey].lines[index]) {
                    data.current[axis].curves[dataSourceKey].lines[index].pointTags = updatedTags
                }
            }

            return updatedTags
        })
    }

    /**
     * @param {Object} result
     * 选择一个结果变量数据
     */
    const handleResultChangeWay = async (result) => {
        setSelectedResult(result)
        if (result) {
            const index = lineTagData.findIndex(m => m.resultVariableId === result.result_variable_id)
            setTagIndex(index)

            const d = lineTagData.find(m => m.resultVariableId === result.result_variable_id)

            form.setFieldsValue(d)
        }
    }

    const handleResultChangeDelWay = async (result) => {
        if (result) {
            const updatedTags = lineTagData.filter(m => m.resultVariableId !== result.result_variable_id)
            setLineTagData(updatedTags)

            // 同步更新到data.current中
            if (currentCurvePosition && data.current && data.current[axis] && data.current[axis].curves) {
                const { dataSourceKey, index } = currentCurvePosition
                if (data.current[axis].curves[dataSourceKey] && data.current[axis].curves[dataSourceKey].lines[index]) {
                    data.current[axis].curves[dataSourceKey].lines[index].pointTags = updatedTags
                }
            }

            // 如果删除的是当前选中的tag，重置选择状态
            if (tagIndex !== undefined && lineTagData[tagIndex] && lineTagData[tagIndex].resultVariableId === result.result_variable_id) {
                setTagIndex(undefined)
                setSelectedResult(null)
                form.resetFields()
            }
        }
    }

    return (
        <VModal
            open={open}
            onCancel={handleModalClose}
            width={1000}
            title={t('结果标签')}
            destroyOnClose
            footer={null}
        >
            <StepTagsModalContainer>
                <Row>
                    <Col span={24}>
                        <Item label={t('曲线组')}>
                            <Select
                                value={axis}
                                options={[
                                    {
                                        label: 'Y1轴',
                                        value: 'yAxis'
                                    },
                                    {
                                        label: 'Y2轴',
                                        value: 'y2Axis'
                                    }
                                ]}
                                onChange={(v) => {
                                    setAxis(v)
                                    // 切换轴时重置相关状态
                                    setCurrentCurvePosition(null)
                                    setLineTagData(null)
                                    setTagIndex(undefined)
                                    setSelectedTreeKey(null)
                                    form.resetFields()
                                }}
                            />
                        </Item>
                    </Col>
                </Row>
                <Row>
                    <Col span={8}>
                        <div
                            style={{
                                height: '900px',
                                overflow: 'scroll'
                            }}
                        >
                            <Tree
                                treeData={treeData}
                                blockNode
                                defaultExpandAll
                                selectedKeys={selectedTreeKey ? [selectedTreeKey] : []}
                                onSelect={(selectedKeys, info) => {
                                    const selectedKey = selectedKeys?.[0]
                                    setSelectedTreeKey(selectedKey)

                                    setSelectedResult(null)

                                    if (selectedKey && info.node) {
                                        const {
                                            dataSourceKey, index, code
                                        } = info.node

                                        // 记录当前选中的曲线位置
                                        setCurrentCurvePosition({ dataSourceKey, index })

                                        // 重置tag选择状态
                                        setTagIndex(undefined)
                                        setSelectedResult(null)
                                        form.resetFields()

                                        setLineTagData(data.current[axis].curves[dataSourceKey].lines[index].pointTags ?? [])
                                    } else {
                                        // 取消选择时重置状态
                                        setCurrentCurvePosition(null)
                                        setLineTagData(null)
                                        setTagIndex(undefined)
                                        setSelectedResult(null)
                                        form.resetFields()
                                    }
                                }}
                            />

                        </div>
                    </Col>
                    <Col span={16}>
                        <div className="chunk">
                            <VTransfer
                                disabled={!lineTagData}
                                listStyle={{
                                    width: '16vw',
                                    height: '45vh'
                                }}
                                targetKeys={
                                    lineTagData?.map(m => m.resultVariableId) ?? []
                                }
                                select={selectedResult}
                                onChange={handleResultChange}
                                dataSource={resultsVarsList}
                                onChangeWay={handleResultChangeWay}
                                onChangeDelWay={handleResultChangeDelWay}
                                oneWay
                                oneWayLabel="variable_name"
                                rowKey="result_variable_id"
                                render={(item) => item.variable_name}
                            />

                            <Form
                                form={form}
                                disabled={tagIndex === undefined}
                                onValuesChange={handleFormValuesChange}
                            >
                                <div style={{ display: 'flex', alignItems: 'center', gap: '3px' }}>
                                    <Item
                                        name="isAll"
                                        valuePropName="checked"
                                    >
                                        <Switch />
                                    </Item>
                                    <Item label={t('开启时的后续修改会同步到当前曲线所有结果')} colon={false} />
                                </div>

                                <div className="chunk">
                                    <Row>
                                        <Col span={6}>
                                            <Item
                                                name="isAbbr"
                                                valuePropName="checked"
                                            >
                                                <Checkbox>
                                                    {t('缩写')}
                                                </Checkbox>
                                            </Item>
                                        </Col>
                                        <Col span={6}>
                                            <Item
                                                name="isName"
                                                valuePropName="checked"
                                            >
                                                <Checkbox>
                                                    {t('名称')}
                                                </Checkbox>
                                            </Item>
                                        </Col>
                                        <Col span={6}>
                                            <Item
                                                name="isVal"
                                                valuePropName="checked"
                                            >
                                                <Checkbox>
                                                    {t('数值')}
                                                </Checkbox>
                                            </Item>
                                        </Col>
                                        <Col span={6}>
                                            <Item
                                                name="isChunk"
                                                valuePropName="checked"
                                            >
                                                <Checkbox>
                                                    {t('框架')}
                                                </Checkbox>
                                            </Item>
                                        </Col>
                                    </Row>
                                    <Row>
                                        <Col span={6}>
                                            <Item
                                                name="isLine"
                                                valuePropName="checked"
                                            >
                                                <Checkbox>
                                                    {t('连接线')}
                                                </Checkbox>
                                            </Item>
                                        </Col>
                                        <Col span={6}>
                                            <Item
                                                name="color"
                                                label={t('颜色')}
                                            >
                                                <ColorSelector />
                                            </Item>
                                        </Col>
                                        <Col span={6}>
                                            <Item
                                                name="isSample"
                                                valuePropName="checked"
                                                hidden={!isBufferCurve || sourceType === SOURCE_TYPE.单数据源}
                                            >
                                                <Checkbox>
                                                    {t('仅标注当前试样')}
                                                </Checkbox>
                                            </Item>
                                        </Col>
                                    </Row>
                                </div>
                            </Form>
                        </div>
                    </Col>

                </Row>

                <div className="footer-btns">
                    <VButton
                        onClick={handleClickConfirm}
                        type="primary"
                    >
                        {t('确认')}
                    </VButton>
                    <VButton
                        onClick={handleModalClose}
                    >
                        {t('取消')}
                    </VButton>
                </div>
            </StepTagsModalContainer>
        </VModal>
    )
}

export default TagSettingModal
