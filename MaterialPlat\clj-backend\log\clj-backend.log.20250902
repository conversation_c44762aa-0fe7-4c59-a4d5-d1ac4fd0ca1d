25-09-02 09:13:04:308 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/host/instance 
 - 参数: 
 {:Hosts [{:HostId "2d06ec3a-9ecd-422c-a76a-1afc16b47b24", :HostName "1", :ConfigId nil, :StationHardware nil} {:HostId "da28d67a-f0fc-46a6-ab37-d412e6ab9f26", :HostName "2", :ConfigId nil, :StationHardware nil}]} 
 =============================================================

25-09-02 09:13:04:613 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/hardware 
 - 参数: 
 {:HardwareDtos {"hardware_simulator-0000-hw-ccss-id-0000" {:DaChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :AdChannels [{:Sensors ["传感器0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0}], :CreepAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 10, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 11, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 12, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 13, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 14, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 15, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 16, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 17, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 18, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 19, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}], :HandBoxChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil}], :Devices ({:Hwkey "hardware_simulator", :SubId 0, :DeviceId "33d3a761-f180-4c0a-9135-4dbf045b96e0"}), :ServoAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 7, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0}], :UpDirectValue 5, :TensileUpOrDown 7, :PressUpOrDown 6} {:Hwkey "hardware_simulator", :RealIndex 3, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0}], :UpDirectValue 5, :TensileUpOrDown 5, :PressUpOrDown 5}], :InputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :OutputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :TempAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 10, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}]}}} 
 =============================================================

25-09-02 09:13:04:742 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/api/appserver/config/info 
 - 参数: 
 {:AppServerIp "*************", :AppServerPort 3000, :ComputerId "MP2QAVV0", :AppServerApiPort 3000} 
 =============================================================

25-09-02 09:13:04:807 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 登录"}
25-09-02 09:13:05:401 DESKTOP-3BSREDP INFO [clj-backend.modules.standby.service:160] - 运行 sync-to-share-http (HTTP POST)...
25-09-02 09:13:05:407 DESKTOP-3BSREDP WARN [clj-backend.modules.standby.service:170] - 从节点 IP 或端口未配置. 跳过 sync-to-share-http. 
25-09-02 09:13:10:184 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "打开项目 project_17"}
25-09-02 09:13:10:196 DESKTOP-3BSREDP WARN [clj-backend.db.connections:40] - 已为数据库连接 project_17 应用性能优化配置
25-09-02 09:13:10:198 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - 创建 project_17 项目库连接
25-09-02 09:13:10:223 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:861] - 模板开始实例化
25-09-02 09:13:15:627 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "016d1673-8af7-47ed-a1c3-138cba4ede9e", :code 0, :msg "模板生成成功"}
25-09-02 09:13:15:629 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-016d1673-8af7-47ed-a1c3-138cba4ede9e 中添加消息 {:ProcessId "016d1673-8af7-47ed-a1c3-138cba4ede9e", :code 0, :msg "模板生成成功"}
25-09-02 09:13:15:633 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "016d1673-8af7-47ed-a1c3-138cba4ede9e", :code 0, :msg "模板生成成功"}
25-09-02 09:13:16:965 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/sample-inst/select 
 - 参数: 
 {:ClassName "project_17", :CurrentInstCode "sample_5167aeea", :SelectedInstCodes []} 
 =============================================================

25-09-02 09:13:21:303 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 17, :project_name "高频-0828(1)(1)", :content "开始流程【默认执行】"}
25-09-02 09:13:21:319 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_17", :ProcessId "project_17-78639282-fa6a-4822-b502-d2144342e71a", :State "running"} 
 =============================================================

25-09-02 09:13:22:471 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_17", :ScriptId "7fbe3f62-92f5-4625-9d7b-f0461c2a7b40", :Result false}
25-09-02 09:13:22:472 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-7fbe3f62-92f5-4625-9d7b-f0461c2a7b40 中添加消息 {:ProcessId "project_17", :ScriptId "7fbe3f62-92f5-4625-9d7b-f0461c2a7b40", :Result false}
25-09-02 09:13:22:475 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_17", :ScriptId "7fbe3f62-92f5-4625-9d7b-f0461c2a7b40", :Result false}
25-09-02 09:13:22:525 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_17", :ScriptId "7cddc9a5-6992-46e3-9946-ceaf0dc56211", :Result false}
25-09-02 09:13:22:526 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-7cddc9a5-6992-46e3-9946-ceaf0dc56211 中添加消息 {:ProcessId "project_17", :ScriptId "7cddc9a5-6992-46e3-9946-ceaf0dc56211", :Result false}
25-09-02 09:13:22:527 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_17", :ScriptId "7cddc9a5-6992-46e3-9946-ceaf0dc56211", :Result false}
25-09-02 09:13:22:653 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_17", :ProcessID "project_17-78639282-fa6a-4822-b502-d2144342e71a", :SubTaskID "onlyAction-adf0e120-9035-4183-b8d4-1bd802f3644d", :MsgBody {:Cmd "start", :InstCode "sample_5167aeea", :ActionID "0d3ca2d0-242b-41e8-a002-6681e72f0c84"}}
25-09-02 09:13:22:661 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 17, :project_name "高频-0828(1)(1)", :content "开始流程【更新试验信息】"}
25-09-02 09:13:22:664 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_17", :ProcessId "project_17-0d3ca2d0-242b-41e8-a002-6681e72f0c84", :State "running"} 
 =============================================================

25-09-02 09:13:23:167 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 17, :project_name "高频-0828(1)(1)", :content "结束流程【更新试验信息】"}
25-09-02 09:13:23:170 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_17", :ProcessId "project_17-0d3ca2d0-242b-41e8-a002-6681e72f0c84", :State "finished"} 
 =============================================================

25-09-02 09:13:23:264 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_17", :ProcessID "project_17-78639282-fa6a-4822-b502-d2144342e71a", :SubTaskID "onlyAction-d6af69bd-4fe1-4427-9f59-7bf08dc81344", :MsgBody {:Cmd "start", :InstCode "sample_5167aeea", :ActionID "cd874ea2-bc09-487e-a784-75bf88d9e2c3"}}
25-09-02 09:13:23:273 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 17, :project_name "高频-0828(1)(1)", :content "开始流程【联机DAQ】"}
25-09-02 09:13:23:276 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_17", :ProcessId "project_17-cd874ea2-bc09-487e-a784-75bf88d9e2c3", :State "running"} 
 =============================================================

25-09-02 09:13:23:443 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 17, :project_name "高频-0828(1)(1)", :content "结束流程【默认执行】"}
25-09-02 09:13:23:446 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_17", :ProcessId "project_17-78639282-fa6a-4822-b502-d2144342e71a", :State "finished"} 
 =============================================================

25-09-02 09:58:33:415 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 17, :project_name "高频-0828(1)(1)", :content "开始流程【关闭项目执行】"}
25-09-02 09:58:33:419 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_17", :ProcessId "project_17-80f9c3ad-d214-405b-a10e-a16ec438b920", :State "running"} 
 =============================================================

25-09-02 09:58:33:453 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-common-service:85] - 项目关闭执行动作---完成
25-09-02 09:58:33:455 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "删除了【关闭项目 project_17】项目"}
25-09-02 09:58:33:468 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 17, :project_name "高频-0828(1)(1)", :content "结束流程【默认执行】"}
25-09-02 09:58:33:473 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 17, :project_name "高频-0828(1)(1)", :content "结束流程【更新试验信息】"}
25-09-02 09:58:33:479 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 17, :project_name "高频-0828(1)(1)", :content "结束流程【联机DAQ】"}
25-09-02 09:58:33:481 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_17", :ProcessId "project_17-cd874ea2-bc09-487e-a784-75bf88d9e2c3", :State "finished"} 
 =============================================================

25-09-02 09:58:33:569 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 17, :project_name "高频-0828(1)(1)", :content "结束流程【关闭项目执行】"}
25-09-02 09:58:33:570 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_17", :ProcessId "project_17-80f9c3ad-d214-405b-a10e-a16ec438b920", :State "finished"} 
 =============================================================

25-09-02 09:58:35:333 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 d:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1756372411236_project_17.db 成功
25-09-02 09:58:35:389 DESKTOP-3BSREDP WARN [clj-backend.db.connections:40] - 已为数据库连接 project_17 应用性能优化配置
25-09-02 09:58:35:390 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - 创建 project_17 项目库连接
25-09-02 11:38:15:437 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/host/instance 
 - 参数: 
 {:Hosts [{:HostId "2d06ec3a-9ecd-422c-a76a-1afc16b47b24", :HostName "1", :ConfigId nil, :StationHardware nil} {:HostId "da28d67a-f0fc-46a6-ab37-d412e6ab9f26", :HostName "2", :ConfigId nil, :StationHardware nil}]} 
 =============================================================

25-09-02 11:38:15:494 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/hardware 
 - 参数: 
 {:HardwareDtos {"hardware_simulator-0000-hw-ccss-id-0000" {:DaChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :AdChannels [{:Sensors ["传感器0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0}], :CreepAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 10, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 11, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 12, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 13, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 14, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 15, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 16, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 17, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 18, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 19, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}], :HandBoxChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil}], :Devices ({:Hwkey "hardware_simulator", :SubId 0, :DeviceId "33d3a761-f180-4c0a-9135-4dbf045b96e0"}), :ServoAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 7, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0}], :UpDirectValue 5, :TensileUpOrDown 7, :PressUpOrDown 6} {:Hwkey "hardware_simulator", :RealIndex 3, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0}], :UpDirectValue 5, :TensileUpOrDown 5, :PressUpOrDown 5}], :InputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :OutputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :TempAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 10, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}]}}} 
 =============================================================

25-09-02 11:38:15:532 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/api/appserver/config/info 
 - 参数: 
 {:AppServerIp "*************", :AppServerPort 3000, :ComputerId "MP2QAVV0", :AppServerApiPort 3000} 
 =============================================================

25-09-02 11:38:15:577 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 登录"}
25-09-02 11:40:27:755 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:784] - [硬件刷新] 开始刷新硬件列表
25-09-02 11:40:27:758 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:787] - [硬件刷新] Phase 1: 开始全局标记硬件删除
25-09-02 11:40:27:770 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:789] - [硬件刷新] Phase 1: 完成全局标记硬件删除
25-09-02 11:40:27:772 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:792] - [硬件刷新] Phase 2: 开始从源端刷新硬件信息
25-09-02 11:40:27:819 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:800] - [硬件刷新] 端口 5000 解析后的hw-key: hardware_simulator
25-09-02 11:40:27:923 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:805] - [硬件刷新] Phase 2: 完成从源端刷新硬件信息
25-09-02 11:40:27:926 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:808] - [硬件刷新] 刷新硬件列表完成
25-09-02 11:40:36:651 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 保存了硬件"}
25-09-02 11:40:41:671 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:784] - [硬件刷新] 开始刷新硬件列表
25-09-02 11:40:41:672 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:787] - [硬件刷新] Phase 1: 开始全局标记硬件删除
25-09-02 11:40:41:677 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:789] - [硬件刷新] Phase 1: 完成全局标记硬件删除
25-09-02 11:40:41:677 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:792] - [硬件刷新] Phase 2: 开始从源端刷新硬件信息
25-09-02 11:40:41:698 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:800] - [硬件刷新] 端口 5000 解析后的hw-key: hardware_simulator
25-09-02 11:40:41:731 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:805] - [硬件刷新] Phase 2: 完成从源端刷新硬件信息
25-09-02 11:40:41:732 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:808] - [硬件刷新] 刷新硬件列表完成
25-09-02 11:41:09:600 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 保存了硬件"}
25-09-02 11:41:10:908 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:784] - [硬件刷新] 开始刷新硬件列表
25-09-02 11:41:10:909 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:787] - [硬件刷新] Phase 1: 开始全局标记硬件删除
25-09-02 11:41:10:913 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:789] - [硬件刷新] Phase 1: 完成全局标记硬件删除
25-09-02 11:41:10:914 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:792] - [硬件刷新] Phase 2: 开始从源端刷新硬件信息
25-09-02 11:41:10:936 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:800] - [硬件刷新] 端口 5000 解析后的hw-key: hardware_simulator
25-09-02 11:41:10:966 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:805] - [硬件刷新] Phase 2: 完成从源端刷新硬件信息
25-09-02 11:41:10:966 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:808] - [硬件刷新] 刷新硬件列表完成
25-09-02 11:41:19:882 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 保存了硬件"}
25-09-02 11:41:21:216 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:784] - [硬件刷新] 开始刷新硬件列表
25-09-02 11:41:21:216 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:787] - [硬件刷新] Phase 1: 开始全局标记硬件删除
25-09-02 11:41:21:221 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:789] - [硬件刷新] Phase 1: 完成全局标记硬件删除
25-09-02 11:41:21:222 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:792] - [硬件刷新] Phase 2: 开始从源端刷新硬件信息
25-09-02 11:41:21:241 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:800] - [硬件刷新] 端口 5000 解析后的hw-key: hardware_simulator
25-09-02 11:41:21:267 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:805] - [硬件刷新] Phase 2: 完成从源端刷新硬件信息
25-09-02 11:41:21:268 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:808] - [硬件刷新] 刷新硬件列表完成
25-09-02 11:41:58:730 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 保存了硬件"}
25-09-02 11:42:00:484 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:784] - [硬件刷新] 开始刷新硬件列表
25-09-02 11:42:00:486 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:787] - [硬件刷新] Phase 1: 开始全局标记硬件删除
25-09-02 11:42:00:491 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:789] - [硬件刷新] Phase 1: 完成全局标记硬件删除
25-09-02 11:42:00:492 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:792] - [硬件刷新] Phase 2: 开始从源端刷新硬件信息
25-09-02 11:42:00:516 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:800] - [硬件刷新] 端口 5000 解析后的hw-key: hardware_simulator
25-09-02 11:42:00:559 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:805] - [硬件刷新] Phase 2: 完成从源端刷新硬件信息
25-09-02 11:42:00:560 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:808] - [硬件刷新] 刷新硬件列表完成
25-09-02 11:42:08:369 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 保存了硬件"}
25-09-02 11:42:09:511 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:784] - [硬件刷新] 开始刷新硬件列表
25-09-02 11:42:09:512 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:787] - [硬件刷新] Phase 1: 开始全局标记硬件删除
25-09-02 11:42:09:517 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:789] - [硬件刷新] Phase 1: 完成全局标记硬件删除
25-09-02 11:42:09:518 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:792] - [硬件刷新] Phase 2: 开始从源端刷新硬件信息
25-09-02 11:42:09:540 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:800] - [硬件刷新] 端口 5000 解析后的hw-key: hardware_simulator
25-09-02 11:42:09:568 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:805] - [硬件刷新] Phase 2: 完成从源端刷新硬件信息
25-09-02 11:42:09:571 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:808] - [硬件刷新] 刷新硬件列表完成
25-09-02 11:42:22:802 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 保存了硬件"}
25-09-02 11:42:24:146 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:784] - [硬件刷新] 开始刷新硬件列表
25-09-02 11:42:24:148 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:787] - [硬件刷新] Phase 1: 开始全局标记硬件删除
25-09-02 11:42:24:151 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:789] - [硬件刷新] Phase 1: 完成全局标记硬件删除
25-09-02 11:42:24:152 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:792] - [硬件刷新] Phase 2: 开始从源端刷新硬件信息
25-09-02 11:42:24:170 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:800] - [硬件刷新] 端口 5000 解析后的hw-key: hardware_simulator
25-09-02 11:42:24:217 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:805] - [硬件刷新] Phase 2: 完成从源端刷新硬件信息
25-09-02 11:42:24:218 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:808] - [硬件刷新] 刷新硬件列表完成
25-09-02 11:42:33:497 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 保存了硬件"}
25-09-02 11:42:34:454 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:784] - [硬件刷新] 开始刷新硬件列表
25-09-02 11:42:34:456 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:787] - [硬件刷新] Phase 1: 开始全局标记硬件删除
25-09-02 11:42:34:460 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:789] - [硬件刷新] Phase 1: 完成全局标记硬件删除
25-09-02 11:42:34:460 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:792] - [硬件刷新] Phase 2: 开始从源端刷新硬件信息
25-09-02 11:42:34:482 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:800] - [硬件刷新] 端口 5000 解析后的hw-key: hardware_simulator
25-09-02 11:42:34:511 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:805] - [硬件刷新] Phase 2: 完成从源端刷新硬件信息
25-09-02 11:42:34:511 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:808] - [硬件刷新] 刷新硬件列表完成
25-09-02 11:43:46:123 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 保存了硬件"}
25-09-02 11:43:56:343 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 保存了硬件"}
25-09-02 11:44:21:007 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:784] - [硬件刷新] 开始刷新硬件列表
25-09-02 11:44:21:008 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:787] - [硬件刷新] Phase 1: 开始全局标记硬件删除
25-09-02 11:44:21:013 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:789] - [硬件刷新] Phase 1: 完成全局标记硬件删除
25-09-02 11:44:21:014 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:792] - [硬件刷新] Phase 2: 开始从源端刷新硬件信息
25-09-02 11:44:21:036 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:800] - [硬件刷新] 端口 5000 解析后的hw-key: hardware_simulator
25-09-02 11:44:21:059 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:805] - [硬件刷新] Phase 2: 完成从源端刷新硬件信息
25-09-02 11:44:21:060 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:808] - [硬件刷新] 刷新硬件列表完成
25-09-02 11:44:30:669 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 保存了硬件"}
25-09-02 11:45:14:876 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 保存了硬件"}
25-09-02 11:45:19:429 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:784] - [硬件刷新] 开始刷新硬件列表
25-09-02 11:45:19:430 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:787] - [硬件刷新] Phase 1: 开始全局标记硬件删除
25-09-02 11:45:19:434 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:789] - [硬件刷新] Phase 1: 完成全局标记硬件删除
25-09-02 11:45:19:436 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:792] - [硬件刷新] Phase 2: 开始从源端刷新硬件信息
25-09-02 11:45:19:462 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:800] - [硬件刷新] 端口 5000 解析后的hw-key: hardware_simulator
25-09-02 11:45:19:516 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:805] - [硬件刷新] Phase 2: 完成从源端刷新硬件信息
25-09-02 11:45:19:517 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:808] - [硬件刷新] 刷新硬件列表完成
25-09-02 11:45:31:552 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 保存了硬件"}
25-09-02 11:45:33:319 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:784] - [硬件刷新] 开始刷新硬件列表
25-09-02 11:45:33:320 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:787] - [硬件刷新] Phase 1: 开始全局标记硬件删除
25-09-02 11:45:33:326 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:789] - [硬件刷新] Phase 1: 完成全局标记硬件删除
25-09-02 11:45:33:327 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:792] - [硬件刷新] Phase 2: 开始从源端刷新硬件信息
25-09-02 11:45:33:344 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:800] - [硬件刷新] 端口 5000 解析后的hw-key: hardware_simulator
25-09-02 11:45:33:367 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:805] - [硬件刷新] Phase 2: 完成从源端刷新硬件信息
25-09-02 11:45:33:368 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:808] - [硬件刷新] 刷新硬件列表完成
25-09-02 11:45:53:073 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 保存了硬件"}
25-09-02 11:45:54:444 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:784] - [硬件刷新] 开始刷新硬件列表
25-09-02 11:45:54:445 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:787] - [硬件刷新] Phase 1: 开始全局标记硬件删除
25-09-02 11:45:54:449 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:789] - [硬件刷新] Phase 1: 完成全局标记硬件删除
25-09-02 11:45:54:449 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:792] - [硬件刷新] Phase 2: 开始从源端刷新硬件信息
25-09-02 11:45:54:468 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:800] - [硬件刷新] 端口 5000 解析后的hw-key: hardware_simulator
25-09-02 11:45:54:493 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:805] - [硬件刷新] Phase 2: 完成从源端刷新硬件信息
25-09-02 11:45:54:494 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:808] - [硬件刷新] 刷新硬件列表完成
25-09-02 11:46:05:977 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 保存了硬件"}
25-09-02 11:46:06:568 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:784] - [硬件刷新] 开始刷新硬件列表
25-09-02 11:46:06:569 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:787] - [硬件刷新] Phase 1: 开始全局标记硬件删除
25-09-02 11:46:06:573 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:789] - [硬件刷新] Phase 1: 完成全局标记硬件删除
25-09-02 11:46:06:574 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:792] - [硬件刷新] Phase 2: 开始从源端刷新硬件信息
25-09-02 11:46:06:595 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:800] - [硬件刷新] 端口 5000 解析后的hw-key: hardware_simulator
25-09-02 11:46:06:618 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:805] - [硬件刷新] Phase 2: 完成从源端刷新硬件信息
25-09-02 11:46:06:619 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:808] - [硬件刷新] 刷新硬件列表完成
25-09-02 11:46:19:383 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 保存了硬件"}
25-09-02 11:46:23:350 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 保存了硬件"}
25-09-02 11:46:36:295 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 保存了硬件"}
25-09-02 11:47:47:982 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 保存了硬件"}
25-09-02 11:49:05:289 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:784] - [硬件刷新] 开始刷新硬件列表
25-09-02 11:49:05:291 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:787] - [硬件刷新] Phase 1: 开始全局标记硬件删除
25-09-02 11:49:05:296 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:789] - [硬件刷新] Phase 1: 完成全局标记硬件删除
25-09-02 11:49:05:297 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:792] - [硬件刷新] Phase 2: 开始从源端刷新硬件信息
25-09-02 11:49:05:316 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:800] - [硬件刷新] 端口 5000 解析后的hw-key: hardware_simulator
25-09-02 11:49:05:363 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:805] - [硬件刷新] Phase 2: 完成从源端刷新硬件信息
25-09-02 11:49:05:364 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:808] - [硬件刷新] 刷新硬件列表完成
25-09-02 11:49:23:597 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 保存了硬件"}
25-09-02 11:49:25:287 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:784] - [硬件刷新] 开始刷新硬件列表
25-09-02 11:49:25:288 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:787] - [硬件刷新] Phase 1: 开始全局标记硬件删除
25-09-02 11:49:25:292 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:789] - [硬件刷新] Phase 1: 完成全局标记硬件删除
25-09-02 11:49:25:294 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:792] - [硬件刷新] Phase 2: 开始从源端刷新硬件信息
25-09-02 11:49:25:315 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:800] - [硬件刷新] 端口 5000 解析后的hw-key: hardware_simulator
25-09-02 11:49:25:339 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:805] - [硬件刷新] Phase 2: 完成从源端刷新硬件信息
25-09-02 11:49:25:340 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:808] - [硬件刷新] 刷新硬件列表完成
25-09-02 11:49:56:544 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 保存了硬件"}
25-09-02 11:50:25:491 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:784] - [硬件刷新] 开始刷新硬件列表
25-09-02 11:50:25:493 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:787] - [硬件刷新] Phase 1: 开始全局标记硬件删除
25-09-02 11:50:25:498 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:789] - [硬件刷新] Phase 1: 完成全局标记硬件删除
25-09-02 11:50:25:498 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:792] - [硬件刷新] Phase 2: 开始从源端刷新硬件信息
25-09-02 11:50:25:517 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:800] - [硬件刷新] 端口 5000 解析后的hw-key: hardware_simulator
25-09-02 11:50:25:539 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:805] - [硬件刷新] Phase 2: 完成从源端刷新硬件信息
25-09-02 11:50:25:540 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:808] - [硬件刷新] 刷新硬件列表完成
25-09-02 11:50:32:646 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 保存了硬件"}
25-09-02 11:50:33:716 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:784] - [硬件刷新] 开始刷新硬件列表
25-09-02 11:50:33:717 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:787] - [硬件刷新] Phase 1: 开始全局标记硬件删除
25-09-02 11:50:33:723 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:789] - [硬件刷新] Phase 1: 完成全局标记硬件删除
25-09-02 11:50:33:724 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:792] - [硬件刷新] Phase 2: 开始从源端刷新硬件信息
25-09-02 11:50:33:743 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:800] - [硬件刷新] 端口 5000 解析后的hw-key: hardware_simulator
25-09-02 11:50:33:770 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:805] - [硬件刷新] Phase 2: 完成从源端刷新硬件信息
25-09-02 11:50:33:771 DESKTOP-3BSREDP INFO [clj-backend.modules.hardware.service:808] - [硬件刷新] 刷新硬件列表完成
25-09-02 14:04:03:928 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/host/instance 
 - 参数: 
 {:Hosts [{:HostId "2d06ec3a-9ecd-422c-a76a-1afc16b47b24", :HostName "1", :ConfigId nil, :StationHardware nil} {:HostId "da28d67a-f0fc-46a6-ab37-d412e6ab9f26", :HostName "2", :ConfigId nil, :StationHardware nil}]} 
 =============================================================

25-09-02 14:04:03:993 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/hardware 
 - 参数: 
 {:HardwareDtos {"hardware_simulator-0000-hw-ccss-id-0000" {:DaChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :AdChannels [{:Sensors ["传感器0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0}], :CreepAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 10, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 11, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 12, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 13, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 14, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 15, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 16, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 17, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 18, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 19, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}], :HandBoxChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil}], :Devices ({:Hwkey "hardware_simulator", :SubId 0, :DeviceId "9cdeaaab-9005-4fb9-b11a-279864d5c0cd"}), :ServoAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 7, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0}], :UpDirectValue 5, :TensileUpOrDown 7, :PressUpOrDown 6} {:Hwkey "hardware_simulator", :RealIndex 3, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0}], :UpDirectValue 5, :TensileUpOrDown 5, :PressUpOrDown 5}], :InputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :OutputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :TempAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 10, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}]}}} 
 =============================================================

25-09-02 14:04:04:034 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/api/appserver/config/info 
 - 参数: 
 {:AppServerIp "*************", :AppServerPort 3000, :ComputerId "MP2QAVV0", :AppServerApiPort 3000} 
 =============================================================

25-09-02 14:04:04:080 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 登录"}
25-09-02 14:04:04:634 DESKTOP-3BSREDP INFO [clj-backend.modules.standby.service:160] - 运行 sync-to-share-http (HTTP POST)...
25-09-02 14:04:04:637 DESKTOP-3BSREDP WARN [clj-backend.modules.standby.service:170] - 从节点 IP 或端口未配置. 跳过 sync-to-share-http. 
25-09-02 14:04:08:999 DESKTOP-3BSREDP WARN [clj-backend.db.connections:40] - 已为数据库连接 project_27 应用性能优化配置
25-09-02 14:04:09:000 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - 创建 project_27 项目库连接
25-09-02 14:04:09:166 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "打开项目 project_27"}
25-09-02 14:04:09:196 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:861] - 模板开始实例化
25-09-02 14:04:10:739 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "5cf045ed-c181-4731-b486-a8b7fd9e0883", :code 0, :msg "模板生成成功"}
25-09-02 14:04:10:740 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-5cf045ed-c181-4731-b486-a8b7fd9e0883 中添加消息 {:ProcessId "5cf045ed-c181-4731-b486-a8b7fd9e0883", :code 0, :msg "模板生成成功"}
25-09-02 14:04:10:742 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "5cf045ed-c181-4731-b486-a8b7fd9e0883", :code 0, :msg "模板生成成功"}
25-09-02 14:04:11:866 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/sample-inst/select 
 - 参数: 
 {:ClassName "project_27", :CurrentInstCode "sample_14785d372", :SelectedInstCodes []} 
 =============================================================

25-09-02 14:04:29:927 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【打开项目默认执行动作】"}
25-09-02 14:04:29:930 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "running"} 
 =============================================================

25-09-02 14:04:30:371 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【打开项目默认执行动作】"}
25-09-02 14:04:30:373 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "finished"} 
 =============================================================

25-09-02 16:59:47:209 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【关闭项目时默认执行动作】"}
25-09-02 16:59:47:212 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "running"} 
 =============================================================

25-09-02 16:59:47:253 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【关闭项目重置参数】"}
25-09-02 16:59:47:255 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "running"} 
 =============================================================

25-09-02 16:59:47:290 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-common-service:85] - 项目关闭执行动作---完成
25-09-02 16:59:47:291 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "删除了【关闭项目 project_27】项目"}
25-09-02 16:59:47:295 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【打开项目默认执行动作】"}
25-09-02 16:59:47:300 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【关闭项目时默认执行动作】"}
25-09-02 16:59:47:301 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "finished"} 
 =============================================================

25-09-02 16:59:47:377 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【关闭项目重置参数】"}
25-09-02 16:59:47:378 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "finished"} 
 =============================================================

25-09-02 16:59:47:453 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:632] - Handling :finalize-project-quit-save event for project {:event :finalize-project-quit-save, :project_id 27, :user_id 1}
25-09-02 16:59:47:574 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 项目数据库 数据库缓存
25-09-02 16:59:47:684 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 系统数据库 数据库缓存
25-09-02 16:59:47:685 DESKTOP-3BSREDP WARN [clj-backend.modules.project.project-service:416] - 项目缓存刷新失败，但继续执行保存操作: {:success false, :message "部分缓存刷新失败", :details {:project true, :project-data false, :system true}}
25-09-02 16:59:47:687 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "保存了项目"}
25-09-02 16:59:47:752 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 D:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1756707875343_project_27_copy.db 成功
25-09-02 16:59:47:776 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:639] - Project event listener started.
25-09-02 16:59:49:136 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 D:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1756707875343_project_27.db 成功
25-09-02 16:59:49:211 DESKTOP-3BSREDP WARN [clj-backend.db.connections:40] - 已为数据库连接 project_27 应用性能优化配置
25-09-02 16:59:49:212 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - 创建 project_27 项目库连接
25-09-02 17:00:24:662 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/host/instance 
 - 参数: 
 {:Hosts [{:HostId "2d06ec3a-9ecd-422c-a76a-1afc16b47b24", :HostName "1", :ConfigId nil, :StationHardware nil} {:HostId "da28d67a-f0fc-46a6-ab37-d412e6ab9f26", :HostName "2", :ConfigId nil, :StationHardware nil}]} 
 =============================================================

25-09-02 17:00:24:687 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:level "error", :content "调用外部接口出错"}
25-09-02 17:00:24:691 DESKTOP-3BSREDP ERROR [clj-backend.common.biz-error:48] - 发生系统错误. 
错误码: 10001 
错误描述: 调用外部接口出错 
错误数据: #error {
 :cause "Connection refused: connect"
 :via
 [{:type java.net.ConnectException
   :message "Connection refused: connect"
   :at [sun.nio.ch.Net connect0 "Net.java" -2]}]
 :trace
 [[sun.nio.ch.Net connect0 "Net.java" -2]
  [sun.nio.ch.Net connect "Net.java" 579]
  [sun.nio.ch.Net connect "Net.java" 568]
  [sun.nio.ch.NioSocketImpl connect "NioSocketImpl.java" 593]
  [java.net.SocksSocketImpl connect "SocksSocketImpl.java" 327]
  [java.net.Socket connect "Socket.java" 633]
  [org.apache.http.conn.scheme.PlainSocketFactory connectSocket "PlainSocketFactory.java" 120]
  [org.apache.http.impl.conn.DefaultClientConnectionOperator openConnection "DefaultClientConnectionOperator.java" 179]
  [org.apache.http.impl.conn.ManagedClientConnectionImpl open "ManagedClientConnectionImpl.java" 328]
  [org.apache.http.impl.client.DefaultRequestDirector tryConnect "DefaultRequestDirector.java" 612]
  [org.apache.http.impl.client.DefaultRequestDirector execute "DefaultRequestDirector.java" 447]
  [org.apache.http.impl.client.AbstractHttpClient doExecute "AbstractHttpClient.java" 884]
  [org.apache.http.impl.client.CloseableHttpClient execute "CloseableHttpClient.java" 82]
  [org.apache.http.impl.client.CloseableHttpClient execute "CloseableHttpClient.java" 107]
  [clj_http.core$request invokeStatic "core.clj" 304]
  [clj_http.core$request invoke "core.clj" 208]
  [clojure.lang.Var invoke "Var.java" 386]
  [clj_http.client$wrap_request_timing$fn__23258 invoke "client.clj" 835]
  [clj_http.headers$wrap_header_map$fn__22290 invoke "headers.clj" 143]
  [clj_http.client$wrap_query_params$fn__23155 invoke "client.clj" 661]
  [clj_http.client$wrap_basic_auth$fn__23162 invoke "client.clj" 677]
  [clj_http.client$wrap_oauth$fn__23166 invoke "client.clj" 687]
  [clj_http.client$wrap_user_info$fn__23171 invoke "client.clj" 700]
  [clj_http.client$wrap_url$fn__23244 invoke "client.clj" 801]
  [clj_http.client$wrap_redirects$fn__22931 invoke "client.clj" 267]
  [clj_http.client$wrap_decompression$fn__22956 invoke "client.clj" 339]
  [clj_http.client$wrap_input_coercion$fn__23088 invoke "client.clj" 490]
  [clj_http.client$wrap_additional_header_parsing$fn__23109 invoke "client.clj" 552]
  [clj_http.client$wrap_output_coercion$fn__23079 invoke "client.clj" 468]
  [clj_http.client$wrap_exceptions$fn__22917 invoke "client.clj" 219]
  [clj_http.client$wrap_accept$fn__23123 invoke "client.clj" 592]
  [clj_http.client$wrap_accept_encoding$fn__23129 invoke "client.clj" 609]
  [clj_http.client$wrap_content_type$fn__23118 invoke "client.clj" 584]
  [clj_http.client$wrap_form_params$fn__23218 invoke "client.clj" 761]
  [clj_http.client$wrap_nested_params$fn__23239 invoke "client.clj" 794]
  [clj_http.client$wrap_method$fn__23178 invoke "client.clj" 707]
  [clj_http.cookies$wrap_cookies$fn__21212 invoke "cookies.clj" 124]
  [clj_http.links$wrap_links$fn__22553 invoke "links.clj" 51]
  [clj_http.client$wrap_unknown_host$fn__23248 invoke "client.clj" 810]
  [clj_http.client$post invokeStatic "client.clj" 925]
  [clj_http.client$post doInvoke "client.clj" 921]
  [clojure.lang.RestFn invoke "RestFn.java" 426]
  [clj_backend.common.http_client$post invokeStatic "http_client.clj" 29]
  [clj_backend.common.http_client$post invoke "http_client.clj" 22]
  [clj_backend.common.http_client$post invokeStatic "http_client.clj" 25]
  [clj_backend.common.http_client$post invoke "http_client.clj" 22]
  [clj_backend.modules.hardware.station.service$host_instance_handle invokeStatic "service.clj" 536]
  [clj_backend.modules.hardware.station.service$host_instance_handle invoke "service.clj" 522]
  [clj_backend.modules.sys.user.sys_user_service$login invokeStatic "sys_user_service.clj" 96]
  [clj_backend.modules.sys.user.sys_user_service$login invoke "sys_user_service.clj" 91]
  [clj_backend.modules.sys.user.sys_user_routes$routes$fn__43149 invoke "sys_user_routes.clj" 56]
  [clj_backend.common.trial$trial_middleware$fn__42698 invoke "trial.clj" 73]
  [reitit.ring.coercion$fn__52552$fn__52554$fn__52555 invoke "coercion.cljc" 40]
  [reitit.ring.coercion$fn__52575$fn__52577$fn__52578 invoke "coercion.cljc" 80]
  [reitit.ring.middleware.exception$wrap$fn__49531$fn__49532 invoke "exception.clj" 49]
  [clj_backend.middleware.logger$logger_middleware$fn__52770 invoke "logger.clj" 18]
  [muuntaja.middleware$wrap_format_request$fn__52693 invoke "middleware.clj" 114]
  [muuntaja.middleware$wrap_format_response$fn__52697 invoke "middleware.clj" 132]
  [muuntaja.middleware$wrap_format_negotiate$fn__52690 invoke "middleware.clj" 96]
  [ring.middleware.params$wrap_params$fn__18764 invoke "params.clj" 67]
  [reitit.ring$ring_handler$fn__12807 invoke "ring.cljc" 329]
  [clojure.lang.AFn applyToHelper "AFn.java" 154]
  [clojure.lang.AFn applyTo "AFn.java" 144]
  [clojure.lang.AFunction$1 doInvoke "AFunction.java" 33]
  [clojure.lang.RestFn invoke "RestFn.java" 411]
  [clojure.lang.Var invoke "Var.java" 386]
  [ring.middleware.reload$wrap_reload$fn__16585 invoke "reload.clj" 39]
  [selmer.middleware$wrap_error_page$fn__16600 invoke "middleware.clj" 18]
  [prone.middleware$wrap_exceptions$fn__16842 invoke "middleware.clj" 169]
  [ring.middleware.flash$wrap_flash$fn__16963 invoke "flash.clj" 39]
  [ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290 invoke "session.clj" 88]
  [ring.middleware.cors$handle_cors invokeStatic "cors.cljc" 175]
  [ring.middleware.cors$handle_cors invoke "cors.cljc" 167]
  [ring.middleware.cors$wrap_cors$fn__16942 invoke "cors.cljc" 205]
  [ring.middleware.keyword_params$wrap_keyword_params$fn__18384 invoke "keyword_params.clj" 53]
  [ring.middleware.nested_params$wrap_nested_params$fn__18442 invoke "nested_params.clj" 89]
  [ring.middleware.multipart_params$wrap_multipart_params$fn__18740 invoke "multipart_params.clj" 173]
  [ring.middleware.params$wrap_params$fn__18764 invoke "params.clj" 67]
  [ring.middleware.cookies$wrap_cookies$fn__18059 invoke "cookies.clj" 175]
  [ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935 invoke "absolute_redirects.clj" 47]
  [ring.middleware.resource$wrap_resource_prefer_resources$fn__18800 invoke "resource.clj" 25]
  [ring.middleware.content_type$wrap_content_type$fn__18883 invoke "content_type.clj" 34]
  [ring.middleware.default_charset$wrap_default_charset$fn__18907 invoke "default_charset.clj" 31]
  [ring.middleware.not_modified$wrap_not_modified$fn__18864 invoke "not_modified.clj" 61]
  [ring.middleware.x_headers$wrap_x_header$fn__18324 invoke "x_headers.clj" 22]
  [ring.middleware.x_headers$wrap_x_header$fn__18324 invoke "x_headers.clj" 22]
  [ring.middleware.x_headers$wrap_x_header$fn__18324 invoke "x_headers.clj" 22]
  [ring.adapter.undertow$undertow_handler$fn$reify__58450 handleRequest "undertow.clj" 40]
  [io.undertow.server.session.SessionAttachmentHandler handleRequest "SessionAttachmentHandler.java" 68]
  [io.undertow.server.Connectors executeRootHandler "Connectors.java" 387]
  [io.undertow.server.HttpServerExchange$1 run "HttpServerExchange.java" 852]
  [org.jboss.threads.ContextClassLoaderSavingRunnable run "ContextClassLoaderSavingRunnable.java" 35]
  [org.jboss.threads.EnhancedQueueExecutor safeRun "EnhancedQueueExecutor.java" 2019]
  [org.jboss.threads.EnhancedQueueExecutor$ThreadBody doRunTask "EnhancedQueueExecutor.java" 1558]
  [org.jboss.threads.EnhancedQueueExecutor$ThreadBody run "EnhancedQueueExecutor.java" 1449]
  [org.xnio.XnioWorker$WorkerThreadFactory$1$1 run "XnioWorker.java" 1282]
  [java.lang.Thread run "Thread.java" 840]]}
25-09-02 17:00:25:975 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/host/instance 
 - 参数: 
 {:Hosts [{:HostId "2d06ec3a-9ecd-422c-a76a-1afc16b47b24", :HostName "1", :ConfigId nil, :StationHardware nil} {:HostId "da28d67a-f0fc-46a6-ab37-d412e6ab9f26", :HostName "2", :ConfigId nil, :StationHardware nil}]} 
 =============================================================

25-09-02 17:00:25:998 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:level "error", :content "调用外部接口出错"}
25-09-02 17:00:26:000 DESKTOP-3BSREDP ERROR [clj-backend.common.biz-error:48] - 发生系统错误. 
错误码: 10001 
错误描述: 调用外部接口出错 
错误数据: #error {
 :cause "Connection refused: connect"
 :via
 [{:type java.net.ConnectException
   :message "Connection refused: connect"
   :at [sun.nio.ch.Net connect0 "Net.java" -2]}]
 :trace
 [[sun.nio.ch.Net connect0 "Net.java" -2]
  [sun.nio.ch.Net connect "Net.java" 579]
  [sun.nio.ch.Net connect "Net.java" 568]
  [sun.nio.ch.NioSocketImpl connect "NioSocketImpl.java" 593]
  [java.net.SocksSocketImpl connect "SocksSocketImpl.java" 327]
  [java.net.Socket connect "Socket.java" 633]
  [org.apache.http.conn.scheme.PlainSocketFactory connectSocket "PlainSocketFactory.java" 120]
  [org.apache.http.impl.conn.DefaultClientConnectionOperator openConnection "DefaultClientConnectionOperator.java" 179]
  [org.apache.http.impl.conn.ManagedClientConnectionImpl open "ManagedClientConnectionImpl.java" 328]
  [org.apache.http.impl.client.DefaultRequestDirector tryConnect "DefaultRequestDirector.java" 612]
  [org.apache.http.impl.client.DefaultRequestDirector execute "DefaultRequestDirector.java" 447]
  [org.apache.http.impl.client.AbstractHttpClient doExecute "AbstractHttpClient.java" 884]
  [org.apache.http.impl.client.CloseableHttpClient execute "CloseableHttpClient.java" 82]
  [org.apache.http.impl.client.CloseableHttpClient execute "CloseableHttpClient.java" 107]
  [clj_http.core$request invokeStatic "core.clj" 304]
  [clj_http.core$request invoke "core.clj" 208]
  [clojure.lang.Var invoke "Var.java" 386]
  [clj_http.client$wrap_request_timing$fn__23258 invoke "client.clj" 835]
  [clj_http.headers$wrap_header_map$fn__22290 invoke "headers.clj" 143]
  [clj_http.client$wrap_query_params$fn__23155 invoke "client.clj" 661]
  [clj_http.client$wrap_basic_auth$fn__23162 invoke "client.clj" 677]
  [clj_http.client$wrap_oauth$fn__23166 invoke "client.clj" 687]
  [clj_http.client$wrap_user_info$fn__23171 invoke "client.clj" 700]
  [clj_http.client$wrap_url$fn__23244 invoke "client.clj" 801]
  [clj_http.client$wrap_redirects$fn__22931 invoke "client.clj" 267]
  [clj_http.client$wrap_decompression$fn__22956 invoke "client.clj" 339]
  [clj_http.client$wrap_input_coercion$fn__23088 invoke "client.clj" 490]
  [clj_http.client$wrap_additional_header_parsing$fn__23109 invoke "client.clj" 552]
  [clj_http.client$wrap_output_coercion$fn__23079 invoke "client.clj" 468]
  [clj_http.client$wrap_exceptions$fn__22917 invoke "client.clj" 219]
  [clj_http.client$wrap_accept$fn__23123 invoke "client.clj" 592]
  [clj_http.client$wrap_accept_encoding$fn__23129 invoke "client.clj" 609]
  [clj_http.client$wrap_content_type$fn__23118 invoke "client.clj" 584]
  [clj_http.client$wrap_form_params$fn__23218 invoke "client.clj" 761]
  [clj_http.client$wrap_nested_params$fn__23239 invoke "client.clj" 794]
  [clj_http.client$wrap_method$fn__23178 invoke "client.clj" 707]
  [clj_http.cookies$wrap_cookies$fn__21212 invoke "cookies.clj" 124]
  [clj_http.links$wrap_links$fn__22553 invoke "links.clj" 51]
  [clj_http.client$wrap_unknown_host$fn__23248 invoke "client.clj" 810]
  [clj_http.client$post invokeStatic "client.clj" 925]
  [clj_http.client$post doInvoke "client.clj" 921]
  [clojure.lang.RestFn invoke "RestFn.java" 426]
  [clj_backend.common.http_client$post invokeStatic "http_client.clj" 29]
  [clj_backend.common.http_client$post invoke "http_client.clj" 22]
  [clj_backend.common.http_client$post invokeStatic "http_client.clj" 25]
  [clj_backend.common.http_client$post invoke "http_client.clj" 22]
  [clj_backend.modules.hardware.station.service$host_instance_handle invokeStatic "service.clj" 536]
  [clj_backend.modules.hardware.station.service$host_instance_handle invoke "service.clj" 522]
  [clj_backend.modules.sys.user.sys_user_service$login invokeStatic "sys_user_service.clj" 96]
  [clj_backend.modules.sys.user.sys_user_service$login invoke "sys_user_service.clj" 91]
  [clj_backend.modules.sys.user.sys_user_routes$routes$fn__43149 invoke "sys_user_routes.clj" 56]
  [clj_backend.common.trial$trial_middleware$fn__42698 invoke "trial.clj" 73]
  [reitit.ring.coercion$fn__52552$fn__52554$fn__52555 invoke "coercion.cljc" 40]
  [reitit.ring.coercion$fn__52575$fn__52577$fn__52578 invoke "coercion.cljc" 80]
  [reitit.ring.middleware.exception$wrap$fn__49531$fn__49532 invoke "exception.clj" 49]
  [clj_backend.middleware.logger$logger_middleware$fn__52770 invoke "logger.clj" 18]
  [muuntaja.middleware$wrap_format_request$fn__52693 invoke "middleware.clj" 114]
  [muuntaja.middleware$wrap_format_response$fn__52697 invoke "middleware.clj" 132]
  [muuntaja.middleware$wrap_format_negotiate$fn__52690 invoke "middleware.clj" 96]
  [ring.middleware.params$wrap_params$fn__18764 invoke "params.clj" 67]
  [reitit.ring$ring_handler$fn__12807 invoke "ring.cljc" 329]
  [clojure.lang.AFn applyToHelper "AFn.java" 154]
  [clojure.lang.AFn applyTo "AFn.java" 144]
  [clojure.lang.AFunction$1 doInvoke "AFunction.java" 33]
  [clojure.lang.RestFn invoke "RestFn.java" 411]
  [clojure.lang.Var invoke "Var.java" 386]
  [ring.middleware.reload$wrap_reload$fn__16585 invoke "reload.clj" 39]
  [selmer.middleware$wrap_error_page$fn__16600 invoke "middleware.clj" 18]
  [prone.middleware$wrap_exceptions$fn__16842 invoke "middleware.clj" 169]
  [ring.middleware.flash$wrap_flash$fn__16963 invoke "flash.clj" 39]
  [ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290 invoke "session.clj" 88]
  [ring.middleware.cors$handle_cors invokeStatic "cors.cljc" 175]
  [ring.middleware.cors$handle_cors invoke "cors.cljc" 167]
  [ring.middleware.cors$wrap_cors$fn__16942 invoke "cors.cljc" 205]
  [ring.middleware.keyword_params$wrap_keyword_params$fn__18384 invoke "keyword_params.clj" 53]
  [ring.middleware.nested_params$wrap_nested_params$fn__18442 invoke "nested_params.clj" 89]
  [ring.middleware.multipart_params$wrap_multipart_params$fn__18740 invoke "multipart_params.clj" 173]
  [ring.middleware.params$wrap_params$fn__18764 invoke "params.clj" 67]
  [ring.middleware.cookies$wrap_cookies$fn__18059 invoke "cookies.clj" 175]
  [ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935 invoke "absolute_redirects.clj" 47]
  [ring.middleware.resource$wrap_resource_prefer_resources$fn__18800 invoke "resource.clj" 25]
  [ring.middleware.content_type$wrap_content_type$fn__18883 invoke "content_type.clj" 34]
  [ring.middleware.default_charset$wrap_default_charset$fn__18907 invoke "default_charset.clj" 31]
  [ring.middleware.not_modified$wrap_not_modified$fn__18864 invoke "not_modified.clj" 61]
  [ring.middleware.x_headers$wrap_x_header$fn__18324 invoke "x_headers.clj" 22]
  [ring.middleware.x_headers$wrap_x_header$fn__18324 invoke "x_headers.clj" 22]
  [ring.middleware.x_headers$wrap_x_header$fn__18324 invoke "x_headers.clj" 22]
  [ring.adapter.undertow$undertow_handler$fn$reify__58450 handleRequest "undertow.clj" 40]
  [io.undertow.server.session.SessionAttachmentHandler handleRequest "SessionAttachmentHandler.java" 68]
  [io.undertow.server.Connectors executeRootHandler "Connectors.java" 387]
  [io.undertow.server.HttpServerExchange$1 run "HttpServerExchange.java" 852]
  [org.jboss.threads.ContextClassLoaderSavingRunnable run "ContextClassLoaderSavingRunnable.java" 35]
  [org.jboss.threads.EnhancedQueueExecutor safeRun "EnhancedQueueExecutor.java" 2019]
  [org.jboss.threads.EnhancedQueueExecutor$ThreadBody doRunTask "EnhancedQueueExecutor.java" 1558]
  [org.jboss.threads.EnhancedQueueExecutor$ThreadBody run "EnhancedQueueExecutor.java" 1449]
  [org.xnio.XnioWorker$WorkerThreadFactory$1$1 run "XnioWorker.java" 1282]
  [java.lang.Thread run "Thread.java" 840]]}
25-09-02 17:00:26:704 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/host/instance 
 - 参数: 
 {:Hosts [{:HostId "2d06ec3a-9ecd-422c-a76a-1afc16b47b24", :HostName "1", :ConfigId nil, :StationHardware nil} {:HostId "da28d67a-f0fc-46a6-ab37-d412e6ab9f26", :HostName "2", :ConfigId nil, :StationHardware nil}]} 
 =============================================================

25-09-02 17:00:26:729 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:level "error", :content "调用外部接口出错"}
25-09-02 17:00:26:731 DESKTOP-3BSREDP ERROR [clj-backend.common.biz-error:48] - 发生系统错误. 
错误码: 10001 
错误描述: 调用外部接口出错 
错误数据: #error {
 :cause "Connection refused: connect"
 :via
 [{:type java.net.ConnectException
   :message "Connection refused: connect"
   :at [sun.nio.ch.Net connect0 "Net.java" -2]}]
 :trace
 [[sun.nio.ch.Net connect0 "Net.java" -2]
  [sun.nio.ch.Net connect "Net.java" 579]
  [sun.nio.ch.Net connect "Net.java" 568]
  [sun.nio.ch.NioSocketImpl connect "NioSocketImpl.java" 593]
  [java.net.SocksSocketImpl connect "SocksSocketImpl.java" 327]
  [java.net.Socket connect "Socket.java" 633]
  [org.apache.http.conn.scheme.PlainSocketFactory connectSocket "PlainSocketFactory.java" 120]
  [org.apache.http.impl.conn.DefaultClientConnectionOperator openConnection "DefaultClientConnectionOperator.java" 179]
  [org.apache.http.impl.conn.ManagedClientConnectionImpl open "ManagedClientConnectionImpl.java" 328]
  [org.apache.http.impl.client.DefaultRequestDirector tryConnect "DefaultRequestDirector.java" 612]
  [org.apache.http.impl.client.DefaultRequestDirector execute "DefaultRequestDirector.java" 447]
  [org.apache.http.impl.client.AbstractHttpClient doExecute "AbstractHttpClient.java" 884]
  [org.apache.http.impl.client.CloseableHttpClient execute "CloseableHttpClient.java" 82]
  [org.apache.http.impl.client.CloseableHttpClient execute "CloseableHttpClient.java" 107]
  [clj_http.core$request invokeStatic "core.clj" 304]
  [clj_http.core$request invoke "core.clj" 208]
  [clojure.lang.Var invoke "Var.java" 386]
  [clj_http.client$wrap_request_timing$fn__23258 invoke "client.clj" 835]
  [clj_http.headers$wrap_header_map$fn__22290 invoke "headers.clj" 143]
  [clj_http.client$wrap_query_params$fn__23155 invoke "client.clj" 661]
  [clj_http.client$wrap_basic_auth$fn__23162 invoke "client.clj" 677]
  [clj_http.client$wrap_oauth$fn__23166 invoke "client.clj" 687]
  [clj_http.client$wrap_user_info$fn__23171 invoke "client.clj" 700]
  [clj_http.client$wrap_url$fn__23244 invoke "client.clj" 801]
  [clj_http.client$wrap_redirects$fn__22931 invoke "client.clj" 267]
  [clj_http.client$wrap_decompression$fn__22956 invoke "client.clj" 339]
  [clj_http.client$wrap_input_coercion$fn__23088 invoke "client.clj" 490]
  [clj_http.client$wrap_additional_header_parsing$fn__23109 invoke "client.clj" 552]
  [clj_http.client$wrap_output_coercion$fn__23079 invoke "client.clj" 468]
  [clj_http.client$wrap_exceptions$fn__22917 invoke "client.clj" 219]
  [clj_http.client$wrap_accept$fn__23123 invoke "client.clj" 592]
  [clj_http.client$wrap_accept_encoding$fn__23129 invoke "client.clj" 609]
  [clj_http.client$wrap_content_type$fn__23118 invoke "client.clj" 584]
  [clj_http.client$wrap_form_params$fn__23218 invoke "client.clj" 761]
  [clj_http.client$wrap_nested_params$fn__23239 invoke "client.clj" 794]
  [clj_http.client$wrap_method$fn__23178 invoke "client.clj" 707]
  [clj_http.cookies$wrap_cookies$fn__21212 invoke "cookies.clj" 124]
  [clj_http.links$wrap_links$fn__22553 invoke "links.clj" 51]
  [clj_http.client$wrap_unknown_host$fn__23248 invoke "client.clj" 810]
  [clj_http.client$post invokeStatic "client.clj" 925]
  [clj_http.client$post doInvoke "client.clj" 921]
  [clojure.lang.RestFn invoke "RestFn.java" 426]
  [clj_backend.common.http_client$post invokeStatic "http_client.clj" 29]
  [clj_backend.common.http_client$post invoke "http_client.clj" 22]
  [clj_backend.common.http_client$post invokeStatic "http_client.clj" 25]
  [clj_backend.common.http_client$post invoke "http_client.clj" 22]
  [clj_backend.modules.hardware.station.service$host_instance_handle invokeStatic "service.clj" 536]
  [clj_backend.modules.hardware.station.service$host_instance_handle invoke "service.clj" 522]
  [clj_backend.modules.sys.user.sys_user_service$login invokeStatic "sys_user_service.clj" 96]
  [clj_backend.modules.sys.user.sys_user_service$login invoke "sys_user_service.clj" 91]
  [clj_backend.modules.sys.user.sys_user_routes$routes$fn__43149 invoke "sys_user_routes.clj" 56]
  [clj_backend.common.trial$trial_middleware$fn__42698 invoke "trial.clj" 73]
  [reitit.ring.coercion$fn__52552$fn__52554$fn__52555 invoke "coercion.cljc" 40]
  [reitit.ring.coercion$fn__52575$fn__52577$fn__52578 invoke "coercion.cljc" 80]
  [reitit.ring.middleware.exception$wrap$fn__49531$fn__49532 invoke "exception.clj" 49]
  [clj_backend.middleware.logger$logger_middleware$fn__52770 invoke "logger.clj" 18]
  [muuntaja.middleware$wrap_format_request$fn__52693 invoke "middleware.clj" 114]
  [muuntaja.middleware$wrap_format_response$fn__52697 invoke "middleware.clj" 132]
  [muuntaja.middleware$wrap_format_negotiate$fn__52690 invoke "middleware.clj" 96]
  [ring.middleware.params$wrap_params$fn__18764 invoke "params.clj" 67]
  [reitit.ring$ring_handler$fn__12807 invoke "ring.cljc" 329]
  [clojure.lang.AFn applyToHelper "AFn.java" 154]
  [clojure.lang.AFn applyTo "AFn.java" 144]
  [clojure.lang.AFunction$1 doInvoke "AFunction.java" 33]
  [clojure.lang.RestFn invoke "RestFn.java" 411]
  [clojure.lang.Var invoke "Var.java" 386]
  [ring.middleware.reload$wrap_reload$fn__16585 invoke "reload.clj" 39]
  [selmer.middleware$wrap_error_page$fn__16600 invoke "middleware.clj" 18]
  [prone.middleware$wrap_exceptions$fn__16842 invoke "middleware.clj" 169]
  [ring.middleware.flash$wrap_flash$fn__16963 invoke "flash.clj" 39]
  [ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290 invoke "session.clj" 88]
  [ring.middleware.cors$handle_cors invokeStatic "cors.cljc" 175]
  [ring.middleware.cors$handle_cors invoke "cors.cljc" 167]
  [ring.middleware.cors$wrap_cors$fn__16942 invoke "cors.cljc" 205]
  [ring.middleware.keyword_params$wrap_keyword_params$fn__18384 invoke "keyword_params.clj" 53]
  [ring.middleware.nested_params$wrap_nested_params$fn__18442 invoke "nested_params.clj" 89]
  [ring.middleware.multipart_params$wrap_multipart_params$fn__18740 invoke "multipart_params.clj" 173]
  [ring.middleware.params$wrap_params$fn__18764 invoke "params.clj" 67]
  [ring.middleware.cookies$wrap_cookies$fn__18059 invoke "cookies.clj" 175]
  [ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935 invoke "absolute_redirects.clj" 47]
  [ring.middleware.resource$wrap_resource_prefer_resources$fn__18800 invoke "resource.clj" 25]
  [ring.middleware.content_type$wrap_content_type$fn__18883 invoke "content_type.clj" 34]
  [ring.middleware.default_charset$wrap_default_charset$fn__18907 invoke "default_charset.clj" 31]
  [ring.middleware.not_modified$wrap_not_modified$fn__18864 invoke "not_modified.clj" 61]
  [ring.middleware.x_headers$wrap_x_header$fn__18324 invoke "x_headers.clj" 22]
  [ring.middleware.x_headers$wrap_x_header$fn__18324 invoke "x_headers.clj" 22]
  [ring.middleware.x_headers$wrap_x_header$fn__18324 invoke "x_headers.clj" 22]
  [ring.adapter.undertow$undertow_handler$fn$reify__58450 handleRequest "undertow.clj" 40]
  [io.undertow.server.session.SessionAttachmentHandler handleRequest "SessionAttachmentHandler.java" 68]
  [io.undertow.server.Connectors executeRootHandler "Connectors.java" 387]
  [io.undertow.server.HttpServerExchange$1 run "HttpServerExchange.java" 852]
  [org.jboss.threads.ContextClassLoaderSavingRunnable run "ContextClassLoaderSavingRunnable.java" 35]
  [org.jboss.threads.EnhancedQueueExecutor safeRun "EnhancedQueueExecutor.java" 2019]
  [org.jboss.threads.EnhancedQueueExecutor$ThreadBody doRunTask "EnhancedQueueExecutor.java" 1558]
  [org.jboss.threads.EnhancedQueueExecutor$ThreadBody run "EnhancedQueueExecutor.java" 1449]
  [org.xnio.XnioWorker$WorkerThreadFactory$1$1 run "XnioWorker.java" 1282]
  [java.lang.Thread run "Thread.java" 840]]}
25-09-02 17:00:27:373 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/host/instance 
 - 参数: 
 {:Hosts [{:HostId "2d06ec3a-9ecd-422c-a76a-1afc16b47b24", :HostName "1", :ConfigId nil, :StationHardware nil} {:HostId "da28d67a-f0fc-46a6-ab37-d412e6ab9f26", :HostName "2", :ConfigId nil, :StationHardware nil}]} 
 =============================================================

25-09-02 17:00:27:398 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:level "error", :content "调用外部接口出错"}
25-09-02 17:00:27:400 DESKTOP-3BSREDP ERROR [clj-backend.common.biz-error:48] - 发生系统错误. 
错误码: 10001 
错误描述: 调用外部接口出错 
错误数据: #error {
 :cause "Connection refused: connect"
 :via
 [{:type java.net.ConnectException
   :message "Connection refused: connect"
   :at [sun.nio.ch.Net connect0 "Net.java" -2]}]
 :trace
 [[sun.nio.ch.Net connect0 "Net.java" -2]
  [sun.nio.ch.Net connect "Net.java" 579]
  [sun.nio.ch.Net connect "Net.java" 568]
  [sun.nio.ch.NioSocketImpl connect "NioSocketImpl.java" 593]
  [java.net.SocksSocketImpl connect "SocksSocketImpl.java" 327]
  [java.net.Socket connect "Socket.java" 633]
  [org.apache.http.conn.scheme.PlainSocketFactory connectSocket "PlainSocketFactory.java" 120]
  [org.apache.http.impl.conn.DefaultClientConnectionOperator openConnection "DefaultClientConnectionOperator.java" 179]
  [org.apache.http.impl.conn.ManagedClientConnectionImpl open "ManagedClientConnectionImpl.java" 328]
  [org.apache.http.impl.client.DefaultRequestDirector tryConnect "DefaultRequestDirector.java" 612]
  [org.apache.http.impl.client.DefaultRequestDirector execute "DefaultRequestDirector.java" 447]
  [org.apache.http.impl.client.AbstractHttpClient doExecute "AbstractHttpClient.java" 884]
  [org.apache.http.impl.client.CloseableHttpClient execute "CloseableHttpClient.java" 82]
  [org.apache.http.impl.client.CloseableHttpClient execute "CloseableHttpClient.java" 107]
  [clj_http.core$request invokeStatic "core.clj" 304]
  [clj_http.core$request invoke "core.clj" 208]
  [clojure.lang.Var invoke "Var.java" 386]
  [clj_http.client$wrap_request_timing$fn__23258 invoke "client.clj" 835]
  [clj_http.headers$wrap_header_map$fn__22290 invoke "headers.clj" 143]
  [clj_http.client$wrap_query_params$fn__23155 invoke "client.clj" 661]
  [clj_http.client$wrap_basic_auth$fn__23162 invoke "client.clj" 677]
  [clj_http.client$wrap_oauth$fn__23166 invoke "client.clj" 687]
  [clj_http.client$wrap_user_info$fn__23171 invoke "client.clj" 700]
  [clj_http.client$wrap_url$fn__23244 invoke "client.clj" 801]
  [clj_http.client$wrap_redirects$fn__22931 invoke "client.clj" 267]
  [clj_http.client$wrap_decompression$fn__22956 invoke "client.clj" 339]
  [clj_http.client$wrap_input_coercion$fn__23088 invoke "client.clj" 490]
  [clj_http.client$wrap_additional_header_parsing$fn__23109 invoke "client.clj" 552]
  [clj_http.client$wrap_output_coercion$fn__23079 invoke "client.clj" 468]
  [clj_http.client$wrap_exceptions$fn__22917 invoke "client.clj" 219]
  [clj_http.client$wrap_accept$fn__23123 invoke "client.clj" 592]
  [clj_http.client$wrap_accept_encoding$fn__23129 invoke "client.clj" 609]
  [clj_http.client$wrap_content_type$fn__23118 invoke "client.clj" 584]
  [clj_http.client$wrap_form_params$fn__23218 invoke "client.clj" 761]
  [clj_http.client$wrap_nested_params$fn__23239 invoke "client.clj" 794]
  [clj_http.client$wrap_method$fn__23178 invoke "client.clj" 707]
  [clj_http.cookies$wrap_cookies$fn__21212 invoke "cookies.clj" 124]
  [clj_http.links$wrap_links$fn__22553 invoke "links.clj" 51]
  [clj_http.client$wrap_unknown_host$fn__23248 invoke "client.clj" 810]
  [clj_http.client$post invokeStatic "client.clj" 925]
  [clj_http.client$post doInvoke "client.clj" 921]
  [clojure.lang.RestFn invoke "RestFn.java" 426]
  [clj_backend.common.http_client$post invokeStatic "http_client.clj" 29]
  [clj_backend.common.http_client$post invoke "http_client.clj" 22]
  [clj_backend.common.http_client$post invokeStatic "http_client.clj" 25]
  [clj_backend.common.http_client$post invoke "http_client.clj" 22]
  [clj_backend.modules.hardware.station.service$host_instance_handle invokeStatic "service.clj" 536]
  [clj_backend.modules.hardware.station.service$host_instance_handle invoke "service.clj" 522]
  [clj_backend.modules.sys.user.sys_user_service$login invokeStatic "sys_user_service.clj" 96]
  [clj_backend.modules.sys.user.sys_user_service$login invoke "sys_user_service.clj" 91]
  [clj_backend.modules.sys.user.sys_user_routes$routes$fn__43149 invoke "sys_user_routes.clj" 56]
  [clj_backend.common.trial$trial_middleware$fn__42698 invoke "trial.clj" 73]
  [reitit.ring.coercion$fn__52552$fn__52554$fn__52555 invoke "coercion.cljc" 40]
  [reitit.ring.coercion$fn__52575$fn__52577$fn__52578 invoke "coercion.cljc" 80]
  [reitit.ring.middleware.exception$wrap$fn__49531$fn__49532 invoke "exception.clj" 49]
  [clj_backend.middleware.logger$logger_middleware$fn__52770 invoke "logger.clj" 18]
  [muuntaja.middleware$wrap_format_request$fn__52693 invoke "middleware.clj" 114]
  [muuntaja.middleware$wrap_format_response$fn__52697 invoke "middleware.clj" 132]
  [muuntaja.middleware$wrap_format_negotiate$fn__52690 invoke "middleware.clj" 96]
  [ring.middleware.params$wrap_params$fn__18764 invoke "params.clj" 67]
  [reitit.ring$ring_handler$fn__12807 invoke "ring.cljc" 329]
  [clojure.lang.AFn applyToHelper "AFn.java" 154]
  [clojure.lang.AFn applyTo "AFn.java" 144]
  [clojure.lang.AFunction$1 doInvoke "AFunction.java" 33]
  [clojure.lang.RestFn invoke "RestFn.java" 411]
  [clojure.lang.Var invoke "Var.java" 386]
  [ring.middleware.reload$wrap_reload$fn__16585 invoke "reload.clj" 39]
  [selmer.middleware$wrap_error_page$fn__16600 invoke "middleware.clj" 18]
  [prone.middleware$wrap_exceptions$fn__16842 invoke "middleware.clj" 169]
  [ring.middleware.flash$wrap_flash$fn__16963 invoke "flash.clj" 39]
  [ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290 invoke "session.clj" 88]
  [ring.middleware.cors$handle_cors invokeStatic "cors.cljc" 175]
  [ring.middleware.cors$handle_cors invoke "cors.cljc" 167]
  [ring.middleware.cors$wrap_cors$fn__16942 invoke "cors.cljc" 205]
  [ring.middleware.keyword_params$wrap_keyword_params$fn__18384 invoke "keyword_params.clj" 53]
  [ring.middleware.nested_params$wrap_nested_params$fn__18442 invoke "nested_params.clj" 89]
  [ring.middleware.multipart_params$wrap_multipart_params$fn__18740 invoke "multipart_params.clj" 173]
  [ring.middleware.params$wrap_params$fn__18764 invoke "params.clj" 67]
  [ring.middleware.cookies$wrap_cookies$fn__18059 invoke "cookies.clj" 175]
  [ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935 invoke "absolute_redirects.clj" 47]
  [ring.middleware.resource$wrap_resource_prefer_resources$fn__18800 invoke "resource.clj" 25]
  [ring.middleware.content_type$wrap_content_type$fn__18883 invoke "content_type.clj" 34]
  [ring.middleware.default_charset$wrap_default_charset$fn__18907 invoke "default_charset.clj" 31]
  [ring.middleware.not_modified$wrap_not_modified$fn__18864 invoke "not_modified.clj" 61]
  [ring.middleware.x_headers$wrap_x_header$fn__18324 invoke "x_headers.clj" 22]
  [ring.middleware.x_headers$wrap_x_header$fn__18324 invoke "x_headers.clj" 22]
  [ring.middleware.x_headers$wrap_x_header$fn__18324 invoke "x_headers.clj" 22]
  [ring.adapter.undertow$undertow_handler$fn$reify__58450 handleRequest "undertow.clj" 40]
  [io.undertow.server.session.SessionAttachmentHandler handleRequest "SessionAttachmentHandler.java" 68]
  [io.undertow.server.Connectors executeRootHandler "Connectors.java" 387]
  [io.undertow.server.HttpServerExchange$1 run "HttpServerExchange.java" 852]
  [org.jboss.threads.ContextClassLoaderSavingRunnable run "ContextClassLoaderSavingRunnable.java" 35]
  [org.jboss.threads.EnhancedQueueExecutor safeRun "EnhancedQueueExecutor.java" 2019]
  [org.jboss.threads.EnhancedQueueExecutor$ThreadBody doRunTask "EnhancedQueueExecutor.java" 1558]
  [org.jboss.threads.EnhancedQueueExecutor$ThreadBody run "EnhancedQueueExecutor.java" 1449]
  [org.xnio.XnioWorker$WorkerThreadFactory$1$1 run "XnioWorker.java" 1282]
  [java.lang.Thread run "Thread.java" 840]]}
25-09-02 17:00:30:982 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/host/instance 
 - 参数: 
 {:Hosts [{:HostId "2d06ec3a-9ecd-422c-a76a-1afc16b47b24", :HostName "1", :ConfigId nil, :StationHardware nil} {:HostId "da28d67a-f0fc-46a6-ab37-d412e6ab9f26", :HostName "2", :ConfigId nil, :StationHardware nil}]} 
 =============================================================

25-09-02 17:00:31:240 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/hardware 
 - 参数: 
 {:HardwareDtos {"hardware_simulator-0000-hw-ccss-id-0000" {:DaChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :AdChannels [{:Sensors ["传感器0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0}], :CreepAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 10, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 11, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 12, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 13, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 14, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 15, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 16, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 17, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 18, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 19, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}], :HandBoxChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil}], :Devices ({:Hwkey "hardware_simulator", :SubId 0, :DeviceId "9cdeaaab-9005-4fb9-b11a-279864d5c0cd"}), :ServoAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 7, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0}], :UpDirectValue 5, :TensileUpOrDown 7, :PressUpOrDown 6} {:Hwkey "hardware_simulator", :RealIndex 3, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0}], :UpDirectValue 5, :TensileUpOrDown 5, :PressUpOrDown 5}], :InputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :OutputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :TempAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 10, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}]}}} 
 =============================================================

25-09-02 17:00:31:326 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/api/appserver/config/info 
 - 参数: 
 {:AppServerIp "*************", :AppServerPort 3000, :ComputerId "MP2QAVV0", :AppServerApiPort 3000} 
 =============================================================

25-09-02 17:00:31:370 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 登录"}
25-09-02 17:00:35:419 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "打开项目 project_27"}
25-09-02 17:00:35:450 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:861] - 模板开始实例化
25-09-02 17:00:39:783 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "4502b496-bf5a-4e7f-bce1-f022d834547f", :code 0, :msg "模板生成成功"}
25-09-02 17:00:39:784 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-4502b496-bf5a-4e7f-bce1-f022d834547f 中添加消息 {:ProcessId "4502b496-bf5a-4e7f-bce1-f022d834547f", :code 0, :msg "模板生成成功"}
25-09-02 17:00:39:784 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "4502b496-bf5a-4e7f-bce1-f022d834547f", :code 0, :msg "模板生成成功"}
25-09-02 17:00:41:060 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【打开项目默认执行动作】"}
25-09-02 17:00:41:062 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "running"} 
 =============================================================

25-09-02 17:00:41:761 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【打开项目默认执行动作】"}
25-09-02 17:00:41:762 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "finished"} 
 =============================================================

25-09-02 17:20:38:474 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【联机】"}
25-09-02 17:20:38:476 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "running"} 
 =============================================================

25-09-02 17:21:10:044 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【关闭项目时默认执行动作】"}
25-09-02 17:21:10:045 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "running"} 
 =============================================================

25-09-02 17:21:10:090 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【关闭项目重置参数】"}
25-09-02 17:21:10:092 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "running"} 
 =============================================================

25-09-02 17:21:10:165 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:632] - Handling :finalize-project-quit-save event for project {:event :finalize-project-quit-save, :project_id 27, :user_id 1}
25-09-02 17:21:10:180 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【关闭项目时默认执行动作】"}
25-09-02 17:21:10:182 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "finished"} 
 =============================================================

25-09-02 17:21:10:298 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 项目数据库 数据库缓存
25-09-02 17:21:10:319 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-common-service:85] - 项目关闭执行动作---完成
25-09-02 17:21:10:321 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "删除了【关闭项目 project_27】项目"}
25-09-02 17:21:10:322 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【关闭项目重置参数】"}
25-09-02 17:21:10:324 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "finished"} 
 =============================================================

25-09-02 17:21:10:327 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【打开项目默认执行动作】"}
25-09-02 17:21:10:336 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【联机】"}
25-09-02 17:21:10:338 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "finished"} 
 =============================================================

25-09-02 17:21:10:409 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 系统数据库 数据库缓存
25-09-02 17:21:10:411 DESKTOP-3BSREDP WARN [clj-backend.modules.project.project-service:416] - 项目缓存刷新失败，但继续执行保存操作: {:success false, :message "部分缓存刷新失败", :details {:project true, :project-data false, :system true}}
25-09-02 17:21:10:412 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "保存了项目"}
25-09-02 17:21:10:469 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 D:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1756707875343_project_27_copy.db 成功
25-09-02 17:21:10:487 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【关闭项目时默认执行动作】"}
25-09-02 17:21:10:492 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【关闭项目重置参数】"}
25-09-02 17:21:10:493 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:639] - Project event listener started.
25-09-02 17:21:12:117 DESKTOP-3BSREDP WARN [clj-backend.db.connections:118] - 关闭 project_27 数据库连接时出错: [SQLITE_LOCKED]  A table in the database is locked (database table is locked)
25-09-02 17:21:12:124 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 D:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1756707875343_project_27.db 成功
25-09-02 17:21:12:189 DESKTOP-3BSREDP WARN [clj-backend.db.connections:40] - 已为数据库连接 project_27 应用性能优化配置
25-09-02 17:21:12:192 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - 创建 project_27 项目库连接
25-09-02 17:21:14:758 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "打开项目 project_27"}
25-09-02 17:21:14:783 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:861] - 模板开始实例化
25-09-02 17:21:15:938 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "e1d8180d-d869-458b-b50d-3b9435c68e68", :code 0, :msg "模板生成成功"}
25-09-02 17:21:15:939 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-e1d8180d-d869-458b-b50d-3b9435c68e68 中添加消息 {:ProcessId "e1d8180d-d869-458b-b50d-3b9435c68e68", :code 0, :msg "模板生成成功"}
25-09-02 17:21:15:939 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "e1d8180d-d869-458b-b50d-3b9435c68e68", :code 0, :msg "模板生成成功"}
25-09-02 17:21:17:092 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【打开项目默认执行动作】"}
25-09-02 17:21:17:093 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "running"} 
 =============================================================

25-09-02 17:21:17:279 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【打开项目默认执行动作】"}
25-09-02 17:21:17:280 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "finished"} 
 =============================================================

25-09-02 17:21:20:709 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【联机】"}
25-09-02 17:21:20:712 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "running"} 
 =============================================================

25-09-02 17:22:25:170 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "【超级管理员】admin: 保存了【联机】动作"}
25-09-02 17:22:25:253 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "【超级管理员】admin: 执行了【abort】操作"}
25-09-02 17:22:25:264 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【联机】"}
25-09-02 17:22:25:265 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "finished"} 
 =============================================================

25-09-02 17:22:26:209 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "【超级管理员】admin: 保存了【联机】动作"}
25-09-02 17:22:26:344 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【联机】"}
25-09-02 17:22:26:346 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "running"} 
 =============================================================

25-09-02 17:36:16:962 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【关闭项目时默认执行动作】"}
25-09-02 17:36:16:963 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "running"} 
 =============================================================

25-09-02 17:36:17:036 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【关闭项目重置参数】"}
25-09-02 17:36:17:038 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "running"} 
 =============================================================

25-09-02 17:36:17:074 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:632] - Handling :finalize-project-quit-save event for project {:event :finalize-project-quit-save, :project_id 27, :user_id 1}
25-09-02 17:36:17:096 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【关闭项目时默认执行动作】"}
25-09-02 17:36:17:098 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "finished"} 
 =============================================================

25-09-02 17:36:17:143 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-common-service:85] - 项目关闭执行动作---完成
25-09-02 17:36:17:144 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "删除了【关闭项目 project_27】项目"}
25-09-02 17:36:17:145 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【关闭项目重置参数】"}
25-09-02 17:36:17:147 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "finished"} 
 =============================================================

25-09-02 17:36:17:150 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【打开项目默认执行动作】"}
25-09-02 17:36:17:156 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【联机】"}
25-09-02 17:36:17:158 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "finished"} 
 =============================================================

25-09-02 17:36:17:194 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 项目数据库 数据库缓存
25-09-02 17:36:17:298 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 系统数据库 数据库缓存
25-09-02 17:36:17:300 DESKTOP-3BSREDP WARN [clj-backend.modules.project.project-service:416] - 项目缓存刷新失败，但继续执行保存操作: {:success false, :message "部分缓存刷新失败", :details {:project true, :project-data false, :system true}}
25-09-02 17:36:17:302 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "保存了项目"}
25-09-02 17:36:17:308 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【关闭项目时默认执行动作】"}
25-09-02 17:36:17:311 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【关闭项目重置参数】"}
25-09-02 17:36:17:354 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 D:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1756707875343_project_27_copy.db 成功
25-09-02 17:36:17:379 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:639] - Project event listener started.
25-09-02 17:36:18:949 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 D:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1756707875343_project_27.db 成功
25-09-02 17:36:19:008 DESKTOP-3BSREDP WARN [clj-backend.db.connections:40] - 已为数据库连接 project_27 应用性能优化配置
25-09-02 17:36:19:009 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - 创建 project_27 项目库连接
25-09-02 17:36:26:226 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/host/instance 
 - 参数: 
 {:Hosts [{:HostId "2d06ec3a-9ecd-422c-a76a-1afc16b47b24", :HostName "1", :ConfigId nil, :StationHardware nil} {:HostId "da28d67a-f0fc-46a6-ab37-d412e6ab9f26", :HostName "2", :ConfigId nil, :StationHardware nil}]} 
 =============================================================

25-09-02 17:36:26:264 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/hardware 
 - 参数: 
 {:HardwareDtos {"hardware_simulator-0000-hw-ccss-id-0000" {:DaChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :AdChannels [{:Sensors ["传感器0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0}], :CreepAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 10, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 11, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 12, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 13, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 14, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 15, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 16, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 17, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 18, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 19, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}], :HandBoxChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil}], :Devices ({:Hwkey "hardware_simulator", :SubId 0, :DeviceId "9cdeaaab-9005-4fb9-b11a-279864d5c0cd"}), :ServoAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 7, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0}], :UpDirectValue 5, :TensileUpOrDown 7, :PressUpOrDown 6} {:Hwkey "hardware_simulator", :RealIndex 3, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0}], :UpDirectValue 5, :TensileUpOrDown 5, :PressUpOrDown 5}], :InputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :OutputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :TempAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 10, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}]}}} 
 =============================================================

25-09-02 17:36:26:297 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/api/appserver/config/info 
 - 参数: 
 {:AppServerIp "*************", :AppServerPort 3000, :ComputerId "MP2QAVV0", :AppServerApiPort 3000} 
 =============================================================

25-09-02 17:36:26:330 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 登录"}
25-09-02 17:36:26:611 DESKTOP-3BSREDP INFO [clj-backend.modules.standby.service:160] - 运行 sync-to-share-http (HTTP POST)...
25-09-02 17:36:26:614 DESKTOP-3BSREDP WARN [clj-backend.modules.standby.service:170] - 从节点 IP 或端口未配置. 跳过 sync-to-share-http. 
25-09-02 17:36:35:601 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 保存了cfg"}
25-09-02 17:43:23:194 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/host/instance 
 - 参数: 
 {:Hosts [{:HostId "2d06ec3a-9ecd-422c-a76a-1afc16b47b24", :HostName "1", :ConfigId nil, :StationHardware nil} {:HostId "da28d67a-f0fc-46a6-ab37-d412e6ab9f26", :HostName "2", :ConfigId nil, :StationHardware nil}]} 
 =============================================================

25-09-02 17:43:23:446 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/hardware 
 - 参数: 
 {:HardwareDtos {"hardware_simulator-0000-hw-ccss-id-0000" {:DaChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :AdChannels [{:Sensors ["传感器0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0}], :CreepAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 10, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 11, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 12, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 13, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 14, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 15, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 16, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 17, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 18, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 19, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}], :HandBoxChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil}], :Devices ({:Hwkey "hardware_simulator", :SubId 0, :DeviceId "9cdeaaab-9005-4fb9-b11a-279864d5c0cd"}), :ServoAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 7, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0}], :UpDirectValue 5, :TensileUpOrDown 7, :PressUpOrDown 6} {:Hwkey "hardware_simulator", :RealIndex 3, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0}], :UpDirectValue 5, :TensileUpOrDown 5, :PressUpOrDown 5}], :InputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :OutputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :TempAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 10, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}]}}} 
 =============================================================

25-09-02 17:43:23:527 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/api/appserver/config/info 
 - 参数: 
 {:AppServerIp "*************", :AppServerPort 3000, :ComputerId "MP2QAVV0", :AppServerApiPort 3000} 
 =============================================================

25-09-02 17:43:23:560 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 登录"}
25-09-02 17:43:30:328 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "打开项目 project_27"}
25-09-02 17:43:30:351 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:861] - 模板开始实例化
25-09-02 17:43:35:146 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "6f0d547a-54fd-4188-b116-4d04d52c0c50", :code 0, :msg "模板生成成功"}
25-09-02 17:43:35:147 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-6f0d547a-54fd-4188-b116-4d04d52c0c50 中添加消息 {:ProcessId "6f0d547a-54fd-4188-b116-4d04d52c0c50", :code 0, :msg "模板生成成功"}
25-09-02 17:43:35:148 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "6f0d547a-54fd-4188-b116-4d04d52c0c50", :code 0, :msg "模板生成成功"}
25-09-02 17:43:36:750 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【打开项目默认执行动作】"}
25-09-02 17:43:36:752 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "running"} 
 =============================================================

25-09-02 17:43:38:031 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【打开项目默认执行动作】"}
25-09-02 17:43:38:034 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "finished"} 
 =============================================================

25-09-02 17:44:28:337 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【关闭项目时默认执行动作】"}
25-09-02 17:44:28:339 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "running"} 
 =============================================================

25-09-02 17:44:28:385 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【关闭项目重置参数】"}
25-09-02 17:44:28:386 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "running"} 
 =============================================================

25-09-02 17:44:28:434 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-common-service:85] - 项目关闭执行动作---完成
25-09-02 17:44:28:436 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "删除了【关闭项目 project_27】项目"}
25-09-02 17:44:28:443 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【打开项目默认执行动作】"}
25-09-02 17:44:28:449 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【关闭项目时默认执行动作】"}
25-09-02 17:44:28:450 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "finished"} 
 =============================================================

25-09-02 17:44:28:459 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:632] - Handling :finalize-project-quit-save event for project {:event :finalize-project-quit-save, :project_id 27, :user_id 1}
25-09-02 17:44:28:524 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【关闭项目重置参数】"}
25-09-02 17:44:28:525 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "finished"} 
 =============================================================

25-09-02 17:44:28:574 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 项目数据库 数据库缓存
25-09-02 17:44:28:681 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 系统数据库 数据库缓存
25-09-02 17:44:28:682 DESKTOP-3BSREDP WARN [clj-backend.modules.project.project-service:416] - 项目缓存刷新失败，但继续执行保存操作: {:success false, :message "部分缓存刷新失败", :details {:project true, :project-data false, :system true}}
25-09-02 17:44:28:684 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "保存了项目"}
25-09-02 17:44:28:735 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 D:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1756707875343_project_27_copy.db 成功
25-09-02 17:44:28:759 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:639] - Project event listener started.
25-09-02 17:44:30:258 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 D:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1756707875343_project_27.db 成功
25-09-02 17:44:30:334 DESKTOP-3BSREDP WARN [clj-backend.db.connections:40] - 已为数据库连接 project_27 应用性能优化配置
25-09-02 17:44:30:335 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - 创建 project_27 项目库连接
25-09-02 17:44:36:030 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/host/instance 
 - 参数: 
 {:Hosts [{:HostId "2d06ec3a-9ecd-422c-a76a-1afc16b47b24", :HostName "1", :ConfigId nil, :StationHardware nil} {:HostId "da28d67a-f0fc-46a6-ab37-d412e6ab9f26", :HostName "2", :ConfigId nil, :StationHardware nil}]} 
 =============================================================

25-09-02 17:44:36:061 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/hardware 
 - 参数: 
 {:HardwareDtos {"hardware_simulator-0000-hw-ccss-id-0000" {:DaChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :AdChannels [{:Sensors ["传感器0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0}], :CreepAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 10, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 11, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 12, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 13, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 14, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 15, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 16, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 17, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 18, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 19, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}], :HandBoxChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil}], :Devices ({:Hwkey "hardware_simulator", :SubId 0, :DeviceId "9cdeaaab-9005-4fb9-b11a-279864d5c0cd"}), :ServoAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 7, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0}], :UpDirectValue 5, :TensileUpOrDown 7, :PressUpOrDown 6} {:Hwkey "hardware_simulator", :RealIndex 3, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0}], :UpDirectValue 5, :TensileUpOrDown 5, :PressUpOrDown 5}], :InputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :OutputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :TempAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 10, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}]}}} 
 =============================================================

25-09-02 17:44:36:094 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/api/appserver/config/info 
 - 参数: 
 {:AppServerIp "*************", :AppServerPort 3000, :ComputerId "MP2QAVV0", :AppServerApiPort 3000} 
 =============================================================

25-09-02 17:44:36:128 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 登录"}
25-09-02 17:44:36:646 DESKTOP-3BSREDP INFO [clj-backend.modules.standby.service:160] - 运行 sync-to-share-http (HTTP POST)...
25-09-02 17:44:36:649 DESKTOP-3BSREDP WARN [clj-backend.modules.standby.service:170] - 从节点 IP 或端口未配置. 跳过 sync-to-share-http. 
25-09-02 17:44:39:340 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "打开项目 project_27"}
25-09-02 17:44:39:361 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:861] - 模板开始实例化
25-09-02 17:44:40:572 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "cd55ca8e-0ba9-4cb3-bea1-1dc5987a4e00", :code 0, :msg "模板生成成功"}
25-09-02 17:44:40:573 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-cd55ca8e-0ba9-4cb3-bea1-1dc5987a4e00 中添加消息 {:ProcessId "cd55ca8e-0ba9-4cb3-bea1-1dc5987a4e00", :code 0, :msg "模板生成成功"}
25-09-02 17:44:40:573 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "cd55ca8e-0ba9-4cb3-bea1-1dc5987a4e00", :code 0, :msg "模板生成成功"}
25-09-02 17:44:41:916 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【打开项目默认执行动作】"}
25-09-02 17:44:41:917 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "running"} 
 =============================================================

25-09-02 17:44:42:289 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【打开项目默认执行动作】"}
25-09-02 17:44:42:291 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "finished"} 
 =============================================================

25-09-02 17:44:59:395 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【联机】"}
25-09-02 17:44:59:397 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "running"} 
 =============================================================

25-09-02 17:45:38:326 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【启动】"}
25-09-02 17:45:38:327 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-679f70fa-d870-40d9-b121-c759b28044ed", :State "running"} 
 =============================================================

25-09-02 17:45:38:391 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【启动】"}
25-09-02 17:45:38:392 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-679f70fa-d870-40d9-b121-c759b28044ed", :State "finished"} 
 =============================================================

25-09-02 17:50:49:770 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【裂纹检查新版流程图】"}
25-09-02 17:50:49:775 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "running"} 
 =============================================================

25-09-02 17:50:49:888 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_27", :ScriptId "d57f1209-8f63-49f3-b491-c466de382b4b", :Result true}
25-09-02 17:50:49:889 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-d57f1209-8f63-49f3-b491-c466de382b4b 中添加消息 {:ProcessId "project_27", :ScriptId "d57f1209-8f63-49f3-b491-c466de382b4b", :Result true}
25-09-02 17:50:49:890 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_27", :ScriptId "d57f1209-8f63-49f3-b491-c466de382b4b", :Result true}
25-09-02 17:50:50:497 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_27", :ScriptId "a991e16c-f159-4b34-93be-7e67c04fb3de", :Result true}
25-09-02 17:50:50:497 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-a991e16c-f159-4b34-93be-7e67c04fb3de 中添加消息 {:ProcessId "project_27", :ScriptId "a991e16c-f159-4b34-93be-7e67c04fb3de", :Result true}
25-09-02 17:50:50:498 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_27", :ScriptId "a991e16c-f159-4b34-93be-7e67c04fb3de", :Result true}
25-09-02 17:51:39:763 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【终止裂纹长度检查】"}
25-09-02 17:51:39:765 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :State "running"} 
 =============================================================

25-09-02 17:51:39:829 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_27", :ProcessID "project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :SubTaskID "onlyAction-0fd5b761-7c8e-432f-b8e5-5b70cd023b0d", :MsgBody {:Cmd "abort", :InstCode "sample_14785d372", :ActionID "d2a28ac5-6be5-4a4c-806f-424fc39131ee"}}
25-09-02 17:51:39:832 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【裂纹检查新版流程图】"}
25-09-02 17:51:39:835 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "finished"} 
 =============================================================

25-09-02 17:51:39:893 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_27", :ProcessID "project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :SubTaskID "onlyAction-7ab30145-6f3a-4ac1-a4c1-6d9ff9ba49cf", :MsgBody {:Cmd "start", :InstCode "sample_14785d372", :ActionID "da399466-da97-449d-b998-0d7a0823cdd0"}}
25-09-02 17:51:39:896 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【横梁停止】"}
25-09-02 17:51:39:897 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-da399466-da97-449d-b998-0d7a0823cdd0", :State "running"} 
 =============================================================

25-09-02 17:51:39:973 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_27", :ScriptId "b305a1c9-8947-4b83-97f6-8a73c9662de6", :Result true}
25-09-02 17:51:39:974 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-b305a1c9-8947-4b83-97f6-8a73c9662de6 中添加消息 {:ProcessId "project_27", :ScriptId "b305a1c9-8947-4b83-97f6-8a73c9662de6", :Result true}
25-09-02 17:51:39:975 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_27", :ScriptId "b305a1c9-8947-4b83-97f6-8a73c9662de6", :Result true}
25-09-02 17:51:40:182 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【横梁停止】"}
25-09-02 17:51:40:183 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-da399466-da97-449d-b998-0d7a0823cdd0", :State "finished"} 
 =============================================================

25-09-02 17:51:40:378 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【终止裂纹长度检查】"}
25-09-02 17:51:40:380 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :State "finished"} 
 =============================================================

25-09-02 17:51:45:649 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【关闭项目时默认执行动作】"}
25-09-02 17:51:45:651 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "running"} 
 =============================================================

25-09-02 17:51:45:702 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【关闭项目重置参数】"}
25-09-02 17:51:45:704 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "running"} 
 =============================================================

25-09-02 17:51:45:760 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-common-service:85] - 项目关闭执行动作---完成
25-09-02 17:51:45:762 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "删除了【关闭项目 project_27】项目"}
25-09-02 17:51:45:765 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【打开项目默认执行动作】"}
25-09-02 17:51:45:769 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【联机】"}
25-09-02 17:51:45:770 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "finished"} 
 =============================================================

25-09-02 17:51:45:881 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【启动】"}
25-09-02 17:51:45:885 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【裂纹检查新版流程图】"}
25-09-02 17:51:45:889 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【终止裂纹长度检查】"}
25-09-02 17:51:45:892 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【横梁停止】"}
25-09-02 17:51:45:895 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【关闭项目时默认执行动作】"}
25-09-02 17:51:45:896 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "finished"} 
 =============================================================

25-09-02 17:51:45:914 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:632] - Handling :finalize-project-quit-save event for project {:event :finalize-project-quit-save, :project_id 27, :user_id 1}
25-09-02 17:51:45:947 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【关闭项目重置参数】"}
25-09-02 17:51:45:948 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "finished"} 
 =============================================================

25-09-02 17:51:46:028 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 项目数据库 数据库缓存
25-09-02 17:51:46:134 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 系统数据库 数据库缓存
25-09-02 17:51:46:135 DESKTOP-3BSREDP WARN [clj-backend.modules.project.project-service:416] - 项目缓存刷新失败，但继续执行保存操作: {:success false, :message "部分缓存刷新失败", :details {:project true, :project-data false, :system true}}
25-09-02 17:51:46:135 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "保存了项目"}
25-09-02 17:51:46:182 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 D:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1756707875343_project_27_copy.db 成功
25-09-02 17:51:46:205 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:639] - Project event listener started.
25-09-02 17:51:47:762 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 D:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1756707875343_project_27.db 成功
25-09-02 17:51:47:860 DESKTOP-3BSREDP WARN [clj-backend.db.connections:40] - 已为数据库连接 project_27 应用性能优化配置
25-09-02 17:51:47:861 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - 创建 project_27 项目库连接
25-09-02 17:52:33:989 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/host/instance 
 - 参数: 
 {:Hosts [{:HostId "2d06ec3a-9ecd-422c-a76a-1afc16b47b24", :HostName "1", :ConfigId nil, :StationHardware nil} {:HostId "da28d67a-f0fc-46a6-ab37-d412e6ab9f26", :HostName "2", :ConfigId nil, :StationHardware nil}]} 
 =============================================================

25-09-02 17:52:34:243 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/hardware 
 - 参数: 
 {:HardwareDtos {"hardware_simulator-0000-hw-ccss-id-0000" {:DaChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :AdChannels [{:Sensors ["传感器0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0}], :CreepAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 10, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 11, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 12, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 13, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 14, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 15, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 16, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 17, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 18, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 19, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}], :HandBoxChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil}], :Devices ({:Hwkey "hardware_simulator", :SubId 0, :DeviceId "9cdeaaab-9005-4fb9-b11a-279864d5c0cd"}), :ServoAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 7, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0}], :UpDirectValue 5, :TensileUpOrDown 7, :PressUpOrDown 6} {:Hwkey "hardware_simulator", :RealIndex 3, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0}], :UpDirectValue 5, :TensileUpOrDown 5, :PressUpOrDown 5}], :InputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :OutputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :TempAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 10, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}]}}} 
 =============================================================

25-09-02 17:52:34:329 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/api/appserver/config/info 
 - 参数: 
 {:AppServerIp "*************", :AppServerPort 3000, :ComputerId "MP2QAVV0", :AppServerApiPort 3000} 
 =============================================================

25-09-02 17:52:34:364 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 登录"}
25-09-02 17:52:39:448 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "打开项目 project_27"}
25-09-02 17:52:39:470 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:861] - 模板开始实例化
25-09-02 17:52:44:118 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "5d96261d-1063-403a-ad4a-794ed942f6dc", :code 0, :msg "模板生成成功"}
25-09-02 17:52:44:119 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-5d96261d-1063-403a-ad4a-794ed942f6dc 中添加消息 {:ProcessId "5d96261d-1063-403a-ad4a-794ed942f6dc", :code 0, :msg "模板生成成功"}
25-09-02 17:52:44:119 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "5d96261d-1063-403a-ad4a-794ed942f6dc", :code 0, :msg "模板生成成功"}
25-09-02 17:52:45:320 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【打开项目默认执行动作】"}
25-09-02 17:52:45:322 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "running"} 
 =============================================================

25-09-02 17:52:47:542 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【打开项目默认执行动作】"}
25-09-02 17:52:47:545 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "finished"} 
 =============================================================

25-09-02 17:52:48:820 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【联机】"}
25-09-02 17:52:48:822 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "running"} 
 =============================================================

25-09-02 17:53:24:823 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【关闭项目时默认执行动作】"}
25-09-02 17:53:24:825 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "running"} 
 =============================================================

25-09-02 17:53:24:874 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【关闭项目重置参数】"}
25-09-02 17:53:24:876 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "running"} 
 =============================================================

25-09-02 17:53:24:955 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-common-service:85] - 项目关闭执行动作---完成
25-09-02 17:53:24:957 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "删除了【关闭项目 project_27】项目"}
25-09-02 17:53:24:962 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【打开项目默认执行动作】"}
25-09-02 17:53:24:966 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【联机】"}
25-09-02 17:53:24:968 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "finished"} 
 =============================================================

25-09-02 17:53:25:008 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【关闭项目时默认执行动作】"}
25-09-02 17:53:25:010 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "finished"} 
 =============================================================

25-09-02 17:53:25:075 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【关闭项目重置参数】"}
25-09-02 17:53:25:077 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "finished"} 
 =============================================================

25-09-02 17:53:25:103 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:632] - Handling :finalize-project-quit-save event for project {:event :finalize-project-quit-save, :project_id 27, :user_id 1}
25-09-02 17:53:25:221 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 项目数据库 数据库缓存
25-09-02 17:53:25:328 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 系统数据库 数据库缓存
25-09-02 17:53:25:330 DESKTOP-3BSREDP WARN [clj-backend.modules.project.project-service:416] - 项目缓存刷新失败，但继续执行保存操作: {:success false, :message "部分缓存刷新失败", :details {:project true, :project-data false, :system true}}
25-09-02 17:53:25:332 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "保存了项目"}
25-09-02 17:53:25:385 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 D:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1756707875343_project_27_copy.db 成功
25-09-02 17:53:25:409 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:639] - Project event listener started.
25-09-02 17:53:26:876 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 D:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1756707875343_project_27.db 成功
25-09-02 17:53:26:971 DESKTOP-3BSREDP WARN [clj-backend.db.connections:40] - 已为数据库连接 project_27 应用性能优化配置
25-09-02 17:53:26:973 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - 创建 project_27 项目库连接
