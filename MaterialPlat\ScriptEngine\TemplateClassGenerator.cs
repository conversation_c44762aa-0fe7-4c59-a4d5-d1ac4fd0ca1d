using Mapping;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using MQ;
using System.Collections.Concurrent;
using System.Reactive.Subjects;
using System.Reflection;
using System.Reflection.PortableExecutable;
using System.Text.Json;
using ScriptEngine.DBHandler;
using ScriptEngine.DBHandler.TableWriters;
using ScriptEngine.SampleInst;
using static Logging.CCSSLogger;
using System.Reactive.Linq;
using ScriptEngine.InputVar;
using Consts;
using System.Runtime.InteropServices;
using System.Reactive;
using System.Reactive.Disposables;
using DBUtils;
using NPOI.XSSF.Streaming.Values;
using ScriptEngine.InputVar.InputVars;
using ScriptEngine.InstantiatedTemplate;
using ScriptEngine.InstantiatedTemplate.Hardware;
using static ScriptEngine.InputVar.InputVar;
using ScriptEngine;
using ScriptEngine.InstantiatedTemplate.Hardware.MappingHardware;
using ScriptEngine.InstantiatedTemplate.SignalVar;
using ScriptEngine.InstantiatedTemplate.SubTask;
using Action = ScriptEngine.InstantiatedTemplate.Action.Action;
using SignalVar = ScriptEngine.InstantiatedTemplate.SignalVar.SignalVar;
using ScriptEngine.InstantiatedTemplate.GlobalProjectMapping;
using ScriptEngine.DaqHandlers;
using ScriptEngine.ResultVar;
using NPOI.SS.Formula.Functions;
using ScriptEngine.CcssFunc;
using System.Diagnostics;

namespace Scripting;

/// <summary>
/// 同步结构
/// </summary>
/// <param name="SyncNo">同步号</param>
/// <param name="Axis">轴</param>
/// <param name="SyncStatus">同步状态</param>
public class SynchronizedInformation
{
    public string SyncNo { get; set; }
    public string Axis { get; set; }
    public int SyncStatus { get; set; }

    public SynchronizedInformation(string syncNo, string axis, int syncStatus)
    {
        SyncNo = syncNo;
        Axis = axis;
        SyncStatus = syncStatus;
    }
}

public class FixedSizeQueue
{
    private Queue<double> _queue;
    private int _capacity;

    /// <summary>
    /// 初始化固定大小的队列
    /// </summary>
    /// <param name="capacity">队列容量</param>
    public FixedSizeQueue(int capacity)
    {
        if (capacity <= 0)
            throw new ArgumentException("队列容量必须大于0", nameof(capacity));

        _capacity = capacity;
        _queue = new Queue<double>(capacity);

        // 初始化队列，填充0值
        for (int i = 0; i < capacity; i++)
        {
            _queue.Enqueue(0);
        }
    }

    /// <summary>
    /// 添加新值到队列，如果队列已满则移除最旧的值
    /// </summary>
    /// <param name="item">要添加的值</param>
    public void Enqueue(double item)
    {
        if (_queue.Count >= _capacity)
        {
            _queue.Dequeue(); // 移除最旧的值
        }
        _queue.Enqueue(item);
    }

    /// <summary>
    /// 获取队列中所有值的平均值
    /// </summary>
    /// <returns>平均值</returns>
    public double GetAverage()
    {
        return _queue.Count > 0 ? _queue.Average() : 0;
    }

    /// <summary>
    /// 获取队列中的所有值
    /// </summary>
    /// <returns>队列中的值数组</returns>
    public double[] GetValues()
    {
        return _queue.ToArray();
    }

    /// <summary>
    /// 获取队列当前元素数量
    /// </summary>
    public int Count => _queue.Count;

    /// <summary>
    /// 获取队列容量
    /// </summary>
    public int Capacity => _capacity;

    /// <summary>
    /// 清空队列并重新用0填充
    /// </summary>
    public void Reset()
    {
        _queue.Clear();
        for (int i = 0; i < _capacity; i++)
        {
            _queue.Enqueue(0);
        }
    }
}

/// <summary>
/// 抽象模板记录，包含一组可用的方法和属性。
/// </summary>
public abstract record ITemplate : IDisposable
{

    // 模板名称, 对应生成的类名
    public virtual string? TemplateName { get; set; }

    // 模本的状态，实验的状态
    protected string? _templateState1 { get; set; }
    public string? TemplateState1
    {
        get
        {
            return this._templateState1;
        }
        set
        {
            this._templateState1 = value;
            SendTemplateStateVarModified();
        }
    }
    //项目是否正在运行
    private bool? _pubtaskStatus=false;
    public bool? PubtaskStatus
    {
        get => _pubtaskStatus;
        set
        {
            _pubtaskStatus = value;
            
            SendPubtaskStatusVarModified();
        }
    }

   

    /// <summary>
    /// 主机ID，蠕变多机监控使用
    /// </summary>
    public string? ProjectHostId { get; set; }
    /// <summary>
    /// 项目名称, 目前仅在日志控件中使用
    /// </summary>
    public string ProjectName { get; set; }

    // 映像（逻辑硬件和真实硬件映射关系）
    public MappingResource mappingResource { get; set; }

    // 脚本执行
    public CSharpScriptExecution ScriptExecution { get; protected set; }
    public CSharpScriptExecution ResultScriptExecutor { get; protected init; }

    public enum TemplateState { Ready, Running, Paused, Stopped };
    // 模板的执行状态, 默认为Ready(准备执行)
    public BehaviorSubject<TemplateState> State = new(TemplateState.Ready);

    // stroe 用来存储所有的模板类instance
    // private static readonly Dictionary<string, ITemplate> store;
    private static Dictionary<string, ITemplate> store;
    public bool IsProject { get; protected set; } = false;

    // 模板数据库路径，后续拼接数据库名称
    public string DBPath { get; set; }

    // 临时变量数据结构
    public Dictionary<string, int> iVariable { get; private set; } = new();
    public Dictionary<string, int[]> iVariableArray { get; private set; } = new();
    public Dictionary<string, int[,]> iVariableGrid { get; private set; } = new();
    public Dictionary<string, List<int>> iVariableList { get; private set; } = new();
    public Dictionary<string, double> dVariable { get; private set; } = new();
    public Dictionary<string, double[]> dVariableArray { get; private set; } = new();
    public Dictionary<string, List<double>> dVariableList { get; private set; } = new();
    public Dictionary<string, double[,]> dVariableGrid { get; private set; } = new();
    public Dictionary<string, string> sVariable { get; private set; } = new();
    public Dictionary<string, string[]> sVariableArray { get; private set; } = new();
    public Dictionary<string, List<string>> sVariableList { get; private set; } = new();
    public Dictionary<string, string[,]> sVariableGrid { get; private set; } = new();


    /// <summary>
    /// Model.dVariableQueue["test"] = new FixedSizeQueue(10); //设置队列固定个数，例如10个数据
    /// Model.dVariableQueue["test"].Enqueue(10.0); //添加数据
    /// var average_10 = Model.dVariableQueue["test"].GetAverage(); //求平均值
    /// Model.dVariableQueue["test"].Reset(); // 全部队列数据初始化为0
    /// </summary>
    public Dictionary<string, FixedSizeQueue> dVariableQueue { get; private set; } = new();

    public CcssFunc station { set; get; }
    /// <summary>
    /// 脚本中使用的例子
    /// Model.GetVarByName<TextInputVar>("input_k1ccwxx").Value = "777";
    /// Model.TemplateSaveConfirm();
    /// return false;
    /// </summary>
    public void TemplateSaveConfirm()
    {
        ISystemBus.SendToSaveTemplateOrProject(this.TemplateName);
    }

    /// <summary>
    /// 用于存储趋势过滤特殊点数据(20250228)
    /// </summary>
    public List<Dictionary<string, double>> TFSpecialPointData { get; private set; } = new();

    /// <summary>
    /// 蠕变试验-添加特殊点操作
    /// </summary>
    /// <param name="data"></param>
    public void AddTFSpecialPointData(Dictionary<string, double> data)
    {
        // 删除无用的key
        var nData = new Dictionary<string, double>(data);
        nData.Remove("index");
        TFSpecialPointData.Add(nData);
        // 写入数据库
        TFSpecialPointHandler.InsertSpecialPointData(Db, nData);
    }

    /// <summary>
    /// 蠕变试验-清空特殊点操作
    /// </summary>
    public void ClearTFSpecialPointData()
    {
        TFSpecialPointData.Clear();
        // 清空数据库数据
        TFSpecialPointHandler.DeleteSpecialPointData(Db);
    }

    /// <summary>
    /// 蠕变试验-初始化特殊点数据
    /// </summary>
    public void InitTFSpecialPointData()
    {
        // 读取数据库数据
        var SpecialPointData = TFSpecialPointHandler.GetSpecialPointData(Db);

        if (SpecialPointData.Count > 0)
        {
            TFSpecialPointData = SpecialPointData;
        }
    }

    /// <summary>
    /// 按照同步号分组信息产生.
    /// TODO：
    /// 这个结构是在“同步开始”子任务中添加到该结构中
    /// 同步执行子任务中，某个组的执行结束之后，该同步组中的结构从dic中移除
    /// </summary>
    public Dictionary<int, List<SynchronizedInformation>> SyncGroup { get; set; } =
        new()
        {

        };

    private void _replaceVar(ITemplate newTemplate)
    {
        MappingHardware = newTemplate.MappingHardware;
        DBPath = newTemplate.DBPath;
        SignalVars = newTemplate.SignalVars;
        SampleInsts = newTemplate.SampleInsts;
        CurrentInst = newTemplate.CurrentInst;
        SubTasks = newTemplate.SubTasks;
        Actions = newTemplate.Actions;
        Funcs = newTemplate.Funcs;
        ScriptExecution = newTemplate.ScriptExecution;
        _precompileClass = newTemplate._precompileClass;
    }

    /// <summary>
    /// 当前试样数据
    /// </summary>
    public virtual SampleInst CurrentInst { get; internal set; }

    /// <summary>
    /// 所有选中的试样
    /// </summary>
    public List<SampleInst> SelectedInsts { get; internal set; } = new();

    internal string[] SelectedInstCodes
    {
        get
        {
            return SelectedInsts.Select(s => s.Code).ToArray();
        }
    }

    /// <summary>
    /// 目标试样(结果变量子任务等依赖的试样)
    /// </summary>
    public SampleInst? TargetSampleInst { get; set; }

    private List<SampleInst> _sampleInsts = new List<SampleInst>();

    /// <summary>
    /// 目标试样容器
    /// </summary>
    public List<SampleInst> TargetSampleInsts
    {
        get => _sampleInsts;
        set
        {
            iVariable["InstsTraverseSign"] = 0;
            _sampleInsts = value;
        }
    }

    /// <summary>
    /// 迭代目标试样
    /// </summary>
    /// <param name="iVariableCode">迭代索引的Code</param>
    /// <returns>可以执行本次迭代的时候返回true</returns>
    public bool IterateTargetSampleInsts(string iVariableCode = "InstsTraverseSign")
    {
        if (!iVariable.TryGetValue(iVariableCode, out var i))
            return false;
        if (TargetSampleInsts.Count == 0 || TargetSampleInsts.Count <= i)
            return false;
        TargetSampleInst = TargetSampleInsts[i];
        // i++
        iVariable[iVariableCode] += 1;
        return true;
    }


    /// <summary>
    /// (internal)设置当前试样
    /// </summary>
    /// <param name="instCode">新的当前试样名</param>
    public void SetCurrentInst(string instCode)
    {
        if (SampleInsts.ContainsKey(instCode))
        {
            // CurrentInst = SampleInsts[InstName];
            this.SelectSampleInsts(instCode);
        }
        else
        {
            // TODO: set 不存在的使用 Error => UI
            System.Console.WriteLine($"试样{instCode}不存在");
        }
    }
    /// <summary>
    /// 通知前端当前模板状态
    /// </summary>
    private void SendPubtaskStatusVarModified()
    {
        var parameters = new
        {
            runningStatus = PubtaskStatus
        };
        // 直接序列化为 JsonElement
        JsonElement uiParams = JsonSerializer.SerializeToElement(parameters);
        var uiCmd = new UICmdParams(
            TemplateName,
            null,
            "testRunningStatusChanged",
            uiParams
        );
        // 发送模板状态修改信息，给UI更新数据
        ISystemBus.SendToUIPubtaskStatus(JsonSerializer.Serialize(uiCmd));
    }

    public void SendTemplateStateVarModified()
    {
        int projectId = int.Parse(this.TemplateName[this.TemplateName.Length - 1].ToString());
        var msg = new { projectId = projectId, state = this.TemplateState1 };
        // 模板状态发生变化通知clj,进行持久化
        ISystemBus.SendToVarModified(JsonSerializer.Serialize(msg), new VarModifiedMsgOptions { SaveDB = true, Type = "ProjectState" });
        // 发送模板状态修改信息，给UI更新数据
        ISystemBus.SendToUITemplateState(JsonSerializer.Serialize(msg));
    }
    public virtual MappingHardware MappingHardware { get; internal set; }

    /// <summary>
    /// 信号变量列表
    /// </summary>
    public Dictionary<string, SignalVar> SignalVars { get; protected set; }

    /// <summary>
    /// 全局变量列表
    /// </summary>
    public Dictionary<string, IInputVar> GlobalInputVars { get; protected set; }

    /// <summary>
    /// 试样列表
    /// </summary>
    public Dictionary<string, SampleInst> SampleInsts { get; protected set; }

    /// <summary>
    /// 子任务列表
    /// </summary>
    public Dictionary<string, SubTask> SubTasks { get; protected set; }

    /// <summary>
    /// 动作列表
    /// </summary>
    public Dictionary<string, Action> Actions { get; protected set; }

    /// <summary>
    /// 函数列表
    /// </summary>
    public Dictionary<string, Func<object[], dynamic>> Funcs { get; protected set; }

    /// <summary>
    /// db handler
    /// </summary>
    public virtual DbHandler? Db { get; protected set; }

    /// <summary>
    /// table writers
    /// </summary>
    public ConcurrentDictionary<string, ITableWriter> Writers { get; protected set; }

    /// <summary>
    /// 监控关联管理器数据
    /// </summary>
    public Dictionary<int, GlobalProjectMapping> GlobalProjectMappings { get; protected set; }

    /// <summary>
    /// 结果变量数据
    /// </summary>
    /// <param name="Code"></param>
    /// <param name="Name"></param>
    public record ResultVarData(string Code, string Name);

    /// <summary>
    /// 控件选中的结果变量数据列表
    /// </summary>
    public List<ResultVarData> ControlSelectedResultVars { get; protected set; }

    /// <summary>
    /// 重置控件选中的结果变量数据列表
    /// </summary>
    /// <param name="resultVars"></param>
    public void ResetControlSelectedResultVars(List<ResultVarData> resultVars)
    {
        ControlSelectedResultVars = resultVars;
    }

    /// <summary>
    /// 根据名称获取变量
    /// </summary>
    /// <param name="propertyName">变量名</param>
    /// <returns>变量值</returns>
    public virtual T GetVarByName<T>(string propertyName)
    {
        return (T)this.CurrentInst.InputVars[propertyName];
    }

    public virtual T? GetNullableVarByName<T>(string propertyName)
    {
        if (CurrentInst.InputVars.TryGetValue(propertyName, out var v))
            return (T)v;
        return default;
    }

    /// <summary>
    /// 根据名称获取变量
    /// </summary>
    /// <param name="propertyName">变量名</param>
    /// <param name="sampleName">试样名</param>
    /// <returns>变量值</returns>
    public virtual T GetVarByName<T>(string propertyName, string sampleName)
    {
        return (T)this.SampleInsts[sampleName].InputVars[propertyName];
    }

    /// <summary>
    /// 初始化模板
    /// </summary>
    static ITemplate() =>
    store = new Dictionary<string, ITemplate>();

    /// <summary>
    /// 根据名称获取模板。
    /// </summary>
    /// <param name="Name">模板名称。</param>
    /// <returns>模板对象。</returns>
    public static ITemplate? GetTemplateByName(string Name)
    => store.GetValueOrDefault(Name);
    public static Dictionary<string, ITemplate>  GetStore()
    {
        return store;
    }
    /// <summary>
    /// 存储模板, 如果有以前的模板instance, 将原来模板的临时变量放进新的模板, 再Dispose掉之前的模板
    /// </summary>
    /// <param name="Name">模板名称。</param>
    /// <param name="Template">模板对象。</param>
    public static void StoreTemplate(string Name, ITemplate newTemplate)
    {
        if (store.ContainsKey(Name))
        {
            ITemplate oldTemplate = store[Name];
            // newTemplate._replaceTemporaryVar(oldTemplate);
            oldTemplate._replaceVar(newTemplate);
        }
        else
        {
            // store.Remove(Name);
            store.Add(Name, newTemplate);
        }
        // store.Remove(Name);
        // store.Add(Name, Template);
    }

    /// <summary>
    /// 删除储存中的模板
    /// </summary>
    /// <param name="name">模板名称。</param>
    /// <param name="Template">模板对象。</param>
    public static void RemoveTemplate(string name)
    {
        store.Remove(name);
    }

    private object GetObjectByName(string propertyName)
    {
        Type objectType = this.GetType();
        PropertyInfo property = objectType.GetProperty(propertyName);

        if (property != null)
        {
            object propertyValue = property.GetValue(this);
            return propertyValue;
        }
        return null;
    }

    public dynamic GetFunctionByName(string methodName)
    {
        Type objectType = this.GetType();
        MethodInfo method = objectType.GetMethod(methodName);

        if (method != null)
        {
            // 如果有返回值
            if (method.ReturnType != typeof(void))
            {
                return new Func<object[], dynamic>(parameters => method.Invoke(this, parameters));
            }
            // 如果没有返回值
            else
            {
                return new Action<object[]>(parameters => method.Invoke(this, parameters));
            }
        }

        return null;
    }

    /// <summary>
    /// 查找当前项目下全局code对应的监控关联管理器数据
    /// </summary>
    /// <param name="globalCode"></param>
    /// <returns></returns>
    public GlobalProjectMapping? GetGlobalProjectMapping(string templateName, string globalCode)
        => GlobalProjectMappings.Values
            .FirstOrDefault(item => $"project_{item.ProjectID}" == templateName && item.GlobalCode == globalCode);


    /// <summary>
    /// 设置结果变量索引,修改手动标记标志为true
    /// </summary>
    /// <param name="resultCode"></param>
    public void SetResultIndex(string resultCode, int index)
    {
        this.CurrentInst.ResultVars[resultCode].Index = index;
        this.CurrentInst.ResultVars[resultCode].IsManualMarked = true;
    }
    /// <summary>
    /// 设结果变量手动标记标志
    /// </summary>
    /// <param name="resultCode"></param>
    /// <param name="isMark"></param>
    public void SetIsMark(string resultCode, bool isMark)
    {
        this.CurrentInst.ResultVars[resultCode].IsManualMarked = isMark;
    }
    /// <summary>
    /// 脚本中获取手动标记标志
    /// </summary>
    /// <param name="resultCode"></param>
    /// <returns></returns>
    public bool GetIsMark(string resultCode)
    {
        return this.CurrentInst.ResultVars[resultCode].IsManualMarked;
    }

    public T RunFunc<T>(string FuncName, params object[] args)
    {
        dynamic function = GetFunctionByName(FuncName);
        var x = function(args);
        return x;

    }

    /// <summary>
    /// 根据名称设置变量的值。
    /// </summary>
    /// <param name="propertyName">属性名称。</param>
    /// <param name="value">新的值。</param>
    // public void SetVarByName<T>(string propertyName, T value)
    // {
    //     Type objectType = GetType();
    //     PropertyInfo property = objectType.GetProperty(propertyName);

    //     if (property != null && property.CanWrite)
    //     {
    //         property.SetValue(this, value);
    //     }
    // }

    /// <summary>
    /// 处理资源的清理和释放。
    /// </summary>
    public void Dispose()
    {
        Type objectType = GetType();
        PropertyInfo[] properties = objectType.GetProperties();

        // foreach (PropertyInfo property in properties)
        // {
        //     if (typeof(IDisposable).IsAssignableFrom(property.PropertyType))
        //     {
        //         object observable = property.GetValue(this);
        //         MethodInfo disposeMethod = observable.GetType().GetMethod("Dispose");
        //
        //         if (disposeMethod != null)
        //         {
        //             //FIXME 需要在源头上能够取消订阅
        //             //disposeMethod.Invoke(observable, null);
        //         }
        //     }
        // }

        // 释放daqHandler数据
        foreach (var (_, daqhandler) in DaqHandlerStore)
        {
            daqhandler.Dispose();
        }
        // 释放creepDaqhandler数据
        foreach (var (_, creepDaqhandler) in CreepDaqHandlerStore)
        {
            creepDaqhandler.Dispose();
        }

        // 清除daqHandler缓存数据
        DaqHandlerDataCache.ClearDaqHandlerDataCache(this.TemplateName);

        // 优雅关闭数据库连接
        try
         {
             Logger.Error($"开始释放模板 {TemplateName} 的数据库连接");
             Db?.Dispose();
             
             // 等待文件句柄完全释放
             Thread.Sleep(500);
             Logger.Error($"模板 {TemplateName} 数据库连接释放完成");
         }
         catch (Exception ex)
         {
             Logger.Error($"模板 {TemplateName} 数据库释放过程中发生错误: {ex.Message}");
         }
         
         try
         {
             MappingHardware.Dispose();
         }
         catch (Exception ex)
         {
             Logger.Error($"模板 {TemplateName} 硬件映射释放过程中发生错误: {ex.Message}");
        }

        State.Dispose();
        TemplateObservable = null;

        UtilExtension.Clear();
    }

    /// <summary>
    /// 获取模板中所有指定类型的参数
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public List<T> GetPropertysByType<T>()
    {
        List<T> typeVars = new();
        foreach (PropertyInfo property in this.GetType().GetProperties())
        {
            if (property.PropertyType == typeof(T))
            {
                typeVars.Add((T)property.GetValue(this));
            }
        }

        return typeVars;
    }

    /// <summary>
    /// 修改模板中全局输入变量的值
    /// </summary>
    /// <param name="varName"></param>
    /// <param name="strJson"></param>
    public void UpdateGlobalVar(string varName, string strJson)
    {
        foreach (KeyValuePair<string, SampleInst> kvp in this.SampleInsts)
        {
            SampleInst sampleInst = kvp.Value;
            sampleInst.InputVars[varName].UpdateVarParams(strJson);
        }
    }

    /// <summary>
    /// 模板预编译脚本类
    /// </summary>
    private dynamic _precompileClass { get; set; }

    /// <summary>
    /// 根据函数名称执行预编译脚本, 在函数名不存在时返回false
    /// </summary>
    /// <param name="methodName"></param>
    /// <returns></returns>
    public bool TryRunPrecompileMethod<T>(string methodName, ref T result, params object[] parameters)
    {
        if (this.ScriptExecution.Error)
        {
            // Logger.Error("脚本预编译错误: " + this.scriptExecution.Error);
            // Logger.Error(this.scriptExecution.ErrorMessage);
            // Logger.Error(this.scriptExecution.GeneratedClassCodeWithLineNumbers);
            ISystemBus.SendToUIscriptPrecompileErrorTopic("预编译脚本执行失败: " + this.ScriptExecution.ErrorMessage, this.ScriptExecution.GeneratedClassCodeWithLineNumbers);
        }
        if (null == this._precompileClass) return false;
        Type classType = _precompileClass!.GetType();
        MethodInfo methodInfo = classType.GetMethod(methodName);


        if (null == methodInfo)
        {
            return false;
        }
        object methodResult = this.ScriptExecution.InvokeMethod(this._precompileClass, methodName, parameters);

        if (methodResult is T)
            result = (T)methodResult;

        return true;
    }

    /// <summary>
    /// 预编译实例化提供脚本, 并赋值给预编译脚本类(_precompileClass)
    /// </summary>
    /// <param name="scripts"></param>
    protected void CompileSctipts(Dictionary<string, (string, string)> scripts)
    {
        List<string> classMethods = new List<string>();

        foreach (KeyValuePair<string, (string, string)> script in scripts)
        {
            string methodName = script.Key;
            string resultType = Type.GetType(script.Value.Item1).FullName;
            string code = script.Value.Item2;

            classMethods.Add($"public {resultType} {methodName} (ITemplate Model)" +
                                   Environment.NewLine +
                                   "{\n" +
                                   code +
                                   Environment.NewLine +
                                   // force a return value - compiler will optimize this out
                                   // if the code provides a return
                                   (!code.Contains("return ")
                                       ? "return default;" + Environment.NewLine
                                       : string.Empty) +
                                   "}");
        }

        if (classMethods.Count != 0)
        {
            this._precompileClass = this.ScriptExecution.CompileClass(
                this.ScriptExecution.GenerateClass(string.Join(Environment.NewLine, classMethods)).ToString());
            if (this.ScriptExecution.Error)
            {
                Logger.Error("脚本预编译错误: " + this.ScriptExecution.Error);
                Logger.Error(this.ScriptExecution.ErrorMessage);
                Logger.Error(this.ScriptExecution.GeneratedClassCodeWithLineNumbers);
                ISystemBus.SendToUIscriptPrecompileErrorTopic("脚本预编译错误: " + this.ScriptExecution.ErrorMessage, this.ScriptExecution.GeneratedClassCodeWithLineNumbers);

            }
        }
    }

    public string CompileSctipt(string script, string resultType, string module)
    {
        string head = "";
        string parameters = "(ITemplate Model, List<Dictionary<string, double[]>> cycles = null)";
        switch (module)
        {
            case "SignalVar":
                head = "Func<string, double[]> GetSignalData = (signalName) => block[signalName];";
                        //  "Func<string, double[]> GetSignalData = (signalName) => {\n" +
                         //  "    if (block is Consts.FlatCDataBlock fblock)\n" +
                         //  "        return Model.SignalVars[signalName].GetSignalData(fblock, Model);\n" +
                         //  "    else if (block is Consts.CDataBlock cblock)\n" +
                         //  "        return Model.SignalVars[signalName].GetSignalData(cblock, Model);\n" +
                         //  "    else\n" +
                         //  "        return new double[0];\n" +
                         //  "};\n" ;
                parameters = "(Dictionary<string,double[]> block, ITemplate Model)";
                resultType = "System.Double[]";
                break;

            case "ResultVar":
                parameters = "(ITemplate Model, params string[] parameters)";
                resultType = "dynamic";
                break;

            case "SampleInst":
                parameters = "(SampleInst thisInst)";
                resultType = "System.Double";
                break;

            default:
                switch (resultType)
                {
                    case "BOOL": resultType = "System.Boolean"; break;
                    case "STRING": resultType = "System.String"; break;
                    case "FLOAT": resultType = "System.Double"; break;
                    case "INT": resultType = "System.Int32"; break;

                    default: resultType = "System.Boolean"; break;
                }
                break;
        }
        string method = $"public {resultType} _compileMethods " + parameters +
                                   Environment.NewLine +
                                   "{\n" +
                                   head +
                                   Environment.NewLine +
                                   script +
                                   Environment.NewLine +
                                   // force a return value - compiler will optimize this out
                                   // if the code provides a return
                                   (!script.Contains("return ")
                                       ? "return default;" + Environment.NewLine
                                       : string.Empty) +
                                   "}";
        this.ScriptExecution.CompileClass(this.ScriptExecution.GenerateClass(method).ToString());
        if (this.ScriptExecution.Error) return this.ScriptExecution.ErrorMessage + Environment.NewLine + this.ScriptExecution.GeneratedClassCodeWithLineNumbers;

        return "Success";
    }

    /// <summary>
    /// 项目数据库初始化
    ///     1. 初始化DbHandler
    /// </summary>
    protected void DbInit()
    {

    }
    /// <summary>
    /// 整个模板用到的封装的全部信号变量的Observable
    /// </summary>
    public virtual IObservable<Dictionary<string, double>> TemplateObservable { get; protected set; }
    //线程安全
    public ConcurrentDictionary<string, DaqHandler> DaqHandlerStore = new();
    public ConcurrentDictionary<string, CreepDaqHandler> CreepDaqHandlerStore = new();
    public ConcurrentDictionary<string, DynamicDaqHandler> DynamicHandlerStore = new();
    /// <summary>
    /// DAQ子任务调用该函数创建数据处理流<br/>
    /// DAQ将采集存库等参数传递到DaqHarder中，由DaqHandler创建数据处理流<br/>
    /// </summary>
    /// <param name="key">在daq中以选择参数提供</param>
    /// <param name="processid">流程图ID，key拼接上此参数,使用同一个Observable</param>
    /// <param name="parameters"></param>
    public virtual void CreateOrUpdateDaqHandler(
         string key,
         string processid,
         DaqHandlerParameters parameters)
    {  
        var uniHanderKey = $"{key}{processid}";
        //添加或更新数据采集处理器
        DaqHandlerStore.AddOrUpdate(uniHanderKey,
         _=>new DaqHandler(parameters),
         (_,item)=> {
             item.Update(parameters);
             return item;
             }
         );
    }

    /// <summary>
    /// 动态的数据采集类
    /// </summary>
    /// <param name="key"></param>
    /// <param name="processid"></param>
    /// <param name="parameters"></param>
    public virtual void CreateOrUpdateDynamicDaqHandler(
     string key,
     string processid,
     DynamicDaqHandlerParameters parameters)
    {
        lock (DynamicHandlerStore)
        {
            var uniHanderKey = $"{key}{processid}";
            //添加或更新动态数据采集处理器
            DynamicHandlerStore.AddOrUpdate(uniHanderKey,
            _ => new DynamicDaqHandler(parameters),
            (_, item) =>
            {
                item.Update(parameters);
                return item;
            }
            );
        }
    }

    /// <summary>
    /// 蠕变daq创建
    /// CreepDAQ子任务调用该函数创建数据处理流<br/>
    /// CreepDAQ将采集存库等参数传递到CreepDaqHarder中，由CreepDaqHarder创建数据处理流<br/>
    /// </summary>
    /// <param name="key">在creepdaq中以选择参数提供</param>
    /// <param name="processid">流程图ID，key拼接上此参数,使用同一个Observable</param>
    /// <param name="parameters"></param>
    public virtual void CreateOrUpdateCreepDaqHandler(
         string key,
         string processid,
         CreepDaqHandlerParameters parameters)
    {
        var uniHanderKey = $"{key}{processid}";
        //添加或更新蠕变数据采集处理器
        CreepDaqHandlerStore.AddOrUpdate(uniHanderKey,
         _=>new CreepDaqHandler(parameters),
         (_,item)=> {
             item.Update(parameters);
             return item;
             }
         );
    }

    /// <summary>
    /// 1. 在DAQ的Abort函数中调用该函数，释放对应的数据处理流
    /// 2. SubTaskEnd子任务中调用位置调用该函数释放该流程图下所有的数据处理流
    /// 3. SubTaskBasicTask子任务的Abort 函数中调用位置调用该函数释放该流程图下所有的数据处理流
    /// </summary>
    /// <param name="keyWithprocessId">DAQkey+流程图ID</param>
    public virtual void RemoveDaqHandler(string keyWithprocessId)
    {
        var keysToRemove = DaqHandlerStore.Keys
                            .Where(k => k.EndsWith(keyWithprocessId))
                            .ToList();
        Logger.Error($"待删除: {keyWithprocessId} keysToRemoveCount :{keysToRemove.Count}");
        Logger.Error($"当前共：{DaqHandlerStore.Count}个handler");
        foreach (var item in keysToRemove)
        {
            if (DaqHandlerStore.TryGetValue(item, out DaqHandler handler))
            {
                handler.Dispose();
                DaqHandlerStore.Remove(item, out _);
            }
            else
            {
                Logger.Error($"keyWithprocessId: {item} 未找到");
            }
        }

        // 蠕变DAQ清除处理
        var CreepKeysToRemove = CreepDaqHandlerStore.Keys
                            .Where(k => k.EndsWith(keyWithprocessId))
                            .ToList();
        foreach (var item in CreepKeysToRemove)
        {
            if (CreepDaqHandlerStore.TryGetValue(item, out CreepDaqHandler handler))
            {
                handler.Dispose();
                CreepDaqHandlerStore.Remove(item, out _);

            }
        }
        Logger.Info($"当前共：{CreepDaqHandlerStore.Count}个 CreepHandler");

        var keysDynamicToRemove = DynamicHandlerStore.Keys
                        .Where(k => k.EndsWith(keyWithprocessId))
                        .ToList();
        foreach (var item in keysDynamicToRemove)
        {
            if (DynamicHandlerStore.TryGetValue(item, out var handler))
            {
                handler.Dispose();
                DynamicHandlerStore.Remove(item, out _);

            }
        }
        Logger.Info($"当前共：{DynamicHandlerStore.Count}个 DynamicHandlerStore");
    }
}

public static class JsonExtensions
{
    public static int? GetNullableInt(this JsonElement e, String key)
    {
        JsonElement NullableInt = e.GetProperty(key);
        if (NullableInt.ValueKind == JsonValueKind.Number)
        {
            return NullableInt.GetInt32();
        }
        else return null;
    }
}

public record Template : ITemplate
{
    /// <summary>
    /// 模板实例化
    /// </summary>
    /// <param name="jsonString">模板的jsonString</param>
    public Template(string jsonString)
    {

        Logger.Info("开始构造template");
        JsonDocument doc = JsonDocument.Parse(jsonString);
        JsonElement root = doc.RootElement.GetProperty("Param");

        this.TemplateName = root.GetProperty("template_name").GetString();
        this._templateState1 = root.GetProperty("state").GetString();
        this.ProjectHostId = root.GetProperty("hostId").GetString();
        this.ProjectName = root.GetProperty("project_name").GetString();
        this.DBPath = root.GetProperty("db_path").GetString() ?? "";

        IsProject = TemplateName.StartsWith("project");
        if (IsProject) Db = new DbHandler(this);
        // 实例化硬件
        MappingHardware = new MappingHardware(root.GetProperty("hardware"), this);
        // 构造映像数据
        this.mappingResource = new MappingResource(this.TemplateName, root.GetProperty("mapping_resource"));
        // 构造全局变量
        this.GlobalInputVars = new();
        // Console.WriteLine("全局变量jsonelement" + root.GetProperty("global_input_vars"));
        // Console.WriteLine("全局变量数组" + root.GetProperty("global_input_vars").EnumerateArray());
        foreach (JsonElement element in root.GetProperty("global_input_vars").EnumerateArray())
        {
            var inputVar = InputVarFactory.CreateInputVar(element);
            this.GlobalInputVars[inputVar.Code] = inputVar;
        }

        // 构造试样数据实例(输入变量 结果变量)
        SampleInsts = new Dictionary<string, SampleInst>();
        var dbResultVars = DbResultVar.GetDbResultVars(this);
        foreach (var element in root.GetProperty("samples").EnumerateArray())
        {
            var sampleInstCode = element.GetProperty("sample_code").GetString();
            dbResultVars.TryGetValue(sampleInstCode, out var instResultVars);
            var sampleInst = new SampleInst(element, this, GlobalInputVars, instResultVars);
            SampleInsts[sampleInst.Code] = sampleInst;
        }
        // 项目时初始化数据库
        if (IsProject)
        {
            Db.InitializeDb(this);
            // 建立数据库连接
            Writers = new ConcurrentDictionary<string, ITableWriter>();
            Db.StartWriter();
        }

        // 选中试样
        this.SelectSampleInsts(root.GetProperty("current_sample").GetString()!);

        // 子任务
        SubTasks = SubTaskUtils.GetSubTasks(root, this.TemplateName);

        // 动作
        Actions = new Dictionary<string, Action>();
        foreach (var element in root.GetProperty("actions").EnumerateArray())
        {
            var actionName = element.GetProperty("action_name").GetString();
            var actionSubTasks = SubTaskUtils.GetSubTasks(element, this.TemplateName);

            Actions[actionName] = new Action(actionName, TemplateName, actionSubTasks);
        }

        // 全局监控关联管理器数据
        this.GlobalProjectMappings = new();
        foreach (var element in root.GetProperty("global_project_mappings").EnumerateArray())
        {
            var id = element.GetProperty("id").GetInt32();
            GlobalProjectMappings[id] = new GlobalProjectMapping(element);
        }

        // 控件选中的结果变量数据
        this.ControlSelectedResultVars = new();
        foreach (var element in root.GetProperty("control_selected_result_vars").EnumerateArray())
        {
            ControlSelectedResultVars.Add(new ResultVarData(element.GetProperty("code").GetString()!, element.GetProperty("name").GetString()!));
        }


        // 函数 TODO待定 不知道结构
        this.Funcs = new();
        // this.scriptExecution = new ();
        // this.scriptExecution.AddDefaultReferencesAndNamespaces();

        // 信号变量
        this.SignalVars = new();
        // 预编译虚拟变量脚本
        CSharpScriptExecution signalScriptExecutor = new CSharpScriptExecution() { SaveGeneratedCode = true, IgnoreHashCheck = true };
        signalScriptExecutor.AddDefaultReferencesAndNamespaces();

        // 构造信号变量
        foreach (var signalVar in root.GetProperty("signal_vars").EnumerateArray().Where(x => x.GetProperty("code").GetString() != null)
        .Select(element => new SignalVar(element, TemplateName)))
        {
            SignalVars[signalVar.Code] = signalVar;
        }

        // 脚本预编译
        Dictionary<string, (string, string)> compileSctipts = new Dictionary<string, (string, string)>();

        this.ScriptExecution = new CSharpScriptExecution { SaveGeneratedCode = true, IgnoreHashCheck = true };
        this.ScriptExecution.AddDefaultReferencesAndNamespaces();

        foreach (var element in root.GetProperty("precompile_scripts").EnumerateObject())
        {
            string methodName = element.Name;
            string resultType = element.Value.GetProperty("result_type").GetString()!;
            string code = element.Value.GetProperty("script").GetString()!;
            // 预编译脚本
            compileSctipts[methodName] = (resultType, code);
        }
        var stopwatch = new Stopwatch();
        //int iwatch = 0;
        
        // 用于监控CPU占用率
        var process = System.Diagnostics.Process.GetCurrentProcess();
        var cpuCounterStartTime = DateTime.UtcNow;
        var startCpuUsage = process.TotalProcessorTime;
        
        IObservable<Dictionary<string, double>> observables = GetAllSignal();
        // 包含所有信号变量的流，将流中数据拿出不等待同步
        TemplateObservable = observables
        // CombineLatest(dicts =>
        // {
        //     stopwatch.Start();
        //     // 将每个字典中的键值对平铺并转换为新的字典
        //     iwatch++;
        //     var dic = dicts.SelectMany(d => d)
        //      .ToDictionary(g => g.Key, g => g.Value);
        //     stopwatch.Stop();

        //     if (iwatch % 100000 == 0)
        //     {
        //         iwatch = 0;
        //         // 计算CPU占用率
        //         var endCpuUsage = process.TotalProcessorTime;
        //         var endTime = DateTime.UtcNow;
        //         var cpuUsedMs = (endCpuUsage - startCpuUsage).TotalMilliseconds;
        //         var totalPassed = (endTime - cpuCounterStartTime).TotalMilliseconds;
        //         var cpuUsagePercent = cpuUsedMs / (Environment.ProcessorCount * totalPassed) * 100;

        //         // 重置CPU计数器
        //         cpuCounterStartTime = endTime;
        //         startCpuUsage = endCpuUsage;

        //         string msg = $"100000次总流字典转换耗时： {stopwatch.ElapsedMilliseconds}ms, CPU占用率: {cpuUsagePercent:F2}%";

        //         // 记录字典大小
        //         msg += $", 字典大小: {dic.Count}";

        //         if (dic.TryGetValue("signal_cycle", out double cycle))
        //         {
        //             msg += $", cycle: {cycle}";
        //         }
        //         if (dic.TryGetValue("signal_time", out double time))
        //         {
        //             msg += $", time: {time}";
        //         }
        //         Logger.Error(msg);
        //         stopwatch.Reset();

        //     }
        //     return dic;
        // }
        // )
        .Publish().RefCount()
        // 切换线程
        .ObserveOn(System.Reactive.Concurrency.TaskPoolScheduler.Default);

        // 编译脚本
        this.CompileSctipts(compileSctipts);

        // 初始化特殊点数据
        InitTFSpecialPointData();

        this.station = new CcssFunc(this, TemplateName);
    }

    /// <summary>
    /// 销毁非托管资源
    /// </summary>
    public new void Dispose()
    {
        base.Dispose();
    }

    private IObservable<Dictionary<string, double>> GetAllSignal()
    {
         List<SignalVar> VRSignals = new List<SignalVar>(
            SignalVars.Values.Where(x => x.IsVirtual == true)
        );

        IObservable<Dictionary<string, double>> ret = GetHWSignal().SelectMany(mainData =>
        {
            // 先将硬件信号数据复制到临时字典
            Dictionary<string, double[]> allSignalData = new Dictionary<string, double[]>(mainData);

            // 计算虚拟信号变量（基于原始硬件信号数据）
            foreach (var item in VRSignals)
            {
                allSignalData.Add(item.Code, item.GetVirtualSignalData(allSignalData, this));
            }

            // 统一进行数据长度填充处理（包括硬件信号和虚拟信号）
            var maxLength = allSignalData.Values.Where(arr => arr != null).Max(arr => arr.Length);
            if (maxLength == 0) maxLength = 1; // 确保至少有一个元素

            Dictionary<string, double[]> paddedData = new Dictionary<string, double[]>();

            // 对所有信号（硬件信号+虚拟信号）进行长度填充
            foreach (var kvp in allSignalData)
            {
                if (kvp.Value == null || kvp.Value.Length == 0)
                {
                    paddedData[kvp.Key] = new double[maxLength];
                }
                else if (kvp.Value.Length < maxLength)
                {
                    var newArray = new double[maxLength];
                    Array.Copy(kvp.Value, newArray, kvp.Value.Length);
                    // 用最后一个值填充不足的部分
                    var lastValue = kvp.Value.LastOrDefault();
                    for (int i = kvp.Value.Length; i < maxLength; i++)
                    {
                        newArray[i] = lastValue;
                    }
                    paddedData[kvp.Key] = newArray;
                }
                else
                {
                    paddedData[kvp.Key] = kvp.Value;
                }
            }

            var results = paddedData
                        .First()
                        .Value.Select(
                            (_value, index) =>
                            {
                                Dictionary<string, double> daqSignalVars = new();
                                foreach (var item in paddedData.Keys)
                                {
                                    double nullAbleValue =
                                        GetElementAtIndex(paddedData.GetValueOrDefault(item), index) ?? 0;
                                    daqSignalVars[item] = nullAbleValue;
                                }
                                return daqSignalVars;
                            }
                        );
            return results.ToObservable();
        });
        return ret;
    }
    /// <summary>
    /// 获取所有信号变量（延后转换优化版本）
    /// </summary>
    /// <returns></returns>
    private IObservable<Dictionary<string, double[]>> GetHWSignal()
    {
        List<SignalVar> templateSignalVars = new List<SignalVar>(
       SignalVars.Values.Where(x => !string.IsNullOrEmpty(x.HwKey) && x.IsVirtual == false)
   );
        // 处理为：如果某个硬件不存在，则不订阅，所以这里要去读取配置文件subtasks.json，查看当前硬件信息
        List<string> uniqueHwKeys = GetHwkeyFromSubTasksJsonFile(out string mainStreamKey);

        //= templateSignalVars
        //    .Select(signal => signal.HwKey)
        //    .Distinct()
        //    .ToList()!;
        IEnumerable<string> signalWithHwKey = new List<string>();

        // 分离主流和辅助流
        IObservable<Dictionary<string, double[]>>? mainStream = null;
        var auxiliaryStreams = new List<IObservable<Dictionary<string, double[]>>>();

        foreach (string uniqueHwKey in uniqueHwKeys)
        {
            if (string.IsNullOrEmpty(uniqueHwKey))
                continue;

            signalWithHwKey = signalWithHwKey.Concat(
                templateSignalVars
                    .Where(singal => uniqueHwKey.Equals(singal.HwKey!))
                    .Select(signal => signal.Code)
            );

            // 直接返回Dictionary<string, double[]>格式的数据，延后处理转换和填充
            var streamData = SignalVarsData.GetDatasFlat(uniqueHwKey!, this)
                .Select(x =>
                {
                    // 只过滤需要的信号变量，保持double[]格式
                    Dictionary<string, double[]> subVars = x.Where(kvp =>
                            signalWithHwKey.Contains(kvp.Key)
                            || kvp.Key == "create_time"|| kvp.Key == "1"
                        )
                        .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
                    
                    return subVars;
                });

            // 判断是主流还是辅助流
            if (uniqueHwKey.Equals(mainStreamKey))
            {
                mainStream = streamData;
            }
            else
            {
                auxiliaryStreams.Add(streamData);
            }
        }

        // 如果没有主流，回退到原来的Merge方式,各个硬件的数据分别推送，采集时只会记录标识信号变量所在硬件的数据
        if (mainStream == null)
        {
            var allStreams = auxiliaryStreams;
            if (allStreams.Count == 0)
            {
                // 如果没有任何流，返回空的Observable
                return Observable.Empty<Dictionary<string, double[]>>();
            }

            var source = allStreams.Merge();

            // 每个流初始化一个数据，这个数据会被后面的过滤条件过滤掉
            var initData = new Dictionary<string, double[]>();
            foreach (var item in signalWithHwKey)
            {
                initData[item] = new double[] { 0.0 }; // 初始值设为包含0的数组
            }
            initData["create_time"] = new double[] { 0.0 };

            return source.StartWith(initData);
        }

        // 使用主流驱动的数据合并方式，保留辅助流的所有数据
        // 为每个辅助流创建数据队列来缓存未被主流消费的数据
        var auxiliaryDataQueues = auxiliaryStreams.Select(_ => new Queue<Dictionary<string, double[]>>()).ToList();
        var latestAuxiliaryData = auxiliaryStreams.Select(_ =>
        {
            var initData = new Dictionary<string, double[]>();
            foreach (var item in signalWithHwKey)
            {
                initData[item] = new double[] { 0.0 };
            }
            initData["create_time"] = new double[] { 0.0 };
            return initData;
        }).ToList();

        // 订阅每个辅助流，将数据存入对应的队列
        for (int i = 0; i < auxiliaryStreams.Count; i++)
        {
            int index = i; // 避免闭包问题
            auxiliaryStreams[index].Subscribe(data =>
            {
                auxiliaryDataQueues[index].Enqueue(data);
                // 如果队列为空，使用最后一次的数据
                // 需要从每个double[]中取最后一个double值
                var auxData = new Dictionary<string, double[]>();
                foreach (var kvp in data)
                {
                    if (kvp.Value != null && kvp.Value.Length > 0)
                    {
                        // 取double[]的最后一个值，构造新的单元素数组
                        auxData[kvp.Key] = new double[] { kvp.Value[kvp.Value.Length - 1] };
                    }
                    else
                    {
                        auxData[kvp.Key] = new double[] { 0.0 };
                    }
                }
                latestAuxiliaryData[index] = auxData;
            });
        }

        // 主流也需要初始值
        var initMainData = new Dictionary<string, double[]>();
        foreach (var item in signalWithHwKey)
        {
            initMainData[item] = new double[] { 0.0 };
        }
        initMainData["create_time"] = new double[] { 0.0 };

        var mainStreamWithInit = mainStream.StartWith(initMainData);

        // 如果没有辅助流，直接返回主流
        if (auxiliaryStreams.Count == 0)
        {
            return mainStreamWithInit;
        }

        // 主流驱动的数据合并，每次主流推送数据时，消费辅助流队列中的数据
        return mainStreamWithInit.Select(mainData =>
        {
            var combined = new Dictionary<string, double[]>(mainData);

            // 处理每个辅助流的数据队列
            for (int i = 0; i < auxiliaryDataQueues.Count; i++)
            {
                Dictionary<string, double[]> auxData;
                if (auxiliaryDataQueues[i].Count > 0)
                {
                    // 使用最新数据并删除已用数据
                    auxData = auxiliaryDataQueues[i].Dequeue();
                }
                else
                {
                    // 如果队列为空，使用最后一次的数据
                    // 需要从每个double[]中取最后一个double值
                    auxData = new Dictionary<string, double[]>();
                    foreach (var kvp in latestAuxiliaryData[i])
                    {
                        if (kvp.Value != null && kvp.Value.Length > 0)
                        {
                            // 取double[]的最后一个值，构造新的单元素数组
                            auxData[kvp.Key] = new double[] { kvp.Value[kvp.Value.Length - 1] };
                        }
                        else
                        {
                            auxData[kvp.Key] = new double[] { 0.0 };
                        }
                    }
                }

                // 合并辅助流数据，但主流数据优先
                foreach (var kvp in auxData)
                {
                    if (!combined.ContainsKey(kvp.Key))
                    {
                        combined[kvp.Key] = kvp.Value;
                    }
                }
            }

            return combined;
        });
    }

    /// <summary>
    /// 读取subtasks.json文件，获取硬件连接器标志
    /// </summary>
    /// <param name="mainStreamKey">返回主流的Key</param>
    /// <returns></returns>
    private static List<string> GetHwkeyFromSubTasksJsonFile(out string mainStreamKey)
    {
        string assemblyPath = Assembly.GetExecutingAssembly().Location;
        string outputPath = Path.GetDirectoryName(assemblyPath)!;
        string subtaskJsonFile = Path.Combine(outputPath, "subtasks.json");
        var list = new List<string>();
        mainStreamKey = string.Empty;
        
        if (File.Exists(subtaskJsonFile))
        {
            JsonDocument document = JsonDocument.Parse(File.ReadAllText(subtaskJsonFile));
            var root = document.RootElement;

            // 获取主流Key
            if (root.TryGetProperty("mainStreamKey", out JsonElement sendKeyElement))
            {
                mainStreamKey = sendKeyElement.GetString() ?? string.Empty;
            }

            var hardwareConnectors = root.GetProperty("hardwareConnectors");
            foreach (JsonElement item in hardwareConnectors.EnumerateArray())
            {
                string name = item.GetProperty("name").GetString()!;
                list.Add(name);
            }
        }
        return list;
    }

    private static double? GetElementAtIndex(double[]? array, int index)
    {
        if (array == null)
        {
            return null;
        }
        else if (index >= 0 && index < array.Length)
        {
            return array[index];
        }
        else
        {
            return null;
        }
    }
}

/// <summary>
/// daqhandler传参
/// </summary>
/// <param name="template">模板实例</param>
/// <param name="timeInterval">时间间隔</param>
/// <param name="bufferInputVar">buffer</param>
/// <param name="isTimeInterval">是否启用时间过滤</param>
/// <param name="subTaskID">子任务ID</param>
/// <param name="simpleCode">试样code</param>
/// <param name="isSendUI">是否推送UI</param>
/// <param name="isSaveBuffer">是否存储到buffer</param>
/// <param name="isSaveDb">是否存DB</param>
/// <param name="intervalTimeCode">时间信号变量code</param>
/// <param name="isMoveInterval">是否启用位移过滤</param>
/// <param name="moveInterval">位移间隔</param>
/// <param name="intervalMoveCode">位移信号变量code</param>
/// <param name="isLoadInterval">是否启用负荷过滤</param>
/// <param name="loadInterval">负荷间隔</param>
/// <param name="intervalLoadCode">负荷信号变量code</param>
public record DaqHandlerParameters
    (
        ITemplate Template,
        double TimeInterval,
        BufferInputVar BufferInputVar,
        bool IsTimeInterval,
        string SubTaskID,
        string? SimpleCode,
        bool IsSendUI,
        bool IsSaveBuffer,
        bool IsSaveDb,
        string IntervalTimeCode,
        bool IsMoveInterval,
        double MoveInterval,
        string IntervalMoveCode,
        bool IsLoadInterval,
        double LoadInterval,
        string IntervalLoadCode,
        bool IsStarinInterval,
        double StrainInteral,
        string IntervalStrainCode,
        bool timeResetZero,
        string signal_timeCode,
        bool IsValidDataFilterEnabled,
        Func<bool, bool>? AboutFunc
    )
{

};

/// <summary>
/// creepDaqHandler 传参
/// </summary>
/// <param name="Template">模板实例</param>
/// <param name="BufferInputVar">buffer</param>
/// <param name="SimpleCode">试样code</param>
/// <param name="SubTaskID">子任务ID</param>
/// <param name="DeviceStatusCodes">蠕变设备状态码结构</param>
/// <param name="LoadTimeSignalCode">加载时间信号变量code</param>
/// <param name="StartTimeSignalCode">启动时间信号变量code</param>
/// <param name="LoadNumberSignalCode">加载段数信号变量code</param>
/// <param name="WaveNumberSignalCode">波形次数信号变量code</param>
/// <param name="WYSignalCode">位移实时值的信号变量code</param>
/// <param name="FHSignalCode">负荷实时值的信号变量code</param>
/// <param name="BXSignalCode">变形实时值的信号变量code</param>
/// <param name="DeviceStatusSignalCode">InSignals信号变量code</param>
/// <param name="BlockCyclesSignalCode">大循环信号变量code</param>
/// <param name="TempSignalCodes">温度信号变量codes</param>
/// <param name="SaveDataMode">全局存盘策略参数-存储方式</param>
/// <param name="CycleInterval">全局存盘策略参数-按照大循环间隔存盘-循环间隔数</param>
/// <param name="TimePeriodsParams">全局存盘策略参数-按照时间段存盘- 时间段</param>
/// <param name="WaveParams">自定义波形（非自定义波形）</param>
/// <param name="IsChoice">预负荷存盘策略参数-是否勾选 预负荷存盘</param>
/// <param name="IsTempFluctuate">预负荷存盘策略参数-是否勾选 温度波动存盘</param>
/// <param name="InitialTemp">预负荷存盘策略参数-初始温度</param>
/// <param name="FluctuateTemp">预负荷存盘策略参数-波动度</param>
/// <param name="TimeInterval">预负荷存盘策略参数-指定时间间隔</param>
/// <param name="IsSaveBuffer">是否存储到buffer</param>
/// <param name="IsGlobalSave">是否全局存盘</param>
/// <param name="IsSendToUI">是否推送UI</param>
/// <param name="IsSaveDB">是否存DB</param>
/// <param name="LastIndex">数据库当前Index</param>
public record CreepDaqHandlerParameters
    (
        ITemplate Template,
        BufferInputVar BufferInputVar,
        string? SimpleCode,
        string SubTaskID,
        Dictionary<int, DeviceStatusCode>? DeviceStatusCodes,
        string? LoadTimeSignalCode,
        string? StartTimeSignalCode,
        string? LoadNumberSignalCode,
        string? WaveNumberSignalCode,
        string? WYSignalCode,
        string? FHSignalCode,
        string? BXSignalCode,
        string? DeviceStatusSignalCode,
        string? BlockCyclesSignalCode,
        List<string>? TempSignalCodes,
        string? SaveDataMode,
        int CycleInterval,
        DoubleArray? TimePeriodsParams,
        List<Dictionary<string, dynamic>>? WaveParams,
        bool IsChoice,
        bool IsTempFluctuate,
        int InitialTemp,
        int FluctuateTemp,
        double TimeInterval,
        bool IsSaveBuffer,
        bool IsGlobalSave,
        bool IsSendToUI,
        bool IsSaveDB,
        long LastIndex
    )
{

};

/// <summary>
///  动态数据采集参数
/// </summary>
/// <param name="ClassName">用来区分当前的采集是周期数据采集还是峰谷值采集</param>
/// <param name="Template">模板实例</param>
/// <param name="BufferInputVar">输入变量</param>
/// <param name="SubTaskID">子任务ID</param>
/// <param name="SimpleCode">试样code</param>
/// <param name="IsSaveDb">是否存储到db</param>
/// <param name="IsExceptionData">是否采集异常数据</param>
/// <param name="ExceptionDataUpperLimit">上限，超过该值为异常数据</param>
/// <param name="ExceptionDataLowerLimit">下限，低于该值为异常数据</param>
/// <param name="ExceptionDataPrecision">判断是否为异常数据的精度</param>
/// <param name="CollectMode">过滤点的方式</param>
/// <param name="CollectFrequency">过滤方式参数，最终为计算出每间隔N个点采集一个</param>
/// <param name="FinalCollectedCycles">最终要采集的周期数</param>
/// <param name="FirstCollectedCycles">开始要采集的周期数</param>
/// <param name="IsIntervalCollected">是否需要间隔N个周期采集</param>
/// <param name="IntervalCollectedCycles">间隔的周期数</param>
/// <param name="IsSpecifyCycle">是否采用指定周期采集</param>
/// <param name="SpecifyCycle">需要采集的指定周期</param>
/// <param name="FrequencySignalCode">频率信号变量Code</param>
/// <param name="DaqRate">采集频率，在CCSSInfo的ADStuct信息中，用来计算采集间隔点</param>
/// <param name="ActiveCtrlSignalCode">控制模式对应的信号变量code</param>
/// 
public record DynamicDaqHandlerParameters(
    string ClassName,
    ITemplate Template,
    BufferInputVar BufferInputVar,
    string SubTaskID,
    string? SimpleCode,
    bool IsSaveDb,
    bool IsExceptionData,
    double ExceptionDataUpperLimit,
    double ExceptionDataLowerLimit,
    double ExceptionDataPrecision,
    string CollectMode,
    double CollectFrequency,
    ulong FinalCollectedCycles,
    ulong FirstCollectedCycles,
    bool IsIntervalCollected,
    ulong IntervalCollectedCycles,
    bool IsSpecifyCycle,
    ulong[] SpecifyCycle,
    string FrequencySignalCode,
    int DaqRate,
    string ActiveCtrlSignalCode,
    string CycleSignalCode,
    bool LogarithmicCollect,
    bool IsFinalCollected,
    bool IsFirstCollected,
    bool  _isDoubleArray,
    string _doubleArrayCode
);