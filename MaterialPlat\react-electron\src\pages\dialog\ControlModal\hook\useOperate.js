import React, { useMemo } from 'react'
import useWidget from '@/hooks/useWidget'
import {
    saveShortcut, savePassage, delWidget, addStaticCurve, actionTab
} from '@/utils/services'
import useShortcut from '@/hooks/useShortcutList'
import useHeader from '@/hooks/useHeader'
import useTableConfig from '@/hooks/useTableConfig'
import {
    VIEW_TYPE
} from '@/utils/layoutConstants'
import { TABLE_TYPE, DONGTAI_CONFIG_KEY } from '@/utils/constants'

import { useDispatch, useSelector } from 'react-redux'
import {
    SPLIT_LAYOUT_WIDGET
} from '@/redux/constants/split'
import { handleTabData } from '@/utils/splitData'
import useStaticCurve from '@/hooks/useStaticCurve'
import { handleAPIData } from '@/pages/staticCurve/constants'
import { TAB_LAYOUT, TAB_DISPLAY } from '@/pages/layout/tabFixed/constants'
import useArrayCurveConfig from '@/hooks/useArrayCurveConfig'
import useDynamicCurve from '@/hooks/useDynamicCurve'
import useGuide from '@/hooks/useGuide'
import useDongtaiConfigs from '@/hooks/useDongtaiConfigs'
import {
    headerParam, shortcutParam, statusBarParam, initTabData
} from '../constant'

const useOperate = () => {
    const dispatch = useDispatch()

    const settingResList = useSelector(state => state.staticCurve.settingResList)
    const currentPageId = useSelector(state => state.template.currentPageId)
    const arrayCurveConfigList = useSelector(state => state.template.arrayCurveConfigList)
    const dynamicCurveParams = useSelector(state => state.template.dynamicCurveParams)

    const { addWidget, initWidget } = useWidget()
    const { getConfigPidList, addOrUpdateConfig } = useDongtaiConfigs()
    const { initShortcutData } = useShortcut()
    const { createArrayCurveConfig } = useArrayCurveConfig()
    const { initHeaderData } = useHeader()
    const { getStaticCurveSettingList } = useStaticCurve()
    const { initTableConfigData, saveTableConfigData } = useTableConfig()
    const { initDynamicCurveList, saveDynamicCurve } = useDynamicCurve()
    const { initGuideData } = useGuide()

    // 每个控件初始化数据
    const initDataFuncMap = useMemo(() => {
        return {
            [VIEW_TYPE.SHORTCUT]: initShortcutData,
            [VIEW_TYPE.HEADER]: initHeaderData,
            [VIEW_TYPE.SAMPLE_TABLE]: initTableConfigData,
            [VIEW_TYPE.SAMPLE_STATISTIC_TABLE]: initTableConfigData,
            [VIEW_TYPE.LIGHTNING_LINE_CHART]: getStaticCurveSettingList,
            [VIEW_TYPE.GAOZHOU_CURVE]: initDynamicCurveList,
            [VIEW_TYPE.DYNAMIC_FORM]: initGuideData
        }
    }, [])

    const baseParam = (widget, name) => {
        return {
            current_id: widget?.current_id,
            parent_id: widget?.widget_id,
            widget_type: widget?.widget_type,
            widget_name: name
        }
    }
    /**
     * 快捷方式
     * 1. 快捷方式有个全局的配置信息，放到了widget.data_source 里面
     * 2. data_source里面有shortcut_ids字段用于存储shortcut_id，
     * 3. shortcut_id需要调用 saveShortcut保存每个快捷，再关联到当前控件
     * @param {*} widget
     * @param {*} name
     */
    const saveShortcutFunc = async (widget, name) => {
        try {
            const parma = { ...shortcutParam, shortcut_id: crypto.randomUUID() }
            const res = await saveShortcut({
                shortcuts: [parma]
            })
            if (res) {
                await addWidget({
                    ...baseParam(widget, name),
                    data_source: {
                        bg_style: 'corner',
                        logo_flag: true,
                        logo_position: 'right',
                        logo: '',
                        shortcut_ids: [parma.shortcut_id]
                    }
                })
                initShortcutData()
            }
        } catch (error) {
            console.error(error)
        }
    }
    /**
     * 表头
     * 目前表头，首先要新增一个表头配置，再关联到当前控件
     * @param {*} widget
     * @param {*} name
     */
    const saveHeaderOk = async (widget, name) => {
        try {
            const id = crypto.randomUUID()
            const res = await savePassage({ ...headerParam, id })
            if (res) {
                await addWidget({
                    ...baseParam(widget, name),
                    data_source: id
                })
                initHeaderData()
            }
        } catch (error) {
            console.log(error)
        }
    }

    /**
     * 新增表格
     * 1. 有2个控件经过此函数，【结果表格，和结果统计表格】希望后续新增的表格配置都可以用这个
     * 2. 目前表格，首先要新增一个表格配置，再关联到当前控件
     * @param {*} widget
     * @param {*} name
     */
    const saveTableEdit = async (widget, name) => {
        const id = crypto.randomUUID()
        const res = await saveTableConfigData({
            id,
            type: widget?.widget_type === VIEW_TYPE.SAMPLE_STATISTIC_TABLE ? TABLE_TYPE.STATISTICS : TABLE_TYPE.RESULT
        })
        if (res) {
            await addWidget({
                ...baseParam(widget, name),
                data_source: id
            })
            initTableConfigData()
        }
    }

    /**
     * 新增静态
     * 目前静态曲线，首先要新增一个曲线配置，再关联到当前控件
     * @param {*} widget
     * @param {*} name
     */
    const saveStaticCurve = async (widget, name) => {
        const res = await addStaticCurve({
            ...handleAPIData(settingResList.find(
                item => item.id === widget.data_source
            )),
            curve_name: name
        })
        if (res) {
            await addWidget({
                ...baseParam(widget, name),
                data_source: res
            })
            getStaticCurveSettingList()
        }
    }

    /**
     * 新增状态栏
     * 目前状态栏的配置信息，存储在widget.data_source 无须新增其他表
     * @param {*} widget
     * @param {*} name
     */
    const saveStatusBar = async (widget, name) => {
        await addWidget({
            ...baseParam(widget, name),
            data_source: statusBarParam
        })
    }

    // 删除
    const handelDel = async (widget) => {
        const ids = {
            [VIEW_TYPE.SHORTCUT]: (widget?.data_source instanceof Array) ? widget?.data_source : widget?.data_source?.shortcut_ids,
            [VIEW_TYPE.HEADER]: [widget?.data_source],
            [VIEW_TYPE.SAMPLE_STATISTIC_TABLE]: [widget?.data_source],
            [VIEW_TYPE.SAMPLE_TABLE]: [widget?.data_source],
            [VIEW_TYPE.LIGHTNING_LINE_CHART]: [],
            [VIEW_TYPE.ARRAY_CURVE]: [widget?.data_source],
            [VIEW_TYPE.TAB_FIXED]: widget?.data_source?.binder_ids ?? [],
            [VIEW_TYPE.GAOZHOU_CURVE]: [widget?.data_source],
            [VIEW_TYPE.PID面板]: [widget?.data_source],
            [VIEW_TYPE.二维数组表格]: [],
            [VIEW_TYPE.DYNAMIC_FORM]: widget?.data_source?.guide_ids ?? []
        }
        if (ids) {
            // 如果加了，请让后台新增加了的删除相关逻辑
            await delWidget({
                widget_id: widget.widget_id,
                type: widget.widget_type,
                ids: ids[widget.widget_type] ?? []
            })
            initWidget()
        }
        dispatch({ type: SPLIT_LAYOUT_WIDGET, param: null })
        if (initDataFuncMap[widget.widget_type]) {
            initDataFuncMap[widget.widget_type]()
        }
    }

    // 保存活页夹
    const saveTabFixed = async (widget, name) => {
        const binders = [initTabData(currentPageId)]
        const res = await actionTab({
            binders: binders.map(i => {
                return handleTabData(i, i.id)
            })
        })
        if (res) {
            await addWidget({
                ...baseParam(widget, name),
                data_source: {
                    binder_ids: binders.map(m => m.id),
                    display: TAB_DISPLAY.中,
                    direction: TAB_LAYOUT.纵向
                }
            })
        }
    }

    // 新增二维数组曲线
    const saveArrayCurve = async (widget, name) => {
        const currentConfig = arrayCurveConfigList.find(
            item => item.curve_id === widget.data_source
        )
        const res = await createArrayCurveConfig({
            ...currentConfig,
            auxiliary: currentConfig.auxiliary ?? [],
            marker: currentConfig.marker ?? []
        })
        if (res) {
            await addWidget({
                ...baseParam(widget, name),
                data_source: res
            })
        }
    }

    // 动态高周曲线
    const saveGaozhouCurve = async (widget, name) => {
        // 新建不传id 进行新增
        const { id, ...currentConfig } = dynamicCurveParams.find(
            item => String(item.id) === widget.data_source
        )
        await saveDynamicCurve({
            ...currentConfig,
            ...baseParam(widget, name)
        })
        initWidget()
        initDynamicCurveList()
    }

    // 保存pid配置
    const savePidPanel = async (widget, name) => {
        const pidSettingConfig = (await getConfigPidList())?.find(f => f.config_id === widget.data_source) || {}
        delete pidSettingConfig?.config_id
        console.log(pidSettingConfig)

        const res = await addOrUpdateConfig(pidSettingConfig)
        if (res) {
            await addWidget({
                ...baseParam(widget, name),
                data_source: res
            })
        }
    }
    const saveDoubleArrayTable = (widget, name) => {
        addWidget({
            ...baseParam(widget, name),
            data_source: ''
        })
    }
    const saveGuide = (widget, name) => {
        addWidget({
            ...baseParam(widget, name),
            data_source: ''
        })
    }

    const saveSlider = (widget, name, data_source = '') => {
        addWidget({
            ...baseParam(widget, name),
            data_source
        })
    }

    // 每个控件的新增函数对象
    const funcMap = useMemo(() => {
        return {
            [VIEW_TYPE.SHORTCUT]: saveShortcutFunc,
            [VIEW_TYPE.HEADER]: saveHeaderOk,
            [VIEW_TYPE.SAMPLE_TABLE]: saveTableEdit,
            [VIEW_TYPE.SAMPLE_STATISTIC_TABLE]: saveTableEdit,
            [VIEW_TYPE.LIGHTNING_LINE_CHART]: saveSlider,
            [VIEW_TYPE.FOOTER]: saveStatusBar,
            [VIEW_TYPE.TAB_FIXED]: saveTabFixed,
            [VIEW_TYPE.ARRAY_CURVE]: saveArrayCurve,
            [VIEW_TYPE.GAOZHOU_CURVE]: saveGaozhouCurve,
            [VIEW_TYPE.PID面板]: savePidPanel,
            [VIEW_TYPE.进度条]: saveSlider,
            [VIEW_TYPE.特殊表头]: saveSlider,
            [VIEW_TYPE.蠕变分屏表头]: saveSlider,
            [VIEW_TYPE.二维数组表格]: saveSlider,
            [VIEW_TYPE.SUB_TASK_PARAM]: saveSlider,
            [VIEW_TYPE.LOG]: saveSlider,
            [VIEW_TYPE.DYNAMIC_FORM]: saveGuide
        }
    }, [])

    return {
        saveShortcutFunc,
        saveHeaderOk,
        saveTableEdit,
        saveStaticCurve,
        handelDel,
        funcMap,
        initDataFuncMap
    }
}

export default useOperate
