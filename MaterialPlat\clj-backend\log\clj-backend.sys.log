2025-09-03 09:10:21,087 [main] INFO  clj-backend.core - -main running ~~ 
2025-09-03 09:10:21,393 [main] INFO  clj-backend.env - 
-=[clj-backend started successfully using the development profile]=- 
2025-09-03 09:10:22,320 [main] INFO  clj-backend.core - Starting monitoring server on port 8181 
2025-09-03 09:10:22,659 [main] INFO  io.undertow - starting server: Undertow - 2.2.20.Final 
2025-09-03 09:10:22,677 [main] INFO  org.xnio - XNIO version 3.8.7.Final 
2025-09-03 09:10:22,868 [main] INFO  org.jboss.threads - JBoss Threads version 3.1.0.Final 
2025-09-03 09:10:22,996 [main] INFO  luminus.http-server - server started on port 3000 
2025-09-03 09:10:22,999 [main] INFO  clj-backend.nrepl - starting nREPL server on port 7000 
2025-09-03 09:10:23,016 [main] INFO  clj-backend.core - #'clj-backend.db.connections/*db-connections started 
2025-09-03 09:10:23,016 [main] INFO  clj-backend.core - #'clj-backend.common.template-utils/connect-template started 
2025-09-03 09:10:23,016 [main] INFO  clj-backend.core - #'clj-backend.events/project-life-cycle-channel started 
2025-09-03 09:10:23,017 [main] INFO  clj-backend.core - #'clj-scheduler.mq/schedulers started 
2025-09-03 09:10:23,017 [main] INFO  clj-backend.core - #'clj-backend.modules.script.service/precompile-scripts started 
2025-09-03 09:10:23,017 [main] INFO  clj-backend.core - #'clj-backend.common.trial/end-time started 
2025-09-03 09:10:23,017 [main] INFO  clj-backend.core - #'clj-backend.common.trial/end-time-time-timestamp started 
2025-09-03 09:10:23,017 [main] INFO  clj-backend.core - #'clj-backend.modules.project.project-service/project-event-listener started 
2025-09-03 09:10:23,017 [main] INFO  clj-backend.core - #'clj-backend.modules.inspection.inspection-record-routes/generate-inspection-record started 
2025-09-03 09:10:23,017 [main] INFO  clj-backend.core - #'clj-backend.modules.inspection.inspection-record-routes/notify-inspect started 
2025-09-03 09:10:23,017 [main] INFO  clj-backend.core - #'clj-backend.handler/init-app started 
2025-09-03 09:10:23,017 [main] INFO  clj-backend.core - #'clj-backend.handler/app-routes started 
2025-09-03 09:10:23,017 [main] INFO  clj-backend.core - #'clj-scheduler.core/scheduler-socket-server started 
2025-09-03 09:10:23,017 [main] INFO  clj-backend.core - #'clj-backend.core/monitoring-server started 
2025-09-03 09:10:23,018 [main] INFO  clj-backend.core - #'clj-backend.core/http-server started 
2025-09-03 09:10:23,018 [main] INFO  clj-backend.core - #'clj-backend.core/repl-server started 
