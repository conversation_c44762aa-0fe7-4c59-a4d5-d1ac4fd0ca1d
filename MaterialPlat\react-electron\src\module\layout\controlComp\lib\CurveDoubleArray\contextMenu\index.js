/* eslint-disable new-cap */
import React, { useState } from 'react'
import { useSelector } from 'react-redux'
import { jsPDF } from 'jspdf'
import { message } from 'antd'
import { isEqual } from 'lodash'

import RightMenu from '@/components/contextMenu2/index'
import useCopy from '@/hooks/useCopy'
import { COPY_TYPE } from '@/utils/constants'
import ExportModal from '@/components/ExportModal/index'
import { getExportCSVDoubelArray, getHardwareMapping } from '@/utils/services'
import { getProcessID } from '@/utils/utils'
import store from '@/redux/store/index'

import { SOURCE_TYPE } from '../constants/constants'
import useColumnsSource from '../setting/hooks/useColumnsSource'

const ContextMenu = ({
    domId,
    setOpen,
    layoutConfig,
    config, isBufferCurve,
    openCross, setOpen<PERSON>ross,
    openBreak, setOpenBreak,
    showPointTag, setShowPointTag,
    showChunkTag, setShowChunkTag,
    openBreakPoint, setOpenBreakPoint,
    onRestore,
    onClearBreakPoint,
    isLocked, setIsLocked,
    isMarking, onActivateMarking, onStopMarking
}) => {
    const openExperiment = useSelector(state => state.subTask.openExperiment)
    const systemConfig = useSelector(state => state.global.systemConfig)
    const stationList = useSelector(state => state.global.stationList)
    const cfgList = useSelector(state => state.global.cfgList)
    const projectList = useSelector(state => state.system.projectList)
    const projectId = useSelector(state => state.project.projectId)

    const channels = useColumnsSource({ sourceType: config?.base?.sourceType, sourceInputCode: config?.base?.sourceInputCode, isBufferCurve })

    const { copy } = useCopy()
    const [uploadModalOpen, setUploadModalOpen] = useState(false)

    // 打印曲线图
    const printCurve = () => {
        const dom = document.getElementById(domId)
        const domCanvas = dom.querySelector('canvas')
        const pdf = new jsPDF({ orientation: 'l' })
        pdf.addImage(domCanvas.toDataURL('image/jpg'), 'PNG', 0, 0, pdf.internal.pageSize.getWidth(), pdf.internal.pageSize.getHeight())
        pdf.save('curve.pdf')
    }

    const copyClipboard = () => {
        const dom = document.getElementById(domId)
        const domCanvas = dom.querySelector('canvas')
        domCanvas.toBlob(blob => {
            copy(blob, COPY_TYPE.图片)
        })
    }

    const options = [
        {
            label: '设置曲线',
            visible: true,
            feature: true,
            onClick: () => setOpen(true),
            line: true
        },
        {
            label: !isLocked ? '锁定' : '解锁',
            visible: openExperiment, // 试验中才能使用
            feature: true,
            onClick: () => setIsLocked(!isLocked)
        },
        {
            label: '恢复',
            visible: true,
            feature: true,
            onClick: () => onRestore?.(),
            line: true
        },
        {
            label: '拷贝到剪贴板',
            visible: !openExperiment,
            feature: true,
            onClick: () => {
                copyClipboard()
            }

        },
        {
            label: '打印曲线图',
            visible: !openExperiment,
            feature: true,
            onClick: () => {
                printCurve()
            }
        },
        {
            label: '导出csv',
            visible: !openExperiment && !isBufferCurve,
            feature: true,
            onClick: () => {
                if (config.base.sourceInputCode) {
                    setUploadModalOpen(true)
                } else {
                    message.error('未选择数据源')
                }
            }
        },
        {
            label: openCross ? '关闭十字线' : '激活十字线',
            visible: !openExperiment,
            // 不能操作：开启断裂点
            feature: !openBreak && !isMarking,
            onClick: () => setOpenCross(!openCross)
        },
        {
            label: openBreak ? '关闭设置断裂点' : '设置断裂点',
            visible: !openExperiment,
            feature: !openCross && !isMarking,
            onClick: () => setOpenBreak(!openBreak)
        },
        {
            label: '撤销断裂点',
            visible: !openExperiment && openBreakPoint,
            feature: !openCross && !isMarking,
            onClick: () => {
                onClearBreakPoint?.()
                setOpenBreakPoint(false)
            }
        },
        {
            label: isMarking ? '停止手工标记' : '激活手工标记',
            visible: !openExperiment,
            feature: !openCross && !openBreak,
            onClick: isMarking ? onStopMarking : onActivateMarking
        },
        {
            label: showPointTag ? '隐藏标签' : '显示标签',
            visible: !openExperiment,
            feature: true,
            onClick: () => {
                // 同步更新配置中的pointTag的open属性
                setShowPointTag(!showPointTag)
            }
        },
        {
            label: showChunkTag ? '隐藏标签块' : '显示标签块',
            visible: !openExperiment,
            feature: true,
            line: true,
            onClick: () => {
                // 同步更新配置中的chunkTag的open属性
                setShowChunkTag(!showChunkTag)
            }
        }
    ].filter(i => !!i && i.visible)

    const handleUploadModalOk = async (path, fileName) => {
        // 这里可以添加实际的导出逻辑
        setUploadModalOpen(false)

        const { cfgId } = await getHardwareMapping()

        const { stationId } = cfgList.find(c => c.cfgId === cfgId)

        const { stationName } = stationList.find(s => s.id === stationId)

        const { project_name } = projectList.find(p => p.project_id === Number(projectId))

        const basePath = path.at(-1) === '\\' ? path : `${path}\\`

        const exportPath = `${basePath}${stationName}_${stationId}\\${project_name}_${projectId}\\`

        const getDataCodes = () => {
            if (!config?.curveGroup) return []

            const dimensionList = store.getState().global.unitList

            const codes = []

            const format = (signalCode, unitId) => {
                const unit = dimensionList?.map(i => i.units)?.flat()?.find(i => i.id === unitId)

                return {
                    code: signalCode,
                    name: channels.find(c => c.code === signalCode)?.name,
                    unit: unit?.name ?? '',
                    proportion: unit?.proportion ?? 1
                }
            }

            Object.values(config.curveGroup).forEach(curveItem => {
                // 只处理启用的曲线
                if (curveItem.isEnable) {
                    const xParam = format(curveItem.xSignal, curveItem.xUnit)
                    // 添加x轴信号
                    if (curveItem.xSignal && codes.every(c => !isEqual(xParam, c))) {
                        codes.push(format(curveItem.xSignal, curveItem.xUnit))
                    }

                    // 遍历每一条线 取y轴信号和单位
                    Object.values(curveItem.curves).forEach(dataSource => {
                        dataSource.lines.forEach(l => {
                            const yParam = format(l.code, l.yUnit)
                            if (codes.every(c => !isEqual(yParam, c))) {
                                codes.push(yParam)
                            }
                        })
                    })
                }
            })

            return codes
        }

        await getExportCSVDoubelArray({
            templateName: getProcessID(),
            arrayCode: config.base.sourceInputCode,
            codes: getDataCodes(),
            path: exportPath,
            fileName,
            type: config.base.sourceType === SOURCE_TYPE.单数据源 ? 'DoubleArray' : 'DoubleArrayList'
        })
    }

    return (
        <>
            <RightMenu
                domId={domId}
                options={options}
                layoutConfig={layoutConfig}
                capture
            />
            <ExportModal
                open={uploadModalOpen}
                title="导出csv"
                defaultPath={systemConfig?.project_directory}
                onOk={handleUploadModalOk}
                onCancel={() => {
                    setUploadModalOpen(false)
                }}
            />
        </>
    )
}

export default ContextMenu
