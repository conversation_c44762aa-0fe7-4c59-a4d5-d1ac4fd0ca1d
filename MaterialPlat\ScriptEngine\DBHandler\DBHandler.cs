using System.Collections.Concurrent;
using System.Data;
using System.Threading;

using DBUtils;
using DocumentFormat.OpenXml.Drawing.Diagrams;
using Microsoft.Data.Sqlite;
using ScriptEngine.DBHandler.TableWriters;
using ScriptEngine.InputVar;
using ScriptEngine.InputVar.InputVars;
using Scripting;
using Logging;

namespace ScriptEngine.DBHandler;

/// <summary>
///  项目数据库处理程序:
///     1. 属性 'Connection' 缓存项目数据库的连接
///     2. 属性 'Writers' 维护数据库中的 TableWriter对象,
///         并持有一个数据库写入线程, 循环调用其中 TableWriter对象 的 Write
/// </summary>
public class DbHandler : IDisposable
{
    /// <summary>
    /// 数据库写入线程的运行标志
    /// </summary>
    private bool IsWriting { get; set; } = true;
    /// <summary>
    /// 数据库连接
    /// </summary>
    public virtual SqliteConnection Connection { get; }

    /// <summary>
    /// TODO: 临时方案，区分主线程和writer线程的数据库连接，以避免事务的嵌套
    /// </summary>
    private SqliteConnection _connection;
    /// <summary>
    /// 维护数据库writer
    /// </summary>
    private ConcurrentBag<ITableWriter> Writers { get; }

    private Task? _writerTask;

    /// <summary>
    /// 数据库写入线程间隔
    /// </summary>
    private int Interval { get; }


        // ------------------------ 读连接池相关字段（并发读优化） ------------------------
        private readonly ConcurrentBag<SqliteConnection> _readPool = new(); // 读连接池
        private int _initialReadConns = 2;     // 初始读连接数（可配置）
        private int _maxReadConns = 8;         // 最大读连接数（可配置）
        private int _activeReaders = 0;        // 活跃读计数
        private int _poolCount = 0;            // 池内连接总数（含借出）
        private volatile bool _isDisposing = false; // 处理处置阶段标志
        private TimeSpan _disposeWait = TimeSpan.FromSeconds(10); // 处置等待超时
        private readonly string _connectionString = string.Empty; // 统一的连接字符串

        /// <summary>
        /// 对连接应用性能优化 PRAGMA（与构造函数中一致）
        /// </summary>
        private void ApplySqliteOptimizations(SqliteConnection conn)
        {
            using var command = conn.CreateCommand();
            // 启用内存映射，提高读写性能（设置为64MB）
            command.CommandText = "PRAGMA mmap_size=67108864;";
            command.ExecuteNonQuery();
            // 使用WAL模式提高写入性能
            command.CommandText = "PRAGMA journal_mode=WAL;";
            command.ExecuteNonQuery();
            // 降低同步级别，提高写入速度（对于机械硬盘，NORMAL是性能和安全的平衡点）
            command.CommandText = "PRAGMA synchronous=NORMAL;";
            command.ExecuteNonQuery();
            // 增加缓存大小，减少磁盘访问（设置为8MB）
            command.CommandText = "PRAGMA cache_size=-8000;";
            command.ExecuteNonQuery();
            // 将临时表和索引存储在内存中
            command.CommandText = "PRAGMA temp_store=MEMORY;";
            command.ExecuteNonQuery();
        }

        /// <summary>
        /// 创建一个新的读连接并应用优化
        /// </summary>
        private SqliteConnection CreateReadConnection()
        {
            var conn = new SqliteConnection(_connectionString);
            conn.Open();
            ApplySqliteOptimizations(conn);
            return conn;
        }

        /// <summary>
        /// 从读连接池借出连接；当池为空时在不超过上限的情况下新建连接
        /// </summary>
        private SqliteConnection RentReadConnection(out bool isPooled)
        {
            isPooled = true;
            if (_isDisposing) throw new ObjectDisposedException(nameof(DbHandler));

            if (_readPool.TryTake(out var conn))
            {
                CCSSLogger.Logger.Debug($"[DbHandler] Rent pooled conn. active={_activeReaders}, poolCount={_poolCount}, bag={_readPool.Count}");
                return conn;
            }

            var newCount = Interlocked.Increment(ref _poolCount);
            if (newCount <= _maxReadConns)
            {
                try
                {
                    CCSSLogger.Logger.Debug($"[DbHandler] Create pooled conn. newPoolCount={newCount}");
                    return CreateReadConnection();
                }
                catch (Exception ex)
                {
                    Interlocked.Decrement(ref _poolCount);
                    CCSSLogger.Logger.Error($"[DbHandler] Create pooled conn failed: {ex.Message}");
                    throw;
                }
            }

            // 超过上限：降级为一次性连接（不计入池内）
            Interlocked.Decrement(ref _poolCount);
            isPooled = false;
            CCSSLogger.Logger.Warn("[DbHandler] Pool exhausted. Use one-shot connection.");
            return CreateReadConnection();
        }

        /// <summary>
        /// 归还读连接到连接池；一次性连接或处置阶段则直接销毁
        /// </summary>
        private void ReturnReadConnection(SqliteConnection conn, bool isPooled)
        {
            if (conn == null) return;

            if (!isPooled)
            {
                try { conn.Close(); conn.Dispose(); } catch { }
                CCSSLogger.Logger.Debug("[DbHandler] Return one-shot conn disposed.");
                return;
            }

            if (_isDisposing || conn.State != ConnectionState.Open)
            {
                try { conn.Dispose(); } catch { }
                Interlocked.Decrement(ref _poolCount);
                CCSSLogger.Logger.Debug("[DbHandler] Return pooled conn disposed (disposing or closed).");
                return;
            }

            _readPool.Add(conn);
            CCSSLogger.Logger.Debug($"[DbHandler] Return pooled conn. poolCount={_poolCount}, bag={_readPool.Count}");
        }

    /// <summary>
    /// 增加一个 tableWriter对象
    /// </summary>
    /// <param name="tableWriter">tableWriter对象</param>
    public void AddWriter(ITableWriter tableWriter)
    {
        Writers.Add(tableWriter);
    }

    /// <summary>
    /// 构造数据库处理程序
    /// </summary>
    /// <param name="template">模板对象</param>
    /// <param name="interval">线程处理间隔(默认100ms)</param>
    public DbHandler(ITemplate template, int interval = 100)
    {
        Interval = interval;
        Writers = new ConcurrentBag<ITableWriter>();

        // 统一连接字符串，便于读连接池复用
        _connectionString = $"Data Source={template.DBPath};Pooling=False;";

        // 主读连接（仅用于必要的管理性操作，不再用于并发读）
        Connection = new SqliteConnection(_connectionString);
        Connection.Open();
        ApplySqliteOptimizations(Connection);

        // 写连接（writer 线程使用）
        _connection = new SqliteConnection(_connectionString);
        _connection.Open();
        ApplySqliteOptimizations(_connection);

        // 预热读连接池
        for (var i = 0; i < _initialReadConns; i++)
        {
            try { _readPool.Add(CreateReadConnection()); Interlocked.Increment(ref _poolCount); }
            catch { /* 预热失败不致命，略过 */ }
        }
    }

    /// <summary>
    /// 无参构造函数，仅用于单元测试中的模拟
    /// </summary>
    protected DbHandler()
    {
        Interval = 100;
        Writers = new ConcurrentBag<ITableWriter>();
        Connection = null!;
        _connection = null!;
    }

    /// <summary>
    /// 启动数据库写入线程
    ///     当运行标志置为 false 之后线程结束
    /// </summary>
    public void StartWriter()
    {
        _writerTask = Task.Run(() =>
        {
            try
            {
                while (IsWriting)
                {
                    Thread.Sleep(Interval);
                    if (Writers.IsEmpty) continue;
                    foreach (var writer in Writers)
                    {
                        writer.Write(_connection);
                    }
                }

            }
            catch (Exception e)
            {
                Logging.CCSSLogger.Logger.Error("数据库Handler写入错误导致的中断");
                Logging.CCSSLogger.Logger.Error(e.Message);
                Logging.CCSSLogger.Logger.Error(e.StackTrace);
                // throw;
            }
        });
    }

    /// <summary>
    /// 销毁Db处理程序对象
    ///     1. 关闭线程
    ///     2. 释放连接
    ///     3. 执行WAL检查点确保数据完整性
    /// </summary>
    public void Dispose()
    {
        // 标记处置开始，禁止新的借出
        _isDisposing = true;
        IsWriting = false;

        // 等待写入任务完成，增加超时保护
        try
        {
            var completed = _writerTask?.Wait(TimeSpan.FromSeconds(10)) ?? true;
            if (!completed) CCSSLogger.Logger.Error("写入任务未在超时时间内完成，强制关闭");
        }
        catch (Exception ex)
        {
            CCSSLogger.Logger.Error($"等待写入任务完成时发生错误: {ex.Message}");
        }

        // 等待活跃读清零
        try
        {
            var start = DateTime.UtcNow;
            while (Interlocked.CompareExchange(ref _activeReaders, 0, 0) != 0 && DateTime.UtcNow - start < _disposeWait)
            {
                CCSSLogger.Logger.Debug($"[DbHandler] Disposing... activeReaders={_activeReaders}");
                Thread.Sleep(10);
            }
            if (Interlocked.CompareExchange(ref _activeReaders, 0, 0) != 0)
            {
                CCSSLogger.Logger.Warn($"[DbHandler] Wait for active readers timed out. active={_activeReaders}");
            }
        }
        catch (Exception ex)
        {
            CCSSLogger.Logger.Error($"等待活跃读清零时发生错误: {ex.Message}");
        }

        // 回收读连接池
        try
        {
            while (_readPool.TryTake(out var rconn))
            {
                try { rconn.Close(); rconn.Dispose(); } catch { }
                Interlocked.Decrement(ref _poolCount);
            }
            CCSSLogger.Logger.Info($"[DbHandler] Read pool cleared. poolCount={_poolCount}, bag={_readPool.Count}");
        }
        catch (Exception ex)
        {
            CCSSLogger.Logger.Error($"回收读连接池时发生错误: {ex.Message}");
        }

        // 执行 WAL 检查点，确保数据写入主数据库
        try
        {
            if (Connection?.State == ConnectionState.Open)
            {
                using var cmd = Connection.CreateCommand();
                cmd.CommandText = "PRAGMA wal_checkpoint(TRUNCATE);";
                cmd.ExecuteNonQuery();
                CCSSLogger.Logger.Error("WAL检查点执行完成");
            }
        }
        catch (Exception ex)
        {
            CCSSLogger.Logger.Error($"WAL检查点执行失败: {ex.Message}");
        }

        // 关闭连接
        try
        {
            Connection?.Close();
            Connection?.Dispose();
        }
        catch (Exception ex)
        {
            CCSSLogger.Logger.Error($"关闭主连接时发生错误: {ex.Message}");
        }

        try
        {
            _connection?.Close();
            _connection?.Dispose();
        }
        catch (Exception ex)
        {
            CCSSLogger.Logger.Error($"关闭写入连接时发生错误: {ex.Message}");
        }

        GC.SuppressFinalize(this);
    }

    // -----------------------------------------------------------------------------------------------------------------
    /// <summary>
    /// 重置 dbHandler 中的 Writers
    /// </summary>
    /// <param name="tableWriters"></param>
    public void ResetWriters(ITableWriter[] tableWriters)
    {
        while (Writers.TryTake(out _))
        {
        }

        foreach (var tableWriter in tableWriters)
            Writers.Add(tableWriter);
    }

    // -----------------------------------------------------------------------------------------------------------------
    /*  ===========================================
     动态数据库操作:
        TODO: 当前在每个位置调用dbHandler的函数, 后期如果为模板中每个修改增加event 可能把这里改为事件的处理程序更好
        1. 初始化
        2. 试样增加
        3. 试样删除
        4. 试样开始
        5. buffer增加
        6. buffer删除
        7. buffer修改
     ===========================================   */

    /// <summary>
    /// 初始化项目数据库:
    ///     1. 检查数据库中是否有table_record表
    ///         没有时创建 table_record表 和 动态数据表
    /// </summary>
    public void InitializeDb(ITemplate template)
    {
        if (!Connection.QueryTableExisting("table_record"))
        {
            Connection.InitDynamicDataTable(template);
        }

    }

    /// <summary>
    /// 开始实验
    /// </summary>
    /// <param name="sampleInst"></param>
    public void Start(SampleInst.SampleInst sampleInst) => Connection.ResetSampleInstTable(sampleInst);
    /// <summary>
    /// 重置buffer中的数据
    /// </summary>
    /// <param name="sampleCode">试样Code</param>
    /// <param name="bufferCode">BufferCode</param>
    public virtual void RestartBuffer(string sampleCode, string bufferCode) => Connection.ResetTable(sampleCode, bufferCode);

    /// <summary>
    /// 创建试样
    /// </summary>
    public void CreateSampleInst(SampleInst.SampleInst sampleInst) => Connection.CreateSampleInstTable(sampleInst);

    /// <summary>
    /// 删除试样时关联的操作
    /// </summary>
    public void DeleteSampleInst(SampleInst.SampleInst sampleInst) => Connection.DropSampleInstTable(sampleInst);

    public void Vacuum () => Connection.Vacuum();

    /// <summary>
    /// 创建Buffer
    /// </summary>
    /// <param name="buffer"></param>
    /// <param name="template"></param>
    public void CreateBuffer(BufferInputVar buffer, ITemplate template) => Connection.CreateBufferTable(buffer, template);
    public void CreateBuffer(BufferInputVar buffer, SampleInst.SampleInst sampleInst) => Connection.CreateBufferTable(buffer, sampleInst);

    /// <summary>
    /// 更新Buffer
    /// </summary>
    public void UpdateBuffer(BufferInputVar buffer, ITemplate template) => Connection.UpdateBufferInstTable(buffer, template);
    // -----------------------------------------------------------------------------------------------------------------
    /// <summary>
    /// 查询表
    /// </summary>
    public DynamicDataTable[] GetTables(SampleInst.SampleInst sampleInst) =>
        Connection.SelectDynamicDataTableBySample(sampleInst.Code);
    public long GetDataByTime(string sampleCode, string bufferCode, long targetTime) =>
        Connection.GetDataByTime(sampleCode, bufferCode, targetTime);
       
    public long GetDataByIndex(string sampleCode, string bufferCode, int index) =>
        Connection.GetDataByIndex(sampleCode, bufferCode, index);

    /// <summary>
    /// 查询表
    /// </summary>
    public DynamicDataTable GetTable(string sampleInstCode, string bufferInputVarCode) =>
        Connection.SelectDynamicDataTable(sampleInstCode, bufferInputVarCode);

    /// <summary>
    /// 获取单个纬度的历史数据
    /// </summary>
    public IEnumerable<double> GetHistoricalData(string tableName, string signalCode)
    {
        if (_isDisposing) return Array.Empty<double>();
        bool pooled = true;
        Interlocked.Increment(ref _activeReaders);
        SqliteConnection? conn = null;
        var results = new List<double>();
        try
        {
            conn = RentReadConnection(out pooled);
            var quotedSignalCodes = $"\"{signalCode}\"";
            var sql = $"SELECT {quotedSignalCodes} FROM {tableName};";
            using var command = new SqliteCommand(sql, conn);
            using var reader = command.ExecuteReader();
            while (reader.Read())
            {
                results.Add(reader.GetDouble(0));
            }
            return results;
        }
        catch
        {
            return Array.Empty<double>();
        }
        finally
        {
            if (conn != null) ReturnReadConnection(conn, pooled);
            Interlocked.Decrement(ref _activeReaders);
        }
    }
    /// <summary>
    /// 获取多个个纬度的历史数据,用起始终止index获取，startIndex=0,endIndex=100表示获取1-100行数据
    /// </summary>
    /// <param name="tableName">表名（sampleCode+bufferCode）</param>
    /// <param name="signalCodes">List<string>信号变量Codes</param>
    /// <param name="startIndex">起始Index</param>
    /// <param name="endIndex">结束Index</param>
    public IEnumerable<double[]> GetHistoricalDataOnlyDouble(string tableName,  List<string> signalCodes , int startIndex, int endIndex)
    {
        if (_isDisposing) return Array.Empty<double[]>();
        bool pooled = true;
        Interlocked.Increment(ref _activeReaders);
        SqliteConnection? conn = null;
        var list = new List<double[]>();
        try
        {
            // 使用引号包围列名，避免与关键字冲突
            var quotedSignalCodes = signalCodes.Select(code =>  $"\"{code}\"" );
            var sql = $"SELECT {string.Join(", ", quotedSignalCodes)} FROM {tableName} LIMIT {endIndex - startIndex} OFFSET {startIndex};";
            conn = RentReadConnection(out pooled);
            using var command = new SqliteCommand(sql, conn);
            using var reader = command.ExecuteReader();

            while (reader.Read())
            {
                var row = new double[signalCodes.Count];
                for (var i = 0; i < signalCodes.Count; i++)
                {
                    row[i] = reader.GetDouble(i);
                }
                list.Add(row);
            }
            return list;
        }
        catch
        {
            return Array.Empty<double[]>();
        }
        finally
        {
            if (conn != null) ReturnReadConnection(conn, pooled);
            Interlocked.Decrement(ref _activeReaders);
        }
    }

    /// <summary>
    /// 获取单个纬度的历史数据Object
    /// </summary>
    /// <param name="tableName">表名（sampleCode+bufferCode）</param>
    /// <param name="signalCodes">信号变量Code</param>
    public IEnumerable<object> GetHistoricalDataObject(string tableName, string signalCode)
    {
        if (_isDisposing) return Array.Empty<object>();
        bool pooled = true;
        Interlocked.Increment(ref _activeReaders);
        SqliteConnection? conn = null;
        var list = new List<object>();
        try
        {
            var quotedSignalCodes = $"\"{signalCode}\"";
            var sql = $"SELECT {quotedSignalCodes} FROM {tableName};";
            conn = RentReadConnection(out pooled);
            using var command = new SqliteCommand(sql, conn);
            using var reader = command.ExecuteReader();

            while (reader.Read())
            {
                if (reader.IsDBNull(0))
                {
                    list.Add(null);
                }
                else
                {
                    switch (reader.GetFieldType(0).Name.ToLower())
                    {
                        case "double":
                            list.Add(reader.GetDouble(0));
                            break;
                        case "int":
                            list.Add(reader.GetInt32(0));
                            break;
                        case "long":
                            list.Add(reader.GetInt64(0));
                            break;
                        case "string":
                            list.Add(reader.GetString(0));
                            break;
                        default:
                            list.Add(reader.GetValue(0));
                            break; // 或者根据需要转换为适当的类型
                    }
                }
            }
            return list;
        }
        catch
        {
            return Array.Empty<object>();
        }
        finally
        {
            if (conn != null) ReturnReadConnection(conn, pooled);
            Interlocked.Decrement(ref _activeReaders);
        }
    }
    /// <summary>
    /// 获取单个纬度的历史数据Object
    /// </summary>
    /// <param name="sampleCode">试样Code</param>
    /// <param name="bufferCode">BufferCode</param>
    /// <param name="signalCode">信号变量Code（列Code）</param>
    /// <returns></returns>
    public IEnumerable<object> GetHistoricalDataObject(string sampleCode, string bufferCode, string signalCode) =>
           GetHistoricalDataObject(sampleCode + bufferCode, signalCode);
    /// <summary>
    /// 获取单个纬度的历史数据double
    /// </summary>
    /// <param name="sampleCode">试样Code</param>
    /// <param name="bufferCode">BufferCode</param>
    /// <param name="signalCode">信号变量Code（列Code）</param>
    public IEnumerable<double> GetHistoricalData(string sampleCode, string bufferCode, string signalCode) =>
        GetHistoricalData(sampleCode + bufferCode, signalCode);

    /// <summary>
    /// 获取多个维度object[] 历史数据
    /// </summary>
    /// <param name="tableName">表名（sampleCode+bufferCode）</param>
    /// <param name="signalCodes">string[] 信号变量Codes</param>
    public IEnumerable<object[]> GetHistoricalData(string tableName, string[] signalCodes)
    {
        if (signalCodes.Length == 0 || _isDisposing) return Array.Empty<object[]>();
        bool pooled = true;
        Interlocked.Increment(ref _activeReaders);
        SqliteConnection? conn = null;
        var list = new List<object[]>();
        try
        {
            var quotedSignalCodes = signalCodes.Select(code => $"\"{code}\"" );
            var sql = $"SELECT {string.Join(", ", quotedSignalCodes)} FROM {tableName};";
            conn = RentReadConnection(out pooled);
            using var reader = new SqliteCommand(sql, conn).ExecuteReader();
            while (reader.Read())
            {
                var row = new object[signalCodes.Length];
                for (var i = 0; i < signalCodes.Length; i++)
                {
                    if (reader.IsDBNull(i))
                    {
                        row[i] = double.NaN;
                    }
                    else if (reader.GetFieldType(i) == typeof(double))
                    {
                        row[i] = reader.GetDouble(i);
                    }
                    else if (reader.GetFieldType(i) == typeof(string))
                    {
                        row[i] = reader.GetString(i);
                    }
                    else
                    {
                        row[i] = reader.GetValue(i);
                    }
                }
                list.Add(row);
            }
            return list;
        }
        catch
        {
            return Array.Empty<object[]>();
        }
        finally
        {
            if (conn != null) ReturnReadConnection(conn, pooled);
            Interlocked.Decrement(ref _activeReaders);
        }
    }

    /// <summary>
    /// 获取历史数据总行数
    /// </summary>
    /// <param name="tableName">表名（sampleCode+bufferCode）</param>
    public int GetHistoricalDataCount(string tableName)
    {
        if (_isDisposing) return 0; // 处置阶段直接返回
        bool pooled = true;
        Interlocked.Increment(ref _activeReaders);
        SqliteConnection? conn = null;
        try
        {
            conn = RentReadConnection(out pooled);
            var sql = $"SELECT COUNT(*) FROM {tableName};";
            using var command = new SqliteCommand(sql, conn);
            var result = Convert.ToInt32(command.ExecuteScalar());
            return result;
        }
        catch
        {
            return 0; // 表不存在或查询失败时返回0
        }
        finally
        {
            if (conn != null) ReturnReadConnection(conn, pooled);
            Interlocked.Decrement(ref _activeReaders);
        }
    }

    /// <summary>
    /// 分页获取历史数据
    /// </summary>
     /// <param name="tableName">表名（sampleCode+bufferCode）</param>
    /// <param name="signalCodes">string[] 信号变量Codes</param>
    /// <param name="pageIndex">第几页</param>
    /// <param name="pageSize">每页数量</param>
    public IEnumerable<object[]> GetHistoricalDataPaged(string tableName, string[] signalCodes, int pageIndex, int pageSize)
    {
        if (signalCodes.Length == 0 || _isDisposing) return Array.Empty<object[]>();
        bool pooled = true;
        Interlocked.Increment(ref _activeReaders);
        SqliteConnection? conn = null;
        var list = new List<object[]>();
        try
        {
            var quotedSignalCodes = signalCodes.Select(code => $"\"{code}\"" );
            var offset = pageIndex * pageSize;
            var sql = $"SELECT {string.Join(", ", quotedSignalCodes)} FROM {tableName} LIMIT {pageSize} OFFSET {offset};";
            conn = RentReadConnection(out pooled);
            using var reader = new SqliteCommand(sql, conn).ExecuteReader();
            while (reader.Read())
            {
                var row = new object[signalCodes.Length];
                for (var i = 0; i < signalCodes.Length; i++)
                {
                    if (reader.IsDBNull(i))
                    {
                        row[i] = double.NaN;
                    }
                    else if (reader.GetFieldType(i) == typeof(double))
                    {
                        row[i] = reader.GetDouble(i);
                    }
                    else if (reader.GetFieldType(i) == typeof(string))
                    {
                        row[i] = reader.GetString(i);
                    }
                    else
                    {
                        row[i] = reader.GetValue(i);
                    }
                }
                list.Add(row);
            }
            return list;
        }
        catch
        {
            return Array.Empty<object[]>();
        }
        finally
        {
            if (conn != null) ReturnReadConnection(conn, pooled);
            Interlocked.Decrement(ref _activeReaders);
        }
    }
}
