import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import {
    Form, Space, InputNumber, Select
} from 'antd'

import VModal from '@/components/vModal/index'
import VButton from '@/components/vButton/index'
import { hardwareForm } from '@/utils/services'

const FORM_INIT_VALUES = {
    FormKey: '',
    g0_RootID: 0,
    g1_HwType: 0,
    g2_AxisID: 0,
    g3_ADID: 0,
    g4_SensorID: 0
}

const { Item } = Form

const ConfigModal = ({
    open,
    onCancel,
    submitCallback,
    formData
}) => {
    const { t } = useTranslation()
    const [form] = Form.useForm()
    const [options, setOptions] = useState([])

    useEffect(() => {
        init()
    }, [])

    const init = async () => {
        const hardwareRes = await hardwareForm({
            key: 'list',
            operation: 'listInit'
        })

        if (!hardwareRes) {
            return
        }

        setOptions(Object.keys(hardwareRes).map(key => ({ label: key, value: key })))
    }

    useEffect(() => {
        if (options.length === 0) {
            return
        }

        if (formData && options.some(item => item.value === formData.FormKey)) {
            form.setFieldsValue(formData)
        }
    }, [formData, options])

    const handleClickSubmit = async () => {
        try {
            const submitRes = await form.validateFields()
            if (submitRes) {
                submitCallback(submitRes)
            }
        } catch (error) {
            console.error(error)
        }
    }
    const onCancelHandle = () => {
        onCancel()
        if (formData) {
            form.setFieldsValue(formData)
        }
    }

    return (
        <VModal
            open={open}
            onCancel={onCancel}
            width={500}
            title={t('配置pid设置')}
            footer={null}
        >
            <Form
                form={form}
                labelCol={{ span: 8 }}
                wrapperCol={{ span: 12 }}
            >
                <Item
                    label="FormKey"
                    name="FormKey"
                    rules={[{ required: true }]}
                >
                    <Select options={options} style={{ width: '200px' }} />
                </Item>
                {Object
                    .keys(FORM_INIT_VALUES)
                    .splice(1)
                    .map(formItem => (
                        <Item
                            key={formItem}
                            name={formItem}
                            label={formItem}
                            rules={[{ required: true }]}

                        >
                            <InputNumber style={{ width: '200px' }} />
                        </Item>
                    ))}
                <Item wrapperCol={{ span: 4, offset: 10 }}>
                    <Space>
                        <VButton onClick={handleClickSubmit}>
                            {t('确定')}
                        </VButton>
                        <VButton onClick={onCancelHandle}>
                            {t('取消')}
                        </VButton>
                    </Space>
                </Item>
            </Form>
        </VModal>
    )
}

export default ConfigModal
