25-09-02 09:04:23:090 DESKTOP-3BSREDP ERROR [clj-scheduler.mq:103] - exception: org.zeromq.ZMQException: Errno *********
 at org.zeromq.ZMQ$Socket.mayRaise (ZMQ.java:2744)
    org.zeromq.ZMQ$Socket.recv (ZMQ.java:2652)
    org.zeromq.ZMQ$Socket.recv (ZMQ.java:2634)
    ezzmq.message$receive_msg.invokeStatic (message.clj:45)
    ezzmq.message$receive_msg.doInvoke (message.clj:28)
    clojure.lang.RestFn.invoke (RestFn.java:413)
    clj_scheduler.mq$rcv_msgs.invokeStatic (mq.clj:61)
    clj_scheduler.mq$rcv_msgs.invoke (mq.clj:60)
    clj_scheduler.mq$start_receive$fn__32123.invoke (mq.clj:93)
    clj_scheduler.mq$start_receive.invokeStatic (mq.clj:92)
    clj_scheduler.mq$start_receive.invoke (mq.clj:89)
    clj_scheduler.mq$start_script_client$fn__32137.invoke (mq.clj:116)
    clojure.core$binding_conveyor_fn$fn__5842.invoke (core.clj:2047)
    clojure.lang.AFn.call (AFn.java:18)
    java.util.concurrent.FutureTask.run (FutureTask.java:264)
    java.util.concurrent.ThreadPoolExecutor.runWorker (ThreadPoolExecutor.java:1136)
    java.util.concurrent.ThreadPoolExecutor$Worker.run (ThreadPoolExecutor.java:635)
    java.lang.Thread.run (Thread.java:840)

25-09-02 09:04:23:094 DESKTOP-3BSREDP ERROR [clj-scheduler.mq:103] - exception: org.zeromq.ZMQException: Errno *********
 at org.zeromq.ZMQ$Socket.mayRaise (ZMQ.java:2744)
    org.zeromq.ZMQ$Socket.recv (ZMQ.java:2652)
    org.zeromq.ZMQ$Socket.recv (ZMQ.java:2634)
    ezzmq.message$receive_msg.invokeStatic (message.clj:45)
    ezzmq.message$receive_msg.doInvoke (message.clj:28)
    clojure.lang.RestFn.invoke (RestFn.java:413)
    clj_scheduler.mq$rcv_msgs.invokeStatic (mq.clj:61)
    clj_scheduler.mq$rcv_msgs.invoke (mq.clj:60)
    clj_scheduler.mq$start_receive$fn__32123.invoke (mq.clj:93)
    clj_scheduler.mq$start_receive.invokeStatic (mq.clj:92)
    clj_scheduler.mq$start_receive.invoke (mq.clj:89)
    clj_scheduler.mq$start_script_client$fn__32137.invoke (mq.clj:116)
    clojure.core$binding_conveyor_fn$fn__5842.invoke (core.clj:2047)
    clojure.lang.AFn.call (AFn.java:18)
    java.util.concurrent.FutureTask.run (FutureTask.java:264)
    java.util.concurrent.ThreadPoolExecutor.runWorker (ThreadPoolExecutor.java:1136)
    java.util.concurrent.ThreadPoolExecutor$Worker.run (ThreadPoolExecutor.java:635)
    java.lang.Thread.run (Thread.java:840)

25-09-02 09:13:15:623 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - TEMPLATE_INST_RSP
25-09-02 09:13:15:625 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"016d1673-8af7-47ed-a1c3-138cba4ede9e","code":0,"msg":"\u6A21\u677F\u751F\u6210\u6210\u529F"}
25-09-02 09:13:21:298 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_17-78639282-fa6a-4822-b502-d2144342e71a 初始化
25-09-02 09:13:21:519 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 09:13:21:520 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_17","ProcessID":"project_17-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"start-node-action","MsgBody":{}}
25-09-02 09:13:22:143 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 09:13:22:144 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\u0022,\r\n  \u0022Code\u0022: \u0022input_delCanshu\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_17\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_5167aeea\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 09:13:22:150 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 09:13:22:151 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\u0022,\r\n  \u0022Code\u0022: \u0022input_addCanshu\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_17\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_5167aeea\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 09:13:22:155 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 09:13:22:157 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_17","ProcessID":"project_17-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"SubTaskEvalScript-55265d12-76be-4e37-9044-e0240568fa69","MsgBody":{}}
25-09-02 09:13:22:164 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:Console.WriteLine("执行判断");
int? ret = Model.station.Ccss_Connected(0);
Console.WriteLine("联机返回值"+ret);
if(ret==0){
  Model.GetVarByName<BooleanInputVar>("input_close_status").Value =true;
  Model.GetVarByName<TextInputVar>("input_open_state").Value ="已联机";
  return true;
}else{
    Model.GetVarByName<BooleanInputVar>("input_close_status").Value =false;
    return false;
}
25-09-02 09:13:22:443 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 09:13:22:445 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_close_status\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_17\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 09:13:22:469 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-02 09:13:22:470 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_17","ScriptId":"7fbe3f62-92f5-4625-9d7b-f0461c2a7b40","Result":false}
25-09-02 09:13:22:480 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:Console.WriteLine("执行判断");
int? ret = Model.station.Ccss_Connected(0);
Console.WriteLine("联机返回值"+ret);
if(ret==0){
  Model.GetVarByName<BooleanInputVar>("input_close_status").Value =true;
  Model.GetVarByName<TextInputVar>("input_open_state").Value ="已联机";
  return true;
}else{
    Model.GetVarByName<BooleanInputVar>("input_close_status").Value =false;
    return false;
}
25-09-02 09:13:22:513 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 09:13:22:515 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_close_status\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_17\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 09:13:22:522 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-02 09:13:22:523 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_17","ScriptId":"7cddc9a5-6992-46e3-9946-ceaf0dc56211","Result":false}
25-09-02 09:13:22:586 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 09:13:22:588 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u672A\\u8054\\u673A\u0022,\r\n  \u0022Code\u0022: \u0022input_open_state\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_17\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 09:13:22:596 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 09:13:22:598 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u672A\\u9650\\u4F4D\u0022,\r\n  \u0022Code\u0022: \u0022input_limit_state\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_17\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 09:13:22:606 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 09:13:22:607 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u8BD5\\u9A8C\\u505C\\u6B62\u0022,\r\n  \u0022Code\u0022: \u0022input_test_state\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_17\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 09:13:22:614 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 09:13:22:615 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_gpzxds\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_17\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_5167aeea\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 09:13:22:620 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 09:13:22:622 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_17","ProcessID":"project_17-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"SubTaskEvalScript-4efa23c4-de22-4f6a-b7e9-f6e92d563d76","MsgBody":{}}
25-09-02 09:13:22:649 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 09:13:22:651 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_17","ProcessID":"project_17-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"onlyAction-adf0e120-9035-4183-b8d4-1bd802f3644d","MsgBody":{"Cmd":"start","InstCode":"sample_5167aeea","ActionID":"0d3ca2d0-242b-41e8-a002-6681e72f0c84"}}
25-09-02 09:13:22:659 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_17-0d3ca2d0-242b-41e8-a002-6681e72f0c84 初始化
25-09-02 09:13:22:725 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 09:13:22:726 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_17","ProcessID":"project_17-0d3ca2d0-242b-41e8-a002-6681e72f0c84","SubTaskID":"start-node-action","MsgBody":{}}
25-09-02 09:13:23:084 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 09:13:23:086 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_17","ProcessID":"project_17-0d3ca2d0-242b-41e8-a002-6681e72f0c84","SubTaskID":"SubTaskEvalScript-f3f0dc42-cd28-4c97-b9c9-7c95fbcece0d","MsgBody":{}}
25-09-02 09:13:23:160 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 09:13:23:161 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_17","ProcessID":"project_17-0d3ca2d0-242b-41e8-a002-6681e72f0c84","SubTaskID":"end-node-action","MsgBody":{}}
25-09-02 09:13:23:163 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_17-0d3ca2d0-242b-41e8-a002-6681e72f0c84  Stop: {:parent {:Type "process-action", :ClassName "project_17", :ProcessID "project_17-78639282-fa6a-4822-b502-d2144342e71a", :SubTaskID "onlyAction-adf0e120-9035-4183-b8d4-1bd802f3644d", :MsgBody {:Cmd "start", :InstCode "sample_5167aeea", :ActionID "0d3ca2d0-242b-41e8-a002-6681e72f0c84"}}}
25-09-02 09:13:23:244 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 09:13:23:253 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 09:13:23:255 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_17","ProcessID":"project_17-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"onlyAction-adf0e120-9035-4183-b8d4-1bd802f3644d","MsgBody":{}}
25-09-02 09:13:23:261 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 09:13:23:263 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_17","ProcessID":"project_17-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"onlyAction-d6af69bd-4fe1-4427-9f59-7bf08dc81344","MsgBody":{"Cmd":"start","InstCode":"sample_5167aeea","ActionID":"cd874ea2-bc09-487e-a784-75bf88d9e2c3"}}
25-09-02 09:13:23:270 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_17-cd874ea2-bc09-487e-a784-75bf88d9e2c3 初始化
25-09-02 09:13:23:366 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 09:13:23:368 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_17","ProcessID":"project_17-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"onlyAction-d6af69bd-4fe1-4427-9f59-7bf08dc81344","MsgBody":{}}
25-09-02 09:13:23:371 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 09:13:23:372 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_17","ProcessID":"project_17-cd874ea2-bc09-487e-a784-75bf88d9e2c3","SubTaskID":"start-node-action","MsgBody":{}}
25-09-02 09:13:23:429 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 09:13:23:432 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_17","ProcessID":"project_17-78639282-fa6a-4822-b502-d2144342e71a","SubTaskID":"end-node-action","MsgBody":{}}
25-09-02 09:13:23:435 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_17-78639282-fa6a-4822-b502-d2144342e71a  Stop: {:parent nil}
25-09-02 09:13:23:589 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 09:58:33:412 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_17-80f9c3ad-d214-405b-a10e-a16ec438b920 初始化
25-09-02 09:58:33:457 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 09:58:33:460 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_17","ProcessID":"project_17-80f9c3ad-d214-405b-a10e-a16ec438b920","SubTaskID":"start-node-action","MsgBody":{}}
25-09-02 09:58:33:463 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-02 09:58:33:466 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_17-78639282-fa6a-4822-b502-d2144342e71a  Stop: {:parent nil}
25-09-02 09:58:33:470 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 09:58:33:471 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-02 09:58:33:472 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_17-0d3ca2d0-242b-41e8-a002-6681e72f0c84  Stop: {:parent {:Type "process-action", :ClassName "project_17", :ProcessID "project_17-78639282-fa6a-4822-b502-d2144342e71a", :SubTaskID "onlyAction-adf0e120-9035-4183-b8d4-1bd802f3644d", :MsgBody {:Cmd "start", :InstCode "sample_5167aeea", :ActionID "0d3ca2d0-242b-41e8-a002-6681e72f0c84"}}}
25-09-02 09:58:33:475 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 09:58:33:477 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-02 09:58:33:477 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_17-cd874ea2-bc09-487e-a784-75bf88d9e2c3  Stop: {:parent {:Type "process-action", :ClassName "project_17", :ProcessID "project_17-78639282-fa6a-4822-b502-d2144342e71a", :SubTaskID "onlyAction-d6af69bd-4fe1-4427-9f59-7bf08dc81344", :MsgBody {:Cmd "start", :InstCode "sample_5167aeea", :ActionID "cd874ea2-bc09-487e-a784-75bf88d9e2c3"}}}
25-09-02 09:58:33:564 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 09:58:33:566 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-02 09:58:33:567 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_17-80f9c3ad-d214-405b-a10e-a16ec438b920  Stop: {:parent nil}
25-09-02 09:58:33:588 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 09:58:33:589 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_17","ProcessID":"project_17-80f9c3ad-d214-405b-a10e-a16ec438b920","SubTaskID":"SubTaskEvalScript-baabeb63-3a1d-460f-832e-fb4f64312454","MsgBody":{}}
25-09-02 09:58:33:590 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:SubTaskEvalScript-baabeb63-3a1d-460f-832e-fb4f64312454不在执行状态, 而是 :aborted
25-09-02 09:58:33:652 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 09:58:33:775 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 09:58:33:776 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_17","ProcessID":"project_17-cd874ea2-bc09-487e-a784-75bf88d9e2c3","SubTaskID":"daqRmc-485c6190-0361-42ff-81c5-6737d7119a1c","MsgBody":{}}
25-09-02 09:58:33:797 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 09:58:33:798 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_17","ProcessID":"project_17-80f9c3ad-d214-405b-a10e-a16ec438b920","SubTaskID":"SubTaskEvalScript-baabeb63-3a1d-460f-832e-fb4f64312454","MsgBody":{}}
25-09-02 14:04:10:737 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - TEMPLATE_INST_RSP
25-09-02 14:04:10:738 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"5cf045ed-c181-4731-b486-a8b7fd9e0883","code":0,"msg":"\u6A21\u677F\u751F\u6210\u6210\u529F"}
25-09-02 14:04:29:925 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-6a579b71-e5fb-4bef-9569-33de699c61ea 初始化
25-09-02 14:04:29:979 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 14:04:29:980 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"start-node-action","MsgBody":{}}
25-09-02 14:04:30:048 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 14:04:30:049 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 14:04:30:056 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 14:04:30:057 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 14:04:30:061 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 14:04:30:062 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"SubTaskEvalScript-b339a41f-5402-476c-99f1-aac17677e715","MsgBody":{}}
25-09-02 14:04:30:323 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 14:04:30:324 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_F5\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 14:04:30:327 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 14:04:30:328 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_Fmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 14:04:30:331 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 14:04:30:332 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_FQ\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 14:04:30:336 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 14:04:30:336 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_KQ\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 14:04:30:340 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 14:04:30:340 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_KR\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 14:04:30:344 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 14:04:30:344 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_pmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 14:04:30:348 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 14:04:30:348 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_jzsl\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 14:04:30:351 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 14:04:30:353 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: -1,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: -1,\r\n  \u0022Code\u0022: \u0022input_type\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: \u0022\u0022,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 14:04:30:357 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 14:04:30:358 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"SubTaskEvalScript-0de23181-201f-40db-ba18-1836ed9ee3fd","MsgBody":{}}
25-09-02 14:04:30:366 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 14:04:30:367 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"end-node-action","MsgBody":{}}
25-09-02 14:04:30:368 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-02 14:04:30:449 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 16:59:47:202 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45 初始化
25-09-02 16:59:47:251 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-63526397-7d5a-4d20-8840-4fa2ceee596d 初始化
25-09-02 16:59:47:257 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 16:59:47:257 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"start-node-action","MsgBody":{}}
25-09-02 16:59:47:293 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-02 16:59:47:294 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-02 16:59:47:296 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 16:59:47:297 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-02 16:59:47:299 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent nil}
25-09-02 16:59:47:373 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 16:59:47:375 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-02 16:59:47:375 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-63526397-7d5a-4d20-8840-4fa2ceee596d  Stop: {:parent nil}
25-09-02 16:59:47:416 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 16:59:47:447 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SAVE_PROJECT
25-09-02 16:59:47:448 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProjectID":27}
25-09-02 16:59:47:449 DESKTOP-3BSREDP INFO [clj-scheduler.mq:47] - Received SAVE_PROJECT ZMQ message, dispatching to project channel: {:ProjectID 27}
25-09-02 16:59:47:451 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 16:59:47:452 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4","MsgBody":{}}
25-09-02 16:59:47:453 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 16:59:47:453 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"start-node-action","MsgBody":{}}
25-09-02 16:59:47:536 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 16:59:47:538 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4","MsgBody":{}}
25-09-02 17:00:39:781 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - TEMPLATE_INST_RSP
25-09-02 17:00:39:782 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"4502b496-bf5a-4e7f-bce1-f022d834547f","code":0,"msg":"\u6A21\u677F\u751F\u6210\u6210\u529F"}
25-09-02 17:00:41:059 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-6a579b71-e5fb-4bef-9569-33de699c61ea 初始化
25-09-02 17:00:41:178 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:00:41:179 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"start-node-action","MsgBody":{}}
25-09-02 17:00:41:276 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:00:41:276 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:00:41:280 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:00:41:280 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:00:41:284 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:00:41:284 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"SubTaskEvalScript-b339a41f-5402-476c-99f1-aac17677e715","MsgBody":{}}
25-09-02 17:00:41:672 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:00:41:673 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_F5\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:00:41:677 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:00:41:678 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_Fmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:00:41:682 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:00:41:682 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_FQ\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:00:41:686 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:00:41:687 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_KQ\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:00:41:691 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:00:41:692 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_KR\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:00:41:695 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:00:41:695 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_pmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:00:41:699 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:00:41:700 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_jzsl\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:00:41:704 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:00:41:706 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: -1,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: -1,\r\n  \u0022Code\u0022: \u0022input_type\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: \u0022\u0022,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:00:41:715 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:00:41:717 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"SubTaskEvalScript-0de23181-201f-40db-ba18-1836ed9ee3fd","MsgBody":{}}
25-09-02 17:00:41:756 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:00:41:758 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"end-node-action","MsgBody":{}}
25-09-02 17:00:41:759 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-02 17:00:41:806 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:20:38:473 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1 初始化
25-09-02 17:20:38:516 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:20:38:517 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"start-node-action","MsgBody":{}}
25-09-02 17:20:38:559 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:20:38:560 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskEvalScript-38c7f155-9116-43d2-97c4-c0952ddf8d40","MsgBody":{}}
25-09-02 17:21:10:042 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45 初始化
25-09-02 17:21:10:089 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-63526397-7d5a-4d20-8840-4fa2ceee596d 初始化
25-09-02 17:21:10:090 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:21:10:091 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"start-node-action","MsgBody":{}}
25-09-02 17:21:10:163 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SAVE_PROJECT
25-09-02 17:21:10:164 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProjectID":27}
25-09-02 17:21:10:164 DESKTOP-3BSREDP INFO [clj-scheduler.mq:47] - Received SAVE_PROJECT ZMQ message, dispatching to project channel: {:ProjectID 27}
25-09-02 17:21:10:165 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:21:10:165 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4","MsgBody":{}}
25-09-02 17:21:10:165 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:21:10:166 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"start-node-action","MsgBody":{}}
25-09-02 17:21:10:176 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:21:10:176 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"end-node-action","MsgBody":{}}
25-09-02 17:21:10:178 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent nil}
25-09-02 17:21:10:239 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:21:10:272 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:21:10:273 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ifopendivice\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:21:10:282 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:21:10:283 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:21:10:300 DESKTOP-3BSREDP ERROR [clj-scheduler.mq:103] - exception: clojure.lang.ExceptionInfo: [SQLITE_BUSY]  The database file is locked (cannot commit transaction - SQL statements in progress)
{:query-id :batch-update!}
 at conman.core$try_query$fn__4846$fn__4847.invoke (core.clj:34)
    clojure.lang.AFn.applyToHelper (AFn.java:156)
    clojure.lang.RestFn.applyTo (RestFn.java:135)
    clojure.core$apply.invokeStatic (core.clj:671)
    clojure.core$apply.invoke (core.clj:662)
    clj_backend.common.common_db$eval9347$f__4866__auto____9374.doInvoke (common_db.clj:6)
    clojure.lang.RestFn.invoke (RestFn.java:428)
    clj_backend.modules.variable.variable_modified$update_input_variable.invokeStatic (variable_modified.clj:44)
    clj_backend.modules.variable.variable_modified$update_input_variable.invoke (variable_modified.clj:29)
    clj_backend.modules.variable.variable_modified$variable_modiffed.invokeStatic (variable_modified.clj:134)
    clj_backend.modules.variable.variable_modified$variable_modiffed.invoke (variable_modified.clj:122)
    clj_scheduler.mq$start_receive$fn__32123.invoke (mq.clj:100)
    clj_scheduler.mq$start_receive.invokeStatic (mq.clj:92)
    clj_scheduler.mq$start_receive.invoke (mq.clj:89)
    clj_scheduler.mq$start_script_client$fn__32137.invoke (mq.clj:116)
    clojure.core$binding_conveyor_fn$fn__5842.invoke (core.clj:2047)
    clojure.lang.AFn.call (AFn.java:18)
    java.util.concurrent.FutureTask.run (FutureTask.java:264)
    java.util.concurrent.ThreadPoolExecutor.runWorker (ThreadPoolExecutor.java:1136)
    java.util.concurrent.ThreadPoolExecutor$Worker.run (ThreadPoolExecutor.java:635)
    java.lang.Thread.run (Thread.java:840)

25-09-02 17:21:10:303 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:21:10:303 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"SubTaskEvalScript-8e9f3ddc-e65c-429c-8106-cfe857f6f11a","MsgBody":{}}
25-09-02 17:21:10:316 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:21:10:318 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"end-node-action","MsgBody":{}}
25-09-02 17:21:10:319 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-63526397-7d5a-4d20-8840-4fa2ceee596d  Stop: {:parent nil}
25-09-02 17:21:10:325 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-02 17:21:10:325 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-02 17:21:10:330 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:21:10:332 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-02 17:21:10:334 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1  Stop: {:parent nil}
25-09-02 17:21:10:449 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:21:10:484 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:21:10:485 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-02 17:21:10:486 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent nil}
25-09-02 17:21:10:490 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:21:10:491 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-02 17:21:10:491 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-63526397-7d5a-4d20-8840-4fa2ceee596d  Stop: {:parent nil}
25-09-02 17:21:10:493 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:21:10:494 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:21:10:495 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-02 17:21:10:495 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:21:10:496 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"daqRmc-9c1f5247-99e5-4a62-8446-73b344f248a2","MsgBody":{}}
25-09-02 17:21:15:936 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - TEMPLATE_INST_RSP
25-09-02 17:21:15:938 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"e1d8180d-d869-458b-b50d-3b9435c68e68","code":0,"msg":"\u6A21\u677F\u751F\u6210\u6210\u529F"}
25-09-02 17:21:17:090 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-6a579b71-e5fb-4bef-9569-33de699c61ea 初始化
25-09-02 17:21:17:150 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:21:17:150 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"start-node-action","MsgBody":{}}
25-09-02 17:21:17:176 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:21:17:177 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:21:17:181 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:21:17:182 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:21:17:186 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:21:17:187 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"SubTaskEvalScript-b339a41f-5402-476c-99f1-aac17677e715","MsgBody":{}}
25-09-02 17:21:17:214 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:21:17:215 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_F5\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:21:17:221 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:21:17:222 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_Fmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:21:17:228 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:21:17:228 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_FQ\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:21:17:231 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:21:17:232 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_KQ\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:21:17:235 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:21:17:237 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_KR\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:21:17:245 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:21:17:247 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_pmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:21:17:254 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:21:17:255 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_jzsl\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:21:17:260 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:21:17:261 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: -1,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: -1,\r\n  \u0022Code\u0022: \u0022input_type\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: \u0022\u0022,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:21:17:268 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:21:17:269 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"SubTaskEvalScript-0de23181-201f-40db-ba18-1836ed9ee3fd","MsgBody":{}}
25-09-02 17:21:17:275 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:21:17:275 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"end-node-action","MsgBody":{}}
25-09-02 17:21:17:276 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-02 17:21:17:324 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:21:20:707 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1 初始化
25-09-02 17:21:20:769 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:21:20:770 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"start-node-action","MsgBody":{}}
25-09-02 17:21:20:796 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:21:20:796 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskEvalScript-38c7f155-9116-43d2-97c4-c0952ddf8d40","MsgBody":{}}
25-09-02 17:22:25:260 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-02 17:22:25:261 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1  Stop: {:parent nil}
25-09-02 17:22:25:266 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:22:25:267 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-02 17:22:25:268 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:22:25:268 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037不在执行状态, 而是 :aborted
25-09-02 17:22:25:268 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-02 17:22:25:269 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:22:25:270 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"daqRmc-9c1f5247-99e5-4a62-8446-73b344f248a2","MsgBody":{}}
25-09-02 17:22:25:269 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037不在执行状态, 而是 :aborted
25-09-02 17:22:25:270 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:daqRmc-9c1f5247-99e5-4a62-8446-73b344f248a2不在执行状态, 而是 :aborted
25-09-02 17:22:25:310 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:22:26:343 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1 初始化
25-09-02 17:22:26:415 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:22:26:415 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"start-node-action","MsgBody":{}}
25-09-02 17:22:26:434 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:22:26:435 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskEvalScript-38c7f155-9116-43d2-97c4-c0952ddf8d40","MsgBody":{}}
25-09-02 17:36:16:958 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45 初始化
25-09-02 17:36:17:034 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-63526397-7d5a-4d20-8840-4fa2ceee596d 初始化
25-09-02 17:36:17:038 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:36:17:039 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"start-node-action","MsgBody":{}}
25-09-02 17:36:17:071 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SAVE_PROJECT
25-09-02 17:36:17:073 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProjectID":27}
25-09-02 17:36:17:074 DESKTOP-3BSREDP INFO [clj-scheduler.mq:47] - Received SAVE_PROJECT ZMQ message, dispatching to project channel: {:ProjectID 27}
25-09-02 17:36:17:074 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:36:17:075 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4","MsgBody":{}}
25-09-02 17:36:17:092 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:36:17:093 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"end-node-action","MsgBody":{}}
25-09-02 17:36:17:094 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:36:17:094 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"start-node-action","MsgBody":{}}
25-09-02 17:36:17:094 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent nil}
25-09-02 17:36:17:110 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:36:17:112 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ifopendivice\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:36:17:119 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:36:17:120 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:36:17:124 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:36:17:125 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"SubTaskEvalScript-8e9f3ddc-e65c-429c-8106-cfe857f6f11a","MsgBody":{}}
25-09-02 17:36:17:142 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:36:17:143 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"end-node-action","MsgBody":{}}
25-09-02 17:36:17:143 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-63526397-7d5a-4d20-8840-4fa2ceee596d  Stop: {:parent nil}
25-09-02 17:36:17:147 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-02 17:36:17:149 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-02 17:36:17:151 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:36:17:152 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:36:17:153 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-02 17:36:17:154 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1  Stop: {:parent nil}
25-09-02 17:36:17:289 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:36:17:300 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:36:17:301 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-02 17:36:17:301 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:36:17:302 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-02 17:36:17:302 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037不在执行状态, 而是 :aborted
25-09-02 17:36:17:302 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:36:17:303 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-02 17:36:17:303 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037不在执行状态, 而是 :aborted
25-09-02 17:36:17:304 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:36:17:304 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:36:17:305 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"daqRmc-9c1f5247-99e5-4a62-8446-73b344f248a2","MsgBody":{}}
25-09-02 17:36:17:305 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037不在执行状态, 而是 :aborted
25-09-02 17:36:17:305 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-02 17:36:17:307 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent nil}
25-09-02 17:36:17:309 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:36:17:309 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-02 17:36:17:310 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-63526397-7d5a-4d20-8840-4fa2ceee596d  Stop: {:parent nil}
25-09-02 17:36:17:312 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:43:35:145 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - TEMPLATE_INST_RSP
25-09-02 17:43:35:146 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"6f0d547a-54fd-4188-b116-4d04d52c0c50","code":0,"msg":"\u6A21\u677F\u751F\u6210\u6210\u529F"}
25-09-02 17:43:36:749 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-6a579b71-e5fb-4bef-9569-33de699c61ea 初始化
25-09-02 17:43:36:878 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:43:36:879 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"start-node-action","MsgBody":{}}
25-09-02 17:43:37:304 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:43:37:306 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:43:37:310 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:43:37:312 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:43:37:316 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:43:37:316 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"SubTaskEvalScript-b339a41f-5402-476c-99f1-aac17677e715","MsgBody":{}}
25-09-02 17:43:37:968 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:43:37:969 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_F5\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:43:37:973 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:43:37:974 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_Fmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:43:37:978 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:43:37:979 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_FQ\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:43:37:982 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:43:37:983 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_KQ\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:43:37:986 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:43:37:987 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_KR\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:43:37:990 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:43:37:992 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_pmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:43:37:995 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:43:37:995 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_jzsl\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:43:38:000 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:43:38:000 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: -1,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: -1,\r\n  \u0022Code\u0022: \u0022input_type\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: \u0022\u0022,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:43:38:008 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:43:38:009 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"SubTaskEvalScript-0de23181-201f-40db-ba18-1836ed9ee3fd","MsgBody":{}}
25-09-02 17:43:38:023 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:43:38:024 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"end-node-action","MsgBody":{}}
25-09-02 17:43:38:025 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-02 17:43:38:072 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:44:28:334 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45 初始化
25-09-02 17:44:28:383 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-63526397-7d5a-4d20-8840-4fa2ceee596d 初始化
25-09-02 17:44:28:384 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:44:28:385 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"start-node-action","MsgBody":{}}
25-09-02 17:44:28:439 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-02 17:44:28:440 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-02 17:44:28:444 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:44:28:446 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-02 17:44:28:447 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent nil}
25-09-02 17:44:28:455 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SAVE_PROJECT
25-09-02 17:44:28:456 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProjectID":27}
25-09-02 17:44:28:458 DESKTOP-3BSREDP INFO [clj-scheduler.mq:47] - Received SAVE_PROJECT ZMQ message, dispatching to project channel: {:ProjectID 27}
25-09-02 17:44:28:459 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:44:28:459 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4","MsgBody":{}}
25-09-02 17:44:28:460 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:44:28:460 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"start-node-action","MsgBody":{}}
25-09-02 17:44:28:460 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4不在执行状态, 而是 :aborted
25-09-02 17:44:28:518 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:44:28:519 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4","MsgBody":{}}
25-09-02 17:44:28:520 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:44:28:520 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4不在执行状态, 而是 :aborted
25-09-02 17:44:28:521 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-02 17:44:28:522 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-63526397-7d5a-4d20-8840-4fa2ceee596d  Stop: {:parent nil}
25-09-02 17:44:28:617 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:44:28:653 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:44:28:653 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ifopendivice\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:44:28:661 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:44:28:662 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:44:28:667 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:44:28:668 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"SubTaskEvalScript-8e9f3ddc-e65c-429c-8106-cfe857f6f11a","MsgBody":{}}
25-09-02 17:44:28:668 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:44:28:669 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"SubTaskEvalScript-8e9f3ddc-e65c-429c-8106-cfe857f6f11a","MsgBody":{}}
25-09-02 17:44:40:569 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - TEMPLATE_INST_RSP
25-09-02 17:44:40:571 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"cd55ca8e-0ba9-4cb3-bea1-1dc5987a4e00","code":0,"msg":"\u6A21\u677F\u751F\u6210\u6210\u529F"}
25-09-02 17:44:41:914 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-6a579b71-e5fb-4bef-9569-33de699c61ea 初始化
25-09-02 17:44:41:993 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:44:41:994 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"start-node-action","MsgBody":{}}
25-09-02 17:44:42:092 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:44:42:093 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:44:42:098 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:44:42:099 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:44:42:105 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:44:42:106 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"SubTaskEvalScript-b339a41f-5402-476c-99f1-aac17677e715","MsgBody":{}}
25-09-02 17:44:42:232 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:44:42:233 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_F5\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:44:42:237 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:44:42:239 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_Fmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:44:42:243 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:44:42:244 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_FQ\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:44:42:249 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:44:42:250 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_KQ\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:44:42:254 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:44:42:254 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_KR\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:44:42:258 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:44:42:259 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_pmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:44:42:263 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:44:42:264 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_jzsl\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:44:42:273 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:44:42:274 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: -1,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: -1,\r\n  \u0022Code\u0022: \u0022input_type\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: \u0022\u0022,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:44:42:279 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:44:42:280 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"SubTaskEvalScript-0de23181-201f-40db-ba18-1836ed9ee3fd","MsgBody":{}}
25-09-02 17:44:42:285 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:44:42:286 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"end-node-action","MsgBody":{}}
25-09-02 17:44:42:287 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-02 17:44:42:333 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:44:59:394 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1 初始化
25-09-02 17:44:59:433 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:44:59:435 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"start-node-action","MsgBody":{}}
25-09-02 17:44:59:475 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:44:59:476 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskEvalScript-38c7f155-9116-43d2-97c4-c0952ddf8d40","MsgBody":{}}
25-09-02 17:45:02:963 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:45:02:964 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-02 17:45:02:975 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:45:02:976 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-02 17:45:02:977 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037不在执行状态, 而是 :finished
25-09-02 17:45:03:064 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:45:03:066 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_ifopendivice\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:45:20:230 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:45:20:231 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:45:20:736 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:45:20:738 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:45:38:324 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-679f70fa-d870-40d9-b121-c759b28044ed 初始化
25-09-02 17:45:38:365 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:45:38:366 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"start-node-action","MsgBody":{}}
25-09-02 17:45:38:382 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:45:38:383 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"cmdConnection-d024261f-2793-4b35-bc63-6661078f2b6e","MsgBody":{}}
25-09-02 17:45:38:389 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:45:38:390 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"end-node-action","MsgBody":{}}
25-09-02 17:45:38:390 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-679f70fa-d870-40d9-b121-c759b28044ed  Stop: {:parent nil}
25-09-02 17:45:38:424 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:45:38:738 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:45:38:739 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:50:49:768 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee 初始化
25-09-02 17:50:49:828 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:50:49:829 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"start-node-action","MsgBody":{}}
25-09-02 17:50:49:830 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:int flag = 0;

int deviceId = 0;
int ret = Model.station.Ccss_Connected(deviceId);
if (ret == 0) {
  /* 已连接 */ 
  flag++;
} else { 
  /* 未连接 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器连接失败，请重新连接控制器";
  return false;
}
int ret1 = Model.station.Ccss_DriveReady(deviceId);
if (ret1 == 0) { 
  /* 准备就绪 */ 
  flag++;
} else {
  /* 未就绪 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器未在启动状态";
  return false;
}
return true;
25-09-02 17:50:49:887 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-02 17:50:49:888 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_27","ScriptId":"d57f1209-8f63-49f3-b491-c466de382b4b","Result":true}
25-09-02 17:50:50:169 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:50:50:169 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"SubTaskEvalScript-cc94c47e-def9-4413-a314-5379c15e2989","MsgBody":{}}
25-09-02 17:50:50:397 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:50:50:398 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"SubTaskEvalScript-d38715eb-7301-437d-b693-3e546d3997f9","MsgBody":{}}
25-09-02 17:50:50:400 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:var blockline = Model.dVariable["blockline"];
if(blockline==1){
  return true;
}
return false;
25-09-02 17:50:50:460 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:50:50:460 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"combinedWaveStartControl-5da849dd-b2c1-47db-8def-f3243aae6634","MsgBody":{}}
25-09-02 17:50:50:495 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-02 17:50:50:496 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_27","ScriptId":"a991e16c-f159-4b34-93be-7e67c04fb3de","Result":true}
25-09-02 17:50:50:590 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:50:50:590 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:50:50:707 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:50:50:708 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-7a9b46e2-ce29-4ac0-a55d-be7b124e697d","MsgBody":{}}
25-09-02 17:50:50:873 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:50:50:874 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:50:50:982 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:50:50:983 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-ec32daaf-a89d-4394-9719-b18e311efb7a","MsgBody":{}}
25-09-02 17:50:50:988 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:50:50:989 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:50:51:090 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:50:51:091 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-c39d0131-3fdb-46d8-af94-0668968e4756","MsgBody":{}}
25-09-02 17:50:51:108 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:50:51:109 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:50:51:214 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:50:51:214 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-b7edfcb1-c345-463c-aacd-c8d2eb206635","MsgBody":{}}
25-09-02 17:50:51:220 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:50:51:220 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:50:51:321 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:50:51:321 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-333a9f87-cae8-4b89-ab58-dd716d819b58","MsgBody":{}}
25-09-02 17:50:51:327 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:50:51:328 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:50:51:435 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:50:51:437 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-3756a5ec-49f2-4c21-a287-b92e938f5c45","MsgBody":{}}
25-09-02 17:50:58:486 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:50:58:487 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"waitEvent-426ba70c-a598-491b-ae16-d5a27715763f","MsgBody":{}}
25-09-02 17:51:02:793 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:51:02:794 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"creepSignalCheck-8d313bcb-d7cc-424e-bb19-d7d82337452b","MsgBody":{}}
25-09-02 17:51:02:810 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:51:02:811 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"daq-0ac09652-82d1-4130-a028-2e081713c233","MsgBody":{}}
25-09-02 17:51:02:812 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:daq-0ac09652-82d1-4130-a028-2e081713c233不在执行状态, 而是 :aborted
25-09-02 17:51:02:901 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:51:02:902 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"SubTaskEvalScript-2d644e51-aac7-477a-99b7-7f3efe700e51","MsgBody":{}}
25-09-02 17:51:05:716 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:51:05:717 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"waitEvent-5cdadc8f-47b5-4639-a8b1-d26cbf98093f","MsgBody":{}}
25-09-02 17:51:05:780 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:51:05:781 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"waitEvent-54a9826e-7e5e-4b46-a94a-3868836dcd6f","MsgBody":{}}
25-09-02 17:51:11:545 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:51:11:546 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"creepSignalCheck-71a89ec2-eaf7-4ba4-aa56-76c5a8ea8f69","MsgBody":{}}
25-09-02 17:51:11:551 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:51:11:552 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"daq-43bef327-78fb-4784-9a26-7efed0ab4432","MsgBody":{}}
25-09-02 17:51:11:554 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:daq-43bef327-78fb-4784-9a26-7efed0ab4432不在执行状态, 而是 :aborted
25-09-02 17:51:11:622 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:51:11:623 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"SubTaskEvalScript-153bfad7-49bc-43e7-a801-219b119c9104","MsgBody":{}}
25-09-02 17:51:17:743 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:51:17:744 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"waitEvent-ffdc7080-10a2-478e-9add-ad7db595b4ee","MsgBody":{}}
25-09-02 17:51:17:772 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:51:17:773 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"waitEvent-911d94cb-a26d-4dda-bb0b-281270094ee5","MsgBody":{}}
25-09-02 17:51:24:733 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:51:24:735 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"creepSignalCheck-05ef462b-d544-4857-9945-538a503ecb00","MsgBody":{}}
25-09-02 17:51:24:740 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:51:24:741 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"daq-32891850-ce61-48d8-98cd-20634cf737c7","MsgBody":{}}
25-09-02 17:51:24:742 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:daq-32891850-ce61-48d8-98cd-20634cf737c7不在执行状态, 而是 :aborted
25-09-02 17:51:29:742 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:51:29:743 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"combinedWaveEndControl-07f9477f-ddab-4730-ac75-81ca40937ab5","MsgBody":{}}
25-09-02 17:51:39:761 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9 初始化
25-09-02 17:51:39:817 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:51:39:818 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9","SubTaskID":"start-node-action","MsgBody":{}}
25-09-02 17:51:39:828 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:51:39:829 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_27","ProcessID":"project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9","SubTaskID":"onlyAction-0fd5b761-7c8e-432f-b8e5-5b70cd023b0d","MsgBody":{"Cmd":"abort","InstCode":"sample_14785d372","ActionID":"d2a28ac5-6be5-4a4c-806f-424fc39131ee"}}
25-09-02 17:51:39:831 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-02 17:51:39:832 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee  Stop: {:parent nil}
25-09-02 17:51:39:882 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:51:39:883 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:51:39:885 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9","SubTaskID":"onlyAction-0fd5b761-7c8e-432f-b8e5-5b70cd023b0d","MsgBody":{}}
25-09-02 17:51:39:885 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:51:39:886 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"daq-a47ba0de-3727-4ea7-a523-2fd2fbd8fb7b","MsgBody":{}}
25-09-02 17:51:39:891 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:51:39:892 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_27","ProcessID":"project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9","SubTaskID":"onlyAction-7ab30145-6f3a-4ac1-a4c1-6d9ff9ba49cf","MsgBody":{"Cmd":"start","InstCode":"sample_14785d372","ActionID":"da399466-da97-449d-b998-0d7a0823cdd0"}}
25-09-02 17:51:39:895 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-da399466-da97-449d-b998-0d7a0823cdd0 初始化
25-09-02 17:51:39:942 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:51:39:943 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-da399466-da97-449d-b998-0d7a0823cdd0","SubTaskID":"start-node-action","MsgBody":{}}
25-09-02 17:51:39:945 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:return true;
25-09-02 17:51:39:970 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-02 17:51:39:972 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_27","ScriptId":"b305a1c9-8947-4b83-97f6-8a73c9662de6","Result":true}
25-09-02 17:51:40:171 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:51:40:171 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-da399466-da97-449d-b998-0d7a0823cdd0","SubTaskID":"stop-4e11b297-545d-46fc-9d3e-816533d9cbb8","MsgBody":{}}
25-09-02 17:51:40:178 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:51:40:179 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-da399466-da97-449d-b998-0d7a0823cdd0","SubTaskID":"end-node-action","MsgBody":{}}
25-09-02 17:51:40:180 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-da399466-da97-449d-b998-0d7a0823cdd0  Stop: {:parent {:Type "process-action", :ClassName "project_27", :ProcessID "project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :SubTaskID "onlyAction-7ab30145-6f3a-4ac1-a4c1-6d9ff9ba49cf", :MsgBody {:Cmd "start", :InstCode "sample_14785d372", :ActionID "da399466-da97-449d-b998-0d7a0823cdd0"}}}
25-09-02 17:51:40:225 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:51:40:231 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:51:40:232 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9","SubTaskID":"onlyAction-7ab30145-6f3a-4ac1-a4c1-6d9ff9ba49cf","MsgBody":{}}
25-09-02 17:51:40:365 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:51:40:366 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9","SubTaskID":"SubTaskEvalScript-748b4a68-608f-47f2-9d29-f552a9eddcab","MsgBody":{}}
25-09-02 17:51:40:373 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:51:40:374 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9","SubTaskID":"end-node-action","MsgBody":{}}
25-09-02 17:51:40:376 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9  Stop: {:parent nil}
25-09-02 17:51:40:435 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:51:45:647 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45 初始化
25-09-02 17:51:45:701 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-63526397-7d5a-4d20-8840-4fa2ceee596d 初始化
25-09-02 17:51:45:702 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:51:45:703 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"start-node-action","MsgBody":{}}
25-09-02 17:51:45:763 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-02 17:51:45:764 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-02 17:51:45:766 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:51:45:767 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-02 17:51:45:768 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1  Stop: {:parent nil}
25-09-02 17:51:45:879 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:51:45:880 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-02 17:51:45:880 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-679f70fa-d870-40d9-b121-c759b28044ed  Stop: {:parent nil}
25-09-02 17:51:45:882 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:51:45:884 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-02 17:51:45:885 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee  Stop: {:parent nil}
25-09-02 17:51:45:886 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:51:45:887 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-02 17:51:45:888 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9  Stop: {:parent nil}
25-09-02 17:51:45:890 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:51:45:891 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-02 17:51:45:891 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-da399466-da97-449d-b998-0d7a0823cdd0  Stop: {:parent {:Type "process-action", :ClassName "project_27", :ProcessID "project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :SubTaskID "onlyAction-7ab30145-6f3a-4ac1-a4c1-6d9ff9ba49cf", :MsgBody {:Cmd "start", :InstCode "sample_14785d372", :ActionID "da399466-da97-449d-b998-0d7a0823cdd0"}}}
25-09-02 17:51:45:893 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:51:45:894 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-02 17:51:45:894 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent nil}
25-09-02 17:51:45:911 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SAVE_PROJECT
25-09-02 17:51:45:912 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProjectID":27}
25-09-02 17:51:45:913 DESKTOP-3BSREDP INFO [clj-scheduler.mq:47] - Received SAVE_PROJECT ZMQ message, dispatching to project channel: {:ProjectID 27}
25-09-02 17:51:45:913 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:51:45:914 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4","MsgBody":{}}
25-09-02 17:51:45:915 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4不在执行状态, 而是 :aborted
25-09-02 17:51:45:921 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:51:45:922 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"start-node-action","MsgBody":{}}
25-09-02 17:51:45:943 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:51:45:944 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-02 17:51:45:945 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-63526397-7d5a-4d20-8840-4fa2ceee596d  Stop: {:parent nil}
25-09-02 17:51:45:961 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:51:45:962 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"daq-d5711df9-3137-4a67-9444-060b64552e70","MsgBody":{}}
25-09-02 17:51:45:963 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:51:45:963 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"daqRmc-9c1f5247-99e5-4a62-8446-73b344f248a2","MsgBody":{}}
25-09-02 17:51:46:022 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:51:46:159 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:51:46:160 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4","MsgBody":{}}
25-09-02 17:51:46:186 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:51:46:188 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ifopendivice\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:51:46:195 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:51:46:196 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:51:46:200 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:51:46:201 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"SubTaskEvalScript-8e9f3ddc-e65c-429c-8106-cfe857f6f11a","MsgBody":{}}
25-09-02 17:51:46:291 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:51:46:292 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"SubTaskEvalScript-8e9f3ddc-e65c-429c-8106-cfe857f6f11a","MsgBody":{}}
25-09-02 17:52:44:117 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - TEMPLATE_INST_RSP
25-09-02 17:52:44:118 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"5d96261d-1063-403a-ad4a-794ed942f6dc","code":0,"msg":"\u6A21\u677F\u751F\u6210\u6210\u529F"}
25-09-02 17:52:45:317 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-6a579b71-e5fb-4bef-9569-33de699c61ea 初始化
25-09-02 17:52:45:451 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:52:45:452 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"start-node-action","MsgBody":{}}
25-09-02 17:52:46:866 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:52:46:867 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:52:46:876 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:52:46:877 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:52:46:881 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:52:46:882 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"SubTaskEvalScript-b339a41f-5402-476c-99f1-aac17677e715","MsgBody":{}}
25-09-02 17:52:47:475 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:52:47:476 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_F5\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:52:47:480 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:52:47:481 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_Fmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:52:47:485 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:52:47:486 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_FQ\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:52:47:490 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:52:47:491 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_KQ\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:52:47:495 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:52:47:496 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_KR\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:52:47:500 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:52:47:501 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_pmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:52:47:504 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:52:47:505 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_jzsl\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:52:47:512 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:52:47:513 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: -1,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: -1,\r\n  \u0022Code\u0022: \u0022input_type\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: \u0022\u0022,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:52:47:523 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:52:47:525 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"SubTaskEvalScript-0de23181-201f-40db-ba18-1836ed9ee3fd","MsgBody":{}}
25-09-02 17:52:47:538 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:52:47:539 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"end-node-action","MsgBody":{}}
25-09-02 17:52:47:541 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-02 17:52:47:588 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:52:48:817 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1 初始化
25-09-02 17:52:48:878 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:52:48:878 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"start-node-action","MsgBody":{}}
25-09-02 17:52:48:953 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:52:48:953 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskEvalScript-38c7f155-9116-43d2-97c4-c0952ddf8d40","MsgBody":{}}
25-09-02 17:52:52:464 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:52:52:465 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-02 17:52:52:470 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:52:52:471 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-02 17:52:52:471 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037不在执行状态, 而是 :finished
25-09-02 17:52:52:514 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-02 17:52:52:515 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_ifopendivice\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-02 17:53:24:822 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45 初始化
25-09-02 17:53:24:871 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-63526397-7d5a-4d20-8840-4fa2ceee596d 初始化
25-09-02 17:53:24:874 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:53:24:875 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"start-node-action","MsgBody":{}}
25-09-02 17:53:24:959 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-02 17:53:24:961 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-02 17:53:24:963 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:53:24:964 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-02 17:53:24:965 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1  Stop: {:parent nil}
25-09-02 17:53:25:005 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:53:25:006 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-02 17:53:25:006 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent nil}
25-09-02 17:53:25:070 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:53:25:072 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-02 17:53:25:073 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-63526397-7d5a-4d20-8840-4fa2ceee596d  Stop: {:parent nil}
25-09-02 17:53:25:101 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SAVE_PROJECT
25-09-02 17:53:25:102 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProjectID":27}
25-09-02 17:53:25:102 DESKTOP-3BSREDP INFO [clj-scheduler.mq:47] - Received SAVE_PROJECT ZMQ message, dispatching to project channel: {:ProjectID 27}
25-09-02 17:53:25:103 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:53:25:103 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4","MsgBody":{}}
25-09-02 17:53:25:104 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:53:25:104 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"start-node-action","MsgBody":{}}
25-09-02 17:53:25:105 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:start-node-action不在执行状态, 而是 :aborted
25-09-02 17:53:25:195 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-02 17:53:25:305 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:53:25:306 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"daq-d5711df9-3137-4a67-9444-060b64552e70","MsgBody":{}}
25-09-02 17:53:25:307 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:53:25:307 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"daqRmc-9c1f5247-99e5-4a62-8446-73b344f248a2","MsgBody":{}}
25-09-02 17:53:25:313 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-02 17:53:25:315 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4","MsgBody":{}}
