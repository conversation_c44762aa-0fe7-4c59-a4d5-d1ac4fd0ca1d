﻿using static ScriptEngine.DBHandler.DoubleArrayDBHandler;
using System.Collections.Concurrent;
using System.Text.Json;
using System.Text.Json.Serialization;
using static Logging.CCSSLogger;
using MessagePack;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System.Dynamic;
using System.Globalization;
using System.Xml.Schema;
using CsvHelper;
using System.Text;


namespace ScriptEngine;

/// <summary>
/// 二维数据结构类
/// </summary>
public class DoubleArray
{
    /// <summary>
    /// 列数据结构列表
    /// key：列名称code
    /// value：列数据结构
    /// </summary>
    private readonly ConcurrentDictionary<string, DoubleArrayColumn> _columns = new();
    //列数据
    public ConcurrentDictionary<string, DoubleArrayColumn> Columns { get { return _columns; } }

    /// <summary>
    /// 初始化设定行数
    /// </summary>
    public int InitRowCount { get; set; }

    /// <summary>
    /// 当前数据行数
    /// </summary>
    public int CurrentRowCount => _columns.IsEmpty ? 0 : _columns.Values.First().Values.Count;

    /// <summary>
    /// 列数
    /// </summary>
    public int ColumnCount => _columns.Count;

    /// <summary>
    /// 默认时间列code
    /// </summary>
    private const string DEFAULT_TIME_CODE = "create_time";

    // 二维数组列数据类型常量
    public const string NUMBERTYPETAG = "Number";
    public const string TEXTTYPETAG = "Text";
    public const string DATETIMETYPETAG = "DateTime";
    public const string CHECKBOXTYPETAG = "Checkbox";
    public const string BUTTONTYPETAG = "Button";
    public const string SELECTTYPETAG = "Select";
    // 发UI的回调
    public List<SendToUICallback> SendToUICallbacks = new List<SendToUICallback>();
    public List<SendToUICallback> ClearDoubleArrayValueCallbacks = new List<SendToUICallback>();
    // 发UI的清空
    public List<SendToUICallback> SendToUIClear = new List<SendToUICallback>();
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="initRowCount"></param>
    /// <param name="doubleArrayColumns"></param>
    public DoubleArray(int initRowCount)
    {
        this.InitRowCount = initRowCount;
        // 默认添加一列 create_time
        this.AddColumn(DEFAULT_TIME_CODE, DEFAULT_TIME_CODE, DATETIMETYPETAG, null, null);
    }

    /// <summary>
    /// 编辑默认时间列数据
    /// </summary>
    /// <param name="rowIndex">行索引</param>
    public void UpdateCreateTimeColumnData(int rowIndex)
    {
        // 判断当前create_time列存在且类型正确，并且当前时间等于0L时才会更新默认时间数据
        if (_columns.ContainsKey(DEFAULT_TIME_CODE) &&
            _columns[DEFAULT_TIME_CODE].Values[(int)rowIndex].GetType() == typeof(long) &&
            (long)_columns[DEFAULT_TIME_CODE].Values[(int)rowIndex] == 0L)
        {
            long timesTamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            _columns[DEFAULT_TIME_CODE].UpdateValue((int)rowIndex, timesTamp);
        }
    }

    /// <summary>
    /// 根据输入的类型字符串返回对应的.NET类型
    /// </summary>
    /// <param name="type"></param>
    /// <returns></returns>
    public static Type GetDataType(string type) => type switch
    {
        // 数字类型对应double
        NUMBERTYPETAG => typeof(double),
        // 文本类型对应string
        TEXTTYPETAG => typeof(string),
        // 日期时间类型对应long（假设为Unix时间戳）
        DATETIMETYPETAG => typeof(long),
        // 复选框类型对应bool
        CHECKBOXTYPETAG => typeof(bool),
        // 按钮类型对应string（假设存储按钮文本）
        BUTTONTYPETAG => typeof(string),
        // 选择框类型对应string（假设存储选择的值）
        SELECTTYPETAG => typeof(string),
        // 默认情况下返回double类型
        _ => typeof(double)
    };

    /// <summary>
    /// 根据数据类型返回默认值
    /// </summary>
    /// <param name="dataType"></param>
    /// <returns></returns>
    public static object GetDataTypeDefaultData(Type dataType) => dataType switch
    {
        // 如果数据类型是 double，则返回 0.0
        _ when dataType == typeof(double) => 0.0,
        // 如果数据类型是 string，则返回空字符串
        _ when dataType == typeof(string) => string.Empty,
        // 如果数据类型是 long，则返回 0L
        _ when dataType == typeof(long) => 0L,
        // 如果数据类型是 bool，则返回 false
        _ when dataType == typeof(bool) => false,
        // 对于其他数据类型，默认返回 0.0
        _ => 0.0
    };

    /// <summary>
    /// 深拷贝方法
    /// </summary>
    /// <returns>返回深拷贝的DoubleArray对象</returns>
    public DoubleArray DeepCopy()
    {
        // 创建新的 DoubleArray 对象
        var newDoubleArray = new DoubleArray(this.InitRowCount);

        // 深拷贝 _columns 字典
        foreach (var column in _columns)
        {
            // 对于每个列，创建一个新的 DoubleArrayColumn 对象
            // 深拷贝列的数据列表
            var newColumn = new DoubleArrayColumn(
                column.Value.Name,
                column.Value.Code,
                column.Value.Type,
                column.Value.TypeParams,
                column.Value.CorrelationCode,
                column.Value.DataType,
                new List<object>(column.Value.Values)
            );

            // 将新列添加到新对象的 _columns 字典中
            newDoubleArray._columns.TryAdd(column.Key, newColumn);
        }

        // 返回新的 DoubleArray 对象
        return newDoubleArray;
    }

    /// <summary>
    /// 添加列
    /// </summary>
    /// <param name="name">列名称</param>
    /// <param name="code">列code</param>
    /// <param name="type">列类型</param>
    /// <param name="typeParams">列类型参数</param>
    /// <param name="correlationCode">关联code</param>
    public void AddColumn(string name, string code, string type, Dictionary<string, string>? typeParams, string? correlationCode)
    {
        // 获取对应的.NET类型
        Type dataType = GetDataType(type);

        // 根据 dataType 和 InitRowCount 动态生成初始值
        object defaultValue = GetDataTypeDefaultData(dataType);
        int count = this.CurrentRowCount == 0 ? this.InitRowCount : this.CurrentRowCount;

        // 使用 Enumerable.Repeat 生成指定数量的初始值
        List<object> initData = Enumerable.Repeat(defaultValue, count).ToList();
        _columns.TryAdd(code, new DoubleArrayColumn(name, code, type, typeParams, correlationCode, dataType, initData));
    }

    /// <summary>
    /// 编辑列
    /// 注意：当列的type发生变化，会清空历史数据，新建一个默认值的list
    /// </summary>
    /// <param name="code">列code</param>
    /// <param name="name">列名称</param>
    /// <param name="type">列类型</param>
    /// <param name="typeParams">列类型参数</param>
    /// <param name="correlationCode">关联code</param>
    public void UpdateColumn(string code, string? name = null, string? type = null, Dictionary<string, string>? typeParams = null, string? correlationCode = null)
    {
        if (_columns.TryGetValue(code, out var column))
        {
            if (name != null) column.Name = name;
            if (typeParams != null) column.TypeParams = typeParams;
            if (correlationCode != null) column.CorrelationCode = correlationCode;
            if (type != null && type != column.Type)
            {
                Type dataType = GetDataType(type);
                object defaultValue = GetDataTypeDefaultData(dataType);
                int count = this.CurrentRowCount == 0 ? this.InitRowCount : this.CurrentRowCount;
                List<object> initData = Enumerable.Repeat(defaultValue, count).ToList();

                // 更新对应属性
                column.DataType = dataType;
                column.Values = initData;
                column.Type = type;
            }
        }
    }

    /// <summary>
    /// 删除列
    /// </summary>
    /// <param name="columnCode">列唯一标识code</param>
    public void DeleteColumn(string columnCode)
    {
        if (_columns.ContainsKey(columnCode))
        {
            _columns.TryRemove(columnCode, out _);
        }
    }


    /// <summary>
    /// 获取列对象
    /// </summary>
    /// <param name="columnCode">列的code</param>
    /// <returns>找到的列对象，如果未找到则返回null</returns>
    public DoubleArrayColumn? GetColumnObject(string columnCode)
    {
        _columns.TryGetValue(columnCode, out var value);
        return value;
    }

    /// <summary>
    /// 获取列对象列表
    /// </summary>
    /// <returns>查找列对象列表,没有返回空list</returns>
    public List<DoubleArrayColumn> GetColumnObjects()
    {
        return _columns.Values.ToList();
    }

    /// <summary>
    /// 根据指定列对二维数组数据进行排序
    /// </summary>
    /// <param name="columnCode">用于排序的列唯一标识code</param>
    /// <param name="ascending">是否升序排序，默认为true</param>
    /// <returns>排序成功返回true，失败返回false</returns>
    public bool SortByColumn(string columnCode, bool ascending = true)
    {
        try
        {
            // 检查列是否存在
            if (!_columns.ContainsKey(columnCode))
                return false;

            // 获取当前行数
            int rowCount = CurrentRowCount;
            if (rowCount <= 1)
                return true; // 0或1行数据不需要排序

            // 创建索引数组
            int[] indices = Enumerable.Range(0, rowCount).ToArray();

            // 获取排序列的值
            var sortColumn = _columns[columnCode].Values;

            // 根据排序方向对索引进行排序
            if (ascending)
            {
                Array.Sort(indices, (i, j) => Comparer<object>.Default.Compare(sortColumn[i], sortColumn[j]));
            }
            else
            {
                Array.Sort(indices, (i, j) => Comparer<object>.Default.Compare(sortColumn[j], sortColumn[i]));
            }

            // 对所有列应用排序
            foreach (var column in _columns.Values)
            {
                var originalValues = new List<object>(column.Values);
                for (int i = 0; i < rowCount; i++)
                {
                    column.Values[i] = originalValues[indices[i]];
                }
            }

            return true;
        }
        catch (Exception ex)
        {
            Logger.Error($"二维数组排序出错：{ex}");
            return false;
        }
    }


    /// <summary>
    /// 添加一行数据
    /// </summary>
    /// <param name="rows">包含一行映射数据的字典</param>
    public void AddRowData(Dictionary<string, object> rows)
    {
        // 遍历原始数据，判断添加的数据是否有对应的key
        foreach (var code in _columns.Keys)
        {
            // 检查列是否存在
            if (rows.ContainsKey(code))
            {
                // 添加值到对应的列
                _columns[code].AddValue(rows[code]);

            }
            else
            {
                // 添加的数据中不包含key，添加默认值, 默认时间列更新当前时间戳
                object defaultData = code switch
                {
                    DEFAULT_TIME_CODE => DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                    _ => GetDataTypeDefaultData(_columns[code].DataType)
                };
                _columns[code].AddValue(defaultData);
            }
        }
    }
    /// <summary>
    /// strings中所有列在array中是否存在
    /// </summary>
    /// <param name="strings"></param>
    /// <returns></returns>
    public bool AllkeyContainsKey(string[] strings)
    {
        foreach (var item in strings)
        {
            if (!_columns.Keys.Contains(item))
            {
                Logger.Error($"目标列不存在");
                return false;
            }
        }
        return true;
    }
    /// <summary>
    /// strings中所有列在array中是否存在且为数字类型
    /// </summary>
    /// <param name="strings"></param>
    /// <returns></returns>
    public bool AllkeyContainsKeyAndDouble(string[] strings)
    {
        foreach (var item in strings)
        {
            if (!_columns.Keys.Contains(item))
            {
                Logger.Error($"目标列不存在");
                return false;
            }
            else
            if (_columns[item].Type != "数字")
            {
                Logger.Error($"目标列类型错误");
                return false;
            }
        }
        return true;
    }
    /// <summary>
    /// 根据指定index插入一行数据
    /// </summary>
    /// <param name="rows">包含一行映射数据的字典</param>
    public void InsertRowData(int index, Dictionary<string, object> rows)
    {
        // 遍历原始数据，判断添加的数据是否有对应的key
        foreach (var code in _columns.Keys)
        {
            // 检查列是否存在
            if (rows.ContainsKey(code))
            {
                // 添加值到对应的列
                _columns[code].InsertValue(index, rows[code]);
            }
            else
            {
                // 添加的数据中不包含key，添加默认值, 默认时间列更新当前时间戳
                object defaultData = code switch
                {
                    DEFAULT_TIME_CODE => DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                    _ => GetDataTypeDefaultData(_columns[code].DataType)
                };
                _columns[code].InsertValue(index, defaultData);
            }
        }
    }

    /// <summary>
    /// 编辑一行数据
    /// </summary>
    /// <param name="rowIndex">行索引</param>
    /// <param name="rows">包含一行映射数据的字典</param>
    public void UpdateRowData(int rowIndex, Dictionary<string, object> rows)
    {
        // 遍历每一列数据
        foreach (var item in rows)
        {
            // 检查列是否存在
            if (_columns.ContainsKey(item.Key))
            {
                // 更新指定行索引的列值
                _columns[item.Key].UpdateValue(rowIndex, item.Value);
            }
        }
        // 更新默认时间列数据
        UpdateCreateTimeColumnData(rowIndex);
    }

    /// <summary>
    /// 删除一行数据
    /// </summary>
    /// <param name="rowIndex">行索引</param>
    public void DeleteRowData(int rowIndex)
    {
        foreach (var column in _columns.Values)
        {
            column.DeleteValue(rowIndex);
        }
    }

    /// <summary>
    /// 获取一行数据
    /// </summary>
    /// <param name="rowIndex">行索引</param>
    /// <returns>如果rowIndex索引不正确,返回null,否则返回key-value数据</returns>
    public Dictionary<string, object>? GetOneRowValues(int rowIndex)
    {
        if (rowIndex < 0 || rowIndex >= CurrentRowCount)
            return null;

        Dictionary<string, object> OneRowValues = new();
        foreach (var item in _columns)
        {
            OneRowValues.Add(item.Key, item.Value.Values[rowIndex]);
        }

        return OneRowValues;
    }

    /// <summary>
    /// 获取单元格值
    /// </summary>
    /// <param name="rowIndex">行索引</param>
    /// <param name="columnCode">列唯一标识code</param>
    /// <returns>如果rowIndex索引不正确,返回null,否则返回 单元格值</returns>
    public object? GetCellValue(int rowIndex, string columnCode)
    {
        if (rowIndex < 0 || rowIndex >= CurrentRowCount)
            return null;
        if (!_columns.ContainsKey(columnCode))
            return null;

        return _columns[columnCode].Values[rowIndex];
    }
    //列赋值
    public int SetCellValueDouble(string columnCode, List<object> objects)
    {
        for (int i = 0; i < objects.Count; i++)
        {
            try
            {
                double value = Convert.ToDouble(objects[i]);
            }
            catch (Exception ex)
            {
                Logger.Error($"列类型转换double失败" + ex);
                return -1;
            }
        }
        //判断列的长度是否足够，不够返回-1
        if (_columns[columnCode].Values.Count >= objects.Count)
        {
            for (int i = 0; i < objects.Count; i++)
            {
                double value = Convert.ToDouble(objects[i]);
                _columns[columnCode].Values[i] = value;
            }
            return 0;
        }
        Logger.Error($"目标列长度不足");
        return -1;
    }
    /// <summary>
    /// 预定义的类型映射字典 
    /// </summary>
     private static readonly Dictionary<Type, Func<JsonElement, object?>> TypeConverters = new()
    {
        { typeof(double), element => element.GetDouble() },
        { typeof(string), element => element.GetString() },
        { typeof(long), element => element.GetInt64() },
        { typeof(bool), element => element.GetBoolean() }
    };
   
    /// <summary>
    /// 将元素转换为目标数据类型。
    /// </summary>
    /// <param name="value">值</param>
    /// <param name="columnCode">二维数组列code</param>
    /// <returns>转换后的对象；如果无法转换，则返回原始值。</returns>
    public object? ValueTypeConverter(object value, string columnCode)
    {
        try
        {
            var dataType = _columns[columnCode].DataType;
            return ValueTypeConverter(value, dataType);

        }
        catch (Exception ex)
        {
            Logger.Error($"二维数组列，将 JSON 元素转换为目标数据类型出错：{ex}");
            return value;
        }
    }
    /// <summary>
    /// 将元素转换为目标数据类型。
    /// </summary>
    /// <param name="value">值</param>
    /// <param name="dataType">二维数组列类型</param>
    /// <returns>转换后的对象；如果无法转换，则返回原始值。</returns>
    public object? ValueTypeConverter(object value, Type dataType)
    {
        try
        {
            if (value is JsonElement jsonElement)
            {

                // 使用预定义的类型映射字典进行转换 
                if (TypeConverters.TryGetValue(dataType, out var converter))
                {
                    return converter(jsonElement);
                }

                // 处理 null 值 
                if (jsonElement.ValueKind == JsonValueKind.Null)
                {
                    return null;
                }
            }
            switch (dataType)
            {
                case Type when dataType == typeof(double):
                    if (value.GetType() != typeof(double))
                    {
                        return Convert.ToDouble(value);
                    }
                    break;
                case Type when dataType == typeof(string):
                    if (value.GetType() != typeof(string))
                    {
                        return Convert.ToString(value);
                    }
                    break;
                case Type when dataType == typeof(long):
                    if (value.GetType() != typeof(long))
                    {
                        return Convert.ToInt64(value);
                    }
                    break;
                case Type when dataType == typeof(bool):
                    if (value.GetType() != typeof(bool))
                    {
                        return Convert.ToBoolean(value);
                    }
                    break;
            }
            return value;

        }
        catch (Exception ex)
        {
            Logger.Error($"二维数组列，将 JSON 元素转换为目标数据类型出错：{ex}");
            return value;
        }
    }
    /// <summary>
    /// 修改单个单元格
    /// </summary>
    /// <param name="rowIndex">行索引</param>
    /// <param name="columnCode">列唯一标识code</param>
    /// <param name="value">新值</param>
    /// <exception cref="KeyNotFoundException">列内部名不存在时抛出</exception>
    /// <exception cref="ArgumentOutOfRangeException">行索引无效时抛出</exception>
    /// <returns>成功返回true</returns>
    public bool UpdateCellValue(int rowIndex, string columnCode, object value)
    {
        if (rowIndex < 0 || rowIndex >= CurrentRowCount)
            throw new ArgumentOutOfRangeException(nameof(rowIndex), $"二维数组行索引无效:{rowIndex}");
        if (!_columns.ContainsKey(columnCode))
            throw new KeyNotFoundException($"二维数组列内部名没有找到:{columnCode}");
        
        _columns[columnCode].UpdateValue(rowIndex, ValueTypeConverter(value, columnCode));
        // 更新默认时间列数据
        UpdateCreateTimeColumnData(rowIndex);
        return true;
    }


    /// <summary>
    /// 获取某一行所有列的数据,用于构建发送给前端的数据
    /// </summary>
    /// <param name="rowIndex">行索引</param>
    /// <returns></returns>
    public Dictionary<string, Dictionary<string, object>> GetColumnValues(int rowIndex)
    {
        Dictionary<string, Dictionary<string, object>> OneRowValues = new();
        foreach (var item in _columns)
        {
            OneRowValues.Add(item.Key, new Dictionary<string, object> { { "value", ValueTypeConverter(item.Value.Values[rowIndex], item.Key) } });
        }

        return OneRowValues;
    }
    /// <summary>
    /// 清空二维数组数据，赋值默认值
    /// </summary>
    public void ClearDoubleArrayValue()
    {
        foreach (var doubleArrayColumn in _columns.Values)
        {
            doubleArrayColumn.ClearValue(this.InitRowCount);
        }
        if (ClearDoubleArrayValueCallbacks.Count > 0)
        {
            foreach (var item in ClearDoubleArrayValueCallbacks)
            {
                item.Invoke();
            }
        }
    }
    /// <summary>
    /// 发送清空UI缓存数据命令到UI
    /// </summary>
    /// <returns></returns>
    public bool SendClearDataCmdToUI()
    {
        if (SendToUIClear.Count > 0)
        {
            foreach (var item in SendToUIClear)
            {
                item.Invoke();
            }
        }
        return true;
    }
    public void SendValueToUI()
    {
        if (SendToUICallbacks.Count > 0)
            foreach (var item in SendToUICallbacks)
            {
                item.Invoke();
            }

    }

    /// <summary>
    /// 获取保存到数据库的数据
    /// </summary>
    /// <returns></returns>
    public Dictionary<string, Value> GetSaveDBData()
    {
        Dictionary<string, Value> saveData = new();
        foreach (var item in _columns)
        {
            saveData.Add(item.Key, new Value(item.Value.Type, item.Value.Values));
        }

        return saveData;
    }



    /// <summary>
    /// 二位数组导出Export
    /// </summary>
    /// <param name="Codes"></param>
    /// <param name="Path"></param>
    /// <param name="FileName"></param> <summary>
    /// 
    /// </summary>
    public void ReportExport(List<DoubleArrayExportParam> Codes, string Path, string FileName)
    {
        if (Codes == null || Codes.Count == 0)
        {
            foreach (var item in Columns)
            {
                Codes.Add(new DoubleArrayExportParam(item.Key, item.Value.Name, "", 1));
            }
        }
        //获得文件地址
        string FilePATH = Scripting.Utils.GetFilePATH(Path , FileName , ".xlsx");
        // 提前创建文件流
        using var fs = new FileStream(FilePATH, FileMode.Create, FileAccess.Write);
        using IWorkbook workbook = new XSSFWorkbook();
        // 创建数据页
        CreateSheet(workbook, Codes, FileName);
        workbook.Write(fs);
        GC.Collect();
        GC.WaitForPendingFinalizers();
    }
    public void CreateSheet(IWorkbook workbook, List<DoubleArrayExportParam> Codes, string FileName)
    {
        var sheet = workbook.CreateSheet(FileName);
        var headerRow = sheet.CreateRow(0);
        var unitRow = sheet.CreateRow(1);
        for (int i = 0; i < Codes.Count; i++)
        {
            headerRow.CreateCell(i).SetCellValue(Codes[i].Name);
            unitRow.CreateCell(i).SetCellValue(Codes[i].Unit);
        }
        for (var rowNum = 1; rowNum <= _columns.First().Value.Values.Count; rowNum++)
        {
            var row=sheet.CreateRow(rowNum);
            for (int i = 0; i < Codes.Count; i++)
            {
                object value = _columns[Codes[i].Code].Values[rowNum - 1];
                if (value is JsonElement je)
                {
                    value = ConvertJsonElement(je);
                }
                if (value is double doubleValue)
                {
                    if (Codes[i].Code == "create_time")
                    {
                        var dateTime = DateTimeOffset.FromUnixTimeMilliseconds((long)doubleValue).DateTime.ToString("yyyy-MM-dd HH:mm:ss");
                        row.CreateCell(i).SetCellValue(dateTime);
                    }
                    else
                    {
                        row.CreateCell(i).SetCellValue(doubleValue * Codes[i].Proportion);
                    }
                }
                else if (value is int intValue)
                    row.CreateCell(i).SetCellValue(intValue * Codes[i].Proportion);
                else if (value is string stringValue)
                    row.CreateCell(i).SetCellValue(stringValue);
                else if (value is bool boolValue)
                    row.CreateCell(i).SetCellValue(boolValue ? "是" : "否");
                else
                    row.CreateCell(i).SetCellValue(value?.ToString() ?? "");
            }
        }

    } 
    
    /// <summary>
    /// 导出CSV报表
    /// </summary>
    /// <param name="Codes">导出列</param>
    /// <param name="Path">地址</param>
    /// <param name="FileName">文件名</param> <summary>
    public void CsvExport(List<DoubleArrayExportParam> Codes, string Path, string FileName)
    {
        
        if (Codes == null || Codes.Count == 0)
        {
            foreach (var item in Columns)
            {
                Codes.Add(new DoubleArrayExportParam(item.Key, item.Value.Name, "", 1));
            }
        }
        // 示例代码（如为实例方法）：
        // 获得文件地址
        var filePath = Scripting.Utils.GetFilePATH(Path, FileName, ".csv");
        // 写入UTF-8 BOM
        using var fs = new FileStream(filePath, FileMode.Create, FileAccess.Write);
        using var writer = new StreamWriter(fs, Encoding.GetEncoding("GBK")); // true表示带BOM
        using var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);

        // 写表头
        foreach (var code in Codes)
        {
            csv.WriteField(code.Name);
        }
        csv.NextRecord();
        // 写单位
        foreach (var code in Codes)
        {
            csv.WriteField(code.Unit);
        }
        csv.NextRecord();
        // 假设所有列长度一致
        int rowCount = Columns[Codes[0].Code].Values.Count;
        for (int i = 0; i < rowCount; i++)
        {
            foreach (var code in Codes)
            {
                var col = Columns[code.Code];
                var value=col.Values[i];
                 if (value is JsonElement je)
                {
                    value = ConvertJsonElement(je);
                }
                if (value is double doubleValue)
                {
                    if (code.Code == "create_time")
                    {
                        var dateTime = DateTimeOffset.FromUnixTimeMilliseconds((long)doubleValue).DateTime.ToString("yyyy-MM-dd HH:mm:ss");
                        csv.WriteField(dateTime);
                       
                    }
                    else
                    {
                        csv.WriteField(doubleValue* code.Proportion);
                    }
                }
                else if (value is int intValue)
                    csv.WriteField(intValue * code.Proportion);
                else if (value is string stringValue)
                    csv.WriteField(col.Values[i]);
                else if (value is bool boolValue)
                    csv.WriteField(boolValue ? "是" : "否");
                else
                    csv.WriteField(col.Values[i]);
               
            }
            csv.NextRecord();
        }
        GC.Collect();
        GC.WaitForPendingFinalizers();
    }
    /// <summary>
    /// 导出时将JsonElement转换为合适的数据类型
    /// </summary>
    /// <param name="jsonElement">要转换的JsonElement</param>
    /// <returns>转换后的值</returns>
    private object ConvertJsonElement(JsonElement jsonElement)
    {
        try
        {
            return jsonElement.ValueKind switch
            {
                JsonValueKind.Number =>
                    jsonElement.TryGetInt32(out var intVal) ? intVal : jsonElement.GetDouble(),
                JsonValueKind.String => jsonElement.GetString() ?? string.Empty,
                JsonValueKind.True => "是",
                JsonValueKind.False => "否",
                JsonValueKind.Null => "",
                _ => jsonElement.ToString()
            };
        }
        catch (Exception ex)
        {
            Logger.Error($"JsonElement转换失败: {ex.Message}");
            return jsonElement.ToString();
        }
    }
}

/// <summary>
/// 二维数组列结构类
/// </summary>
public class DoubleArrayColumn
{
    /// <summary>
    /// 显示名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 内部名
    /// </summary>
    public string Code { get; set; }

    /// <summary>
    /// 列类型-数字、文本、日历、下拉选择、checkbox、按钮
    /// </summary>
    public string Type { get; set; }

    /// <summary>
    /// 类型参数
    /// </summary>
    public Dictionary<string, string>? TypeParams { get; set; }

    /// <summary>
    /// 伴随变量内部名
    /// </summary>
    public string? CorrelationCode { get; set; }

    /// <summary>
    /// 列数据类型
    /// </summary>
    [JsonIgnore]
    public Type DataType { get; set; }
    /// <summary>
    /// 列值列表
    /// </summary>
    public List<object> Values { get; set; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="name"></param>
    /// <param name="code"></param>
    /// <param name="type"></param>
    /// <param name="typeParams"></param>
    /// <param name="correlationCode"></param>
    /// <param name="dataType"></param>
    /// <param name="values"></param>
    public DoubleArrayColumn(string name, string code, string type, Dictionary<string, string>? typeParams, string? correlationCode, Type dataType, List<object> values)
    {
        this.Name = name;
        this.Code = code;
        this.Type = type;
        this.TypeParams = typeParams;
        this.CorrelationCode = correlationCode;
        this.DataType = dataType;
        this.Values = values;
    }

    /// <summary>
    /// 列数据添加值
    /// </summary>
    /// <param name="value">值</param>
    public void AddValue(object value)
    {
        // 判断数据类是否正确
        if (value != null && value.GetType() != DataType)
        {
            Logger.Error($"二维数组 {Code} 列,添加数据的数据类型不正确: 原始数据类型 {DataType}, 传入数据类型 {value.GetType()}");
        }
        else
        {
            Values.Add(value);
        }
    }

    public void InsertValue(int index, object value)
    {
        // 判断数据类是否正确
        if (value != null && value.GetType() != DataType)
        {
            Logger.Error($"二维数组 {Code} 列,添加数据的数据类型不正确: 原始数据类型 {DataType}, 传入数据类型 {value.GetType()}");
        }
        else
        {
            if (index >= 0 && index < Values.Count)
            {
                Values.Insert(index, value);
            }
        }
    }

    /// <summary>
    /// 列数据编辑值
    /// </summary>
    /// <param name="index">索引</param>
    /// <param name="value">值</param>
    public void UpdateValue(int index, object value)
    {
        // 判断索引是否正确，数据类型是否正确
        if (value != null && value.GetType() != DataType)
        {
            Logger.Error($"二维数组 {Code} 列,编辑数据的数据类型不正确: 原始数据类型 {DataType}, 传入数据类型 {value.GetType()}");
        }
        else
        {
            if (index >= 0 && index < Values.Count)
            {
                Values[index] = value;
            }
        }
    }

    /// <summary>
    /// 列数据删除值
    /// </summary>
    /// <param name="index">索引</param>
    public void DeleteValue(int index)
    {
        // 判断索引是否正确
        if (index >= 0 && index < Values.Count)
        {
            Values.RemoveAt(index);
        }
    }

    /// <summary>
    /// 清空数据，赋值默认值
    /// </summary>
    public void ClearValue(int count)
    {
        // 清空数据
        Values.Clear();
        // 获取当前类型默认值
        object defaultValue = DoubleArray.GetDataTypeDefaultData(this.DataType);
        // 将数据赋值为默认值
        for (int i = 0; i < count; i++)
        {
            Values.Add(defaultValue);
        }
    }

}

/// <summary>
/// 二维数组发送到前端的消息结构
/// </summary>
[MessagePackObject]
public class SendToUIMessage
{
    public SendToUIMessage()
    {
    }
    /// <summary>
    /// 项目ID
    /// </summary>
    [Key("ProcessID")]
    public string ProcessID { get; set; }

    /// <summary>
    /// 二维数组code标识
    /// </summary>
    [Key("SourceID")]
    public string? SourceID { get; set; }

    /// <summary>
    /// 二维数组集合code标识
    /// </summary>
    [Key("SourceSetID")]
    public string? SourceSetID { get; set; }

    /// <summary>
    /// 二维数组集合需要更新的index
    /// </summary>
    [Key("SourcesetIndex")]
    public int? SourcesetIndex { get; set; }

    /// <summary>
    /// 输入命令
    /// All_Update 全局更新（全量模式）
    /// Row_Append 行增加  （增量模式）
    /// </summary>
    [Key("OutputCmd")]
    public string OutputCmd { get; set; }

    /// <summary>
    /// 全量模式下，增量模式下，数据内容
    /// </summary>
    [Key("OutputMsg")]
    public List<Dictionary<string, Dictionary<string, object>>> OutputMsg { get; set; }

    /// <summary>
    /// 构造函数
    /// </summary>
    public SendToUIMessage(
        string processID,
        int sendMode,
        List<Dictionary<string, Dictionary<string, object>>> outputMsg,
        string? sourceID = null,
        string? sourceSetID = null,
        int? sourcesetIndex = null)
    {
        ProcessID = processID;
        SourceID = sourceID;
        OutputCmd = GetOutputCmdStr(sendMode);
        OutputMsg = outputMsg;
        SourceSetID = sourceSetID;
        SourcesetIndex = sourcesetIndex;
    }

    /// <summary>
    /// 获取输入命令
    /// </summary>
    /// <param name="sendMode">发送模式</param>
    /// <returns></returns>
    public static string GetOutputCmdStr(int sendMode) => sendMode switch
    {
        0 => "All_Update",
        1 => "Row_Append",
        _ => "All_Update"
    };
}
public delegate void SendToUICallback();
public record DoubleArrayExportParam(string Code = null,string Name = null,string Unit = null,double Proportion = 1);