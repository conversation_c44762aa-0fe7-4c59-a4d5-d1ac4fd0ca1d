## 目标
以最小改动替换 ResetDynamicDataTable 为 DROP+CREATE（来自 sqlite_master 的原始 DDL），提升大数据量重置速度；保持对外 API 与调用点不变。

## 将修改/新增的文件
- 修改：MaterialPlat/ScriptEngine/DBHandler/DBOperations.cs
  - 替换私有方法 ResetDynamicDataTable 的实现为：
    1) 查询 sqlite_master 获取原 DDL
    2) DROP TABLE IF EXISTS
    3) 使用原 DDL CREATE TABLE
    4) 清理 sqlite_sequence（幂等）

## 执行阶段划分
1) 实现替换
   - 完成 DBOperations.ResetDynamicDataTable 核心实现替换
   - 添加必要中文注释（说明并发注意、异常提示）
2) 编译验证
   - 解决编译器可能提示的命名空间/引用问题
3) 基础验证（不启动服务）
   - 针对 SQLite 内核 API 的最小调用路径进行单元测试（或轻量脚本）

## 可能遇到的问题与对策
- 表名不存在：抛出显式异常（“表 xxx 不存在，无法重置”）
- 并发写入占用：保持现有上层流程（RestartBuffer 等）先停写；注释中提示
- sqlite_master.sql 为空：极小概率（损坏或未建表），保障异常路径清晰

## 回滚方案
- 仅替换私有方法实现，无接口变更；如需回滚，恢复为原“DELETE + sqlite_sequence 删除”实现即可。

## 验收标准
- 相同功能：ResetTable / ResetSampleInstTable 行为不变
- 性能：在百万级行的表上，DROP+CREATE 明显快于 DELETE 清空
- 正确性：重置后可立即写入；结构与历史 DDL 一致；无异常日志


