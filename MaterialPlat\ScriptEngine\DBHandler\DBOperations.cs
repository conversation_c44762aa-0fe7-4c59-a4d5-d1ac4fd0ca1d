using System.Data;
using Dapper;
using Logging;
using Newtonsoft.Json;
using ScriptEngine.DBHandler.TableWriters;
using ScriptEngine.InputVar;
using ScriptEngine.InputVar.InputVars;
using Scripting;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace ScriptEngine.DBHandler;

internal static class DbOperations
{
    private const string RECORD_TABLE_DDL = 
        $@"CREATE TABLE table_record (
                sampleCode TEXT,
                bufferCode TEXT,
                tableName TEXT, 
                fields TEXT,
                deleteFlag INTEGER DEFAULT false,
                createTime INTEGER DEFAULT (STRFTIME('%s', 'now', 'localtime')));";

    private const string ADD_RECORD_SQL =
        $@"INSERT INTO table_record (sampleCode, bufferCode, tableName, fields)
           VALUES (@SampleCode, @BufferCode, @TableName, @Fields);";
    
    private const string UPDATE_RECORD_SQL =
        $@"UPDATE table_record 
            SET fields = @Fields
            WHERE sampleCode = @SampleCode AND bufferCode = @BufferCode;";
    
    private const string DELETE_RECORD_SQL =
        $@"UPDATE table_record 
            SET deleteFlag = true
            WHERE tableName = @TableName;";

    private const string SELECT_BY_SAMPLE_SQL =
        $@"SELECT tableName, fields FROM table_record
            WHERE sampleCode = @SampleCode";
    
    private const string SELECT_BY_BUFFER_SQL =
        $@"SELECT tableName, fields FROM table_record
            WHERE bufferCode = @BufferCode";

    private const string SELECT_SQL =
        $@"SELECT tableName, fields FROM table_record
            WHERE sampleCode = @SampleCode AND bufferCode = @BufferCode";
    
    /// <summary>
    /// 创建记录表
    /// </summary>
    private static void CreateRecordTable(this IDbConnection connection)
    {
        connection.Execute(RECORD_TABLE_DDL);
    }
    
    /// <summary>
    /// 创建动态数据表
    /// </summary>
    /// <param name="connection"></param>
    /// <param name="sampleCode"></param>
    /// <param name="bufferCode"></param>
    /// <param name="bufferFields"></param>
    private static void CreateDynamicDataTable(this IDbConnection connection, string sampleCode, string bufferCode, string[] bufferFields)
    {
        // TODO: 拼接表名可能有不支持字符
        var tableName = sampleCode + bufferCode;
        Console.WriteLine("创建表: " + tableName);
        connection.Execute(ADD_RECORD_SQL, new
        {
            SampleCode = sampleCode,
            BufferCode = bufferCode,
            TableName = tableName,
            Fields = JsonSerializer.Serialize(bufferFields)
        });

        var fieldsSql = bufferFields.Aggregate(
            "",
            (current, field) => current + ",\n" + field + " REAL");
        var ddl = @$"CREATE TABLE {tableName}(
                        create_time INTEGER,
                        `index` INTEGER,
                        `abnormal` INTEGER,
                        `computer_time` TEXT
                        {fieldsSql});";//computer_time TEXT, // 计算机时间戳

        connection.Execute(ddl);
    }

    /// <summary>
    /// 更新动态数据表
    /// </summary>
    /// <param name="connection"></param>
    /// <param name="sampleCode"></param>
    /// <param name="bufferCode"></param>
    /// <param name="bufferFields"></param>
    private static void UpdateDynamicDataTable(this IDbConnection connection, string sampleCode, string bufferCode, string[] bufferFields)
    {
        var tableName = sampleCode + bufferCode;
        var dbFields = JsonConvert.DeserializeObject<string[]>(
            connection.QuerySingle<string>($"SELECT fields FROM table_record WHERE  tableName = '{tableName}';"));
        var newFields = dbFields == null ? bufferFields : bufferFields.Except(dbFields).ToArray();
        // 更新记录
        connection.Execute(UPDATE_RECORD_SQL, new
        {
            SampleCode = sampleCode,
            BufferCode = bufferCode,
            Fields = JsonSerializer.Serialize(bufferFields)
        });
        // 更新动态数据表
        foreach (var field in newFields)
        {
            connection.Execute($"ALTER TABLE {tableName} ADD COLUMN {field} REAL;");
        }
    }

    /// <summary>
    /// 根据试样Code查询试样表
    /// </summary>
    /// <param name="connection">连接对象</param>
    /// <param name="sampleCode">试样Code</param>
    /// <returns></returns>
    public static DynamicDataTable[] SelectDynamicDataTableBySample(this IDbConnection connection, string sampleCode)
    {
        return connection.Query(
            SELECT_BY_SAMPLE_SQL, 
            new { SampleCode = sampleCode})
            .Select(row => new DynamicDataTable(row.tableName, JsonConvert.DeserializeObject<string[]>(row.fields)))
            .ToArray();
    }

    /// <summary>
    /// 根据试样Code查询试样表
    /// </summary>
    /// <param name="connection">连接对象</param>
    /// <param name="bufferCode"></param>
    /// <returns></returns>
    public static DynamicDataTable[] SelectDynamicDataTableByBuffer(this IDbConnection connection, string bufferCode)
    {
        return connection.Query(
                SELECT_BY_BUFFER_SQL, 
                new { BufferCode = bufferCode})
            .Select(row => new DynamicDataTable(row.tableName, JsonConvert.DeserializeObject(row.fields)))
            .ToArray();
    }

    /// <summary>
    /// 根据试样Code查询试样表
    /// </summary>
    /// <param name="connection">连接对象</param>
    /// <param name="sampleCode"></param>
    /// <param name="bufferCode"></param>
    /// <returns></returns>
    public static DynamicDataTable SelectDynamicDataTable(this IDbConnection connection,string sampleCode, string bufferCode)
    {
        var dbRow = connection.QuerySingle(SELECT_SQL,
            new
            {
                SampleCode = sampleCode,
                BufferCode = bufferCode
            });
        return new DynamicDataTable(dbRow.tableName, JsonConvert.DeserializeObject<string[]>(dbRow.fields));
    }

    /// <summary>
    /// 根据时间戳在表中查询idx
    /// </summary>
    public static long GetDataByTime(this IDbConnection connection,string sampleCode, string bufferCode, long targetTime)
    {
        var selectDataSql = 
            @$"SELECT `index` FROM {sampleCode + bufferCode}
         ORDER BY ABS(create_time - @TargetTime) LIMIT 1;";
        try
        {
            var dbData = connection.QuerySingle(selectDataSql, new
            {
                SampleCode = sampleCode,
                BufferCode = bufferCode,
                TargetTime = targetTime
            });
            return dbData.index ?? 0;
        }
        catch (Exception e)
        {
            Console.WriteLine(e.Message);
            Console.WriteLine(e.StackTrace);
            return 0;
        }
    }
    
    /// <summary>
    /// 根据idx查询时间
    /// </summary>
    public static long GetDataByIndex(this IDbConnection connection,string sampleCode, string bufferCode, int index)
    {
        // index可能不存在
        var selectDataSql = $"SELECT create_time FROM {sampleCode + bufferCode} WHERE `index` = @Index;";
        var dbData = connection.QuerySingle(selectDataSql, new { Index = index });
        return dbData.create_time ?? 0;
    }

    /// <summary>
    /// 删除动态数据表
    /// </summary>
    /// <param name="connection"></param>
    /// <param name="sampleCode"></param>
    /// <param name="bufferCode"></param>
    private static void DropDynamicDataTable(this IDbConnection connection, string sampleCode, string bufferCode)
    {
        // TODO: 拼接表名可能有不支持字符
        connection.DropDynamicDataTable(sampleCode + bufferCode);
    }
    
    private static void DropDynamicDataTable(this IDbConnection connection, string tableName)
    {
        connection.Execute(DELETE_RECORD_SQL, new
        {
            TableName = tableName
        });
        
        var dropSql = @$"DROP TABLE {tableName};";
        connection.Execute(dropSql);
    }

    /// <summary>
    /// 重置动态数据表（性能优化版）：
    /// 1. 从 sqlite_master 读取原始建表语句
    /// 2. DROP TABLE IF EXISTS
    /// 3. 使用原建表语句重新创建
    /// 4. 清理 sqlite_sequence 记录（幂等）
    /// 注意：调用前需确保上层已停止该表的并发写入（如 RestartBuffer 流程），避免持有预编译句柄导致 DROP 失败。
    /// </summary>
    /// <param name="connection">数据库连接</param>
    /// <param name="tableName">目标表名</param>
    private static void ResetDynamicDataTable(this IDbConnection connection, string tableName)
    {
        // 读取原始建表语句（使用参数化避免 SQL 注入风险）
        var tableInfo = connection.QuerySingleOrDefault<dynamic>(
            "SELECT sql FROM sqlite_master WHERE type='table' AND name=@TableName;",
            new { TableName = tableName });

        if (tableInfo == null)
            throw new Exception($"表 {tableName} 不存在，无法重置。");

        // Dapper dynamic 返回 dynamic 对象，直接强转为 string
        var createTableSql = (string)tableInfo.sql;
        if (string.IsNullOrWhiteSpace(createTableSql))
            throw new Exception($"未能从 sqlite_master 读取到 {tableName} 的建表语句，无法重置。");

        // 删除并重建表
        connection.Execute($"DROP TABLE IF EXISTS {tableName};");
        connection.Execute(createTableSql);

        // 清理 sqlite_sequence（若表没有自增主键记录，此操作也是幂等的）
        connection.Execute("DELETE FROM sqlite_sequence WHERE name = @TableName;", new { TableName = tableName });
    }
    // =================================================================================================================
    /// <summary>
    /// 初始化动态数据表
    /// </summary>
    /// <param name="connection"></param>
    /// <param name="template"></param>
    public static void InitDynamicDataTable(this IDbConnection connection, ITemplate template)
    {
        // using (var transaction = connection.BeginTransaction())
        // {
            try
            {
                connection.CreateRecordTable();
                foreach (var (sampleCode, sampleInst) in template.SampleInsts)
                    foreach (var bufferInputVar in sampleInst.GetBufferInputVars())
                        connection.CreateDynamicDataTable(sampleCode, bufferInputVar.Code, bufferInputVar.SignalCodes);
                // transaction.Commit();
            }
            catch (Exception ex)
            {   
                CCSSLogger.Logger.Error(ex.Message);
                // transaction.Rollback();
            } 
        // }
        // TODO: 临时在init的时候整理data库的内存
        // connection.Execute("VACUUM;");
        
    }

    /// <summary>
    /// 创建试样动态数据表
    /// </summary>
    /// <param name="connection"></param>
    /// <param name="sampleInst"></param>
    public static void CreateSampleInstTable(this IDbConnection connection, SampleInst.SampleInst sampleInst)
    {
        // using var transaction = connection.BeginTransaction();

        try
        {
            foreach (var bufferInputVar in sampleInst.GetBufferInputVars())
            {
                connection.CreateDynamicDataTable(
                    sampleInst.Code,
                    bufferInputVar.Code, bufferInputVar.SignalCodes);
            }
            // transaction.Commit();
        }
        catch(Exception ex)
        {
            CCSSLogger.Logger.Error(ex.Message);
            // transaction.Rollback();
        }
    }

    /// <summary>
    /// 删除试样动态数据表
    /// </summary>
    /// <param name="connection"></param>
    /// <param name="sampleInst"></param>
    public static void DropSampleInstTable(this IDbConnection connection, SampleInst.SampleInst sampleInst)
    {
        // using var transaction = connection.BeginTransaction();
        try
        {
            var tables = connection.SelectDynamicDataTableBySample(sampleInst.Code);
            foreach (var table in tables)
            {
                connection.DropDynamicDataTable(table.TableName);
            }
            // transaction.Commit();
        }
        catch(Exception ex)
        {
            CCSSLogger.Logger.Error(ex.Message);
            // transaction.Rollback();
        }
    }
    
    /// <summary>
    /// 更新试样数据表
    /// </summary>
    /// <param name="connection"></param>
    /// <param name="sampleInst"></param>
    public static void ResetSampleInstTable(this IDbConnection connection, SampleInst.SampleInst sampleInst)
    {
        // using var transaction = connection.BeginTransaction();
        try
        {
            var tables = connection.SelectDynamicDataTableBySample(sampleInst.Code);
            foreach (var table in tables)
            {
                connection.ResetDynamicDataTable(table.TableName);
            }
            // transaction.Commit();
        }
        catch(Exception ex)
        {
            CCSSLogger.Logger.Error(ex.Message);
            // transaction.Rollback();
        }
    }

    /// <summary>
    /// 更新试样数据表
    /// </summary>
    /// <param name="connection"></param>
    /// <param name="sampleInstCode"></param>
    /// <param name="bufferCode"></param>
    public static void ResetTable(this IDbConnection connection, string sampleInstCode, string bufferCode)
    {
        // using var transaction = connection.BeginTransaction();
        try
        {
            var tableName = sampleInstCode + bufferCode;
            connection.ResetDynamicDataTable(tableName);
            // transaction.Commit();
        }
        catch(Exception ex)
        {
            CCSSLogger.Logger.Error(ex.Message);
            // transaction.Rollback();
        }
    }

    /// <summary>
    /// 创建Buffer动态数据表
    /// </summary>
    /// <param name="connection"></param>
    /// <param name="buffer"></param>
    /// <param name="template"></param>
    public static void CreateBufferTable(this IDbConnection connection,  BufferInputVar buffer, ITemplate template)
    {
        // using var transaction = connection.BeginTransaction();
        try
        {
            foreach (var (_, sampleInst) in template.SampleInsts)
            {
                connection.CreateDynamicDataTable(sampleInst.Code, buffer.Code, buffer.SignalCodes);   
            }
            // transaction.Commit();
        }
        catch(Exception ex)
        {
            CCSSLogger.Logger.Error(ex.Message);
            // transaction.Rollback();
        }
    }

    /// <summary>
    /// 创建Buffer动态数据表
    /// </summary>
    /// <param name="connection"></param>
    /// <param name="buffer"></param>
    /// <param name="sampleInst"></param>
    public static void CreateBufferTable(this IDbConnection connection,  BufferInputVar buffer, SampleInst.SampleInst sampleInst)
    {
        // using var transaction = connection.BeginTransaction();
        try
        {
            connection.CreateDynamicDataTable(sampleInst.Code, buffer.Code, buffer.SignalCodes);
            // transaction.Commit();
        }
        catch(Exception ex)
        {
            CCSSLogger.Logger.Error(ex.Message);
            // transaction.Rollback();
        }
    }

    /// <summary>
    /// 删除Buffer动态数据表
    /// </summary>
    /// <param name="connection"></param>
    /// <param name="buffer"></param>
    public static void DropBufferInstTable(this IDbConnection connection, BufferInputVar buffer)
    {
        // using var transaction = connection.BeginTransaction();
        try
        {
            var tables = connection.SelectDynamicDataTableBySample(buffer.Code);
            foreach (var table in tables)
            {
                connection.DropDynamicDataTable(table.TableName);
            }
            // transaction.Commit();
        }
        catch(Exception ex)
        {
            CCSSLogger.Logger.Error(ex.Message);
            // transaction.Rollback();
        }
    }

    /// <summary>
    /// 更新Buffer动态数据表
    /// </summary>
    /// <param name="connection"></param>
    /// <param name="buffer"></param>
    /// <param name="template"></param>
    public static void UpdateBufferInstTable(this IDbConnection connection, BufferInputVar buffer, ITemplate template) 
    {
        // using var transaction = connection.BeginTransaction();
        try
        {
            if (buffer.IsOverall)
            {
                // 全局变量修改所有试样
                foreach (var (_, sampleInst) in template.SampleInsts)
                {
                    // 实例化内容修改
                    // TODO: 实例化内容修改应该由buffer修改时调用
                    sampleInst.RefreshTableWriter(template, buffer.Code, buffer.SignalCodes);
                    // 持久化内容修改
                    connection.UpdateDynamicDataTable(sampleInst.Code, buffer.Code, buffer.SignalCodes);
                } 
            }
            else
            {
                // 非全局变量, 修改当前试样
                var sampleInst = template.SampleInsts[buffer.InstCode];
                connection.UpdateDynamicDataTable(sampleInst.Code, buffer.Code, buffer.SignalCodes);
            }
            // transaction.Commit();
        }
        catch(Exception ex)
        {
            CCSSLogger.Logger.Error(ex.Message);
            // transaction.Rollback();
        }
    }

    /// <summary>
    /// 整理数据库冗余内存
    /// </summary>
    public static void Vacuum(this IDbConnection connection)
    {
        // connection.Execute("VACUUM");
    }
    
}