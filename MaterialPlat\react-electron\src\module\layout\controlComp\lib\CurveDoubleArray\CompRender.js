import React, {
    useEffect, useRef, useState, useMemo,
    cache
} from 'react'
import { useSelector } from 'react-redux'
import { message } from 'antd'

import useInputVariableValueByCode from '@/hooks/project/inputVariable/useInputVariableValueByCode'

import { cloneDeep } from 'lodash'
import { Container } from './style'
import Render from './render'
import ContextMenu from './contextMenu'
import Setting from './setting'
import MarkingModal from './markingModal'
import { useManualMarking } from './hooks/useManualMarking'
import { PROPORTION_TYPE, SOURCE_TYPE } from './constants/constants'

const CurveDoubleArray = ({
    id, layoutConfig,
    config: initalConfig, compStatus,
    updateConfig,
    isBufferCurve,
    isRightClick
}) => {
    const openExperiment = useSelector(state => state.subTask.openExperiment)
    const optSample = useSelector(state => state.project.optSample)

    // 自定义坐标源
    const openDefineAxis = initalConfig?.defineAxis?.isDefineAxis

    const variableValue = useInputVariableValueByCode(openDefineAxis ? initalConfig?.defineAxis?.inputCode : null)

    // 试验中轴应用自定义配置  非试验应用数据范围/上下限范围
    const config = useMemo(() => {
        if (!initalConfig) {
            return null
        }

        if (openExperiment && !openDefineAxis) {
            return initalConfig
        }

        const res = cloneDeep(initalConfig)

        if (!openExperiment) {
            res.xAxis.proportionType = initalConfig?.xAxis?.proportionType === PROPORTION_TYPE.上下限范围 ? PROPORTION_TYPE.上下限范围 : PROPORTION_TYPE.数据范围
            res.yAxis.proportionType = initalConfig?.yAxis?.proportionType === PROPORTION_TYPE.上下限范围 ? PROPORTION_TYPE.上下限范围 : PROPORTION_TYPE.数据范围
            res.y2Axis.proportionType = initalConfig?.y2Axis?.proportionType === PROPORTION_TYPE.上下限范围 ? PROPORTION_TYPE.上下限范围 : PROPORTION_TYPE.数据范围
        }

        if (openDefineAxis) {
            // res.xAxis.proportionType = PROPORTION_TYPE.数据范围
            const targetAxisConfig = res.defineAxis.source.find(i => i.value === variableValue)

            if (!targetAxisConfig) {
                message.error('定义坐标源 - 未找到当前选项对应配置')
                return res
            }

            if (!targetAxisConfig?.xSignal) {
                message.error('定义坐标源 - 当前配置未找到x轴信号')
                return res
            }

            if (!targetAxisConfig?.ySignal || targetAxisConfig?.ySignal.length === 0) {
                message.error('定义坐标源 - 当前配置未找到y轴信号')
                return res
            }

            res.xAxis.name = targetAxisConfig?.xName
            res.yAxis.name = targetAxisConfig?.yName
            res.curveGroup.yAxis.xSignal = targetAxisConfig.xSignal
            res.curveGroup.yAxis.xUnit = targetAxisConfig.xUnit
            res.curveGroup.yAxis.ySignal = targetAxisConfig.ySignal

            Object.keys(res.curveGroup.yAxis.curves).forEach(key => {
                res.curveGroup.yAxis.curves[key].lines = res.curveGroup.yAxis.curves[key].lines.map((l, lIndex) => {
                    return {
                        ...l,
                        yUnit: res?.base.sourceType === SOURCE_TYPE.单数据源 ? null : targetAxisConfig.yUnit,
                        code: targetAxisConfig.ySignal[lIndex]
                    }
                })
            })

            res.curveGroup.y2Axis.isEnable = false
        }

        return res
    }, [openExperiment, initalConfig, openDefineAxis, variableValue])

    const renderRef = useRef()

    // 开启十字线
    const [openCross, setOpenCross] = useState(false)
    // 设置断裂点
    const [openBreak, setOpenBreak] = useState(false)
    // 设置过断裂点
    const [openBreakPoint, setOpenBreakPoint] = useState(false)

    // 显示标签 - 从配置中读取初始状态
    const showPointTag = (config?.pointTag?.open && !openExperiment) || false
    const showChunkTag = (config?.chunkTag?.open && !openExperiment) || false

    // 锁定状态
    const [isLocked, setIsLocked] = useState(false)
    // 使用手工标定hook
    const {
        isMarking,
        markingModalOpen,
        setMarkingModalOpen,
        handleActivateMarking,
        handleStopMarking,
        handleMarkingOk,
        handleMarkingStep,
        resetMarking
    } = useManualMarking(config?.marker)

    // 打开配置弹窗
    const [open, setOpen] = useState(false)

    // 试验状态变化 控件配置变化  重置状态
    useEffect(() => {
        initState()
    }, [initalConfig, openExperiment, resetMarking])

    // 单数据源切换试样时 重置状态
    useEffect(() => {
        if (initalConfig?.base?.sourceType === SOURCE_TYPE.单数据源) {
            initState()
        }
    }, [optSample, isBufferCurve, initalConfig?.base?.sourceType])

    const initState = () => {
        setOpenCross(false)
        setOpenBreak(false)
        setOpenBreakPoint(false)
        setIsLocked(false)
        resetMarking()
    }

    // 处理点标签显示隐藏的配置同步
    const setShowPointTag = (isShow) => {
        if (initalConfig && showPointTag !== isShow) {
            const newConfig = {
                ...initalConfig,
                compStatus,
                pointTag: {
                    ...initalConfig.pointTag,
                    open: isShow
                }
            }
            updateConfig(newConfig)
        }
    }

    // 处理标签块显示隐藏的配置同步
    const setShowChunkTag = (isShow) => {
        if (initalConfig && showChunkTag !== isShow) {
            const newConfig = {
                ...initalConfig,
                compStatus,
                chunkTag: {
                    ...initalConfig.chunkTag,
                    open: isShow
                }
            }
            updateConfig(newConfig)
        }
    }

    const updateCompStatus = (status) => {
        if (initalConfig) {
            const newConfig = {
                ...initalConfig,
                compStatus: status
            }
            updateConfig(newConfig)
        }
    }

    return (
        <Container
            id={id}
        >
            {
                config && (
                    <Render
                        ref={renderRef}
                        isBufferCurve={isBufferCurve}
                        id={id}
                        config={config}
                        compStatus={compStatus}
                        updateCompStatus={updateCompStatus}
                        openCross={openCross}
                        openBreak={openBreak}
                        showPointTag={showPointTag}
                        showChunkTag={showChunkTag}
                        setOpenBreakPoint={setOpenBreakPoint}
                        isLocked={isLocked}
                        isMarking={isMarking}
                        onMarkingStep={handleMarkingStep}
                    />
                )
            }

            {
                isRightClick && (
                    <ContextMenu
                        domId={id}
                        layoutConfig={layoutConfig}
                        config={initalConfig}
                        isBufferCurve={isBufferCurve}
                        setOpen={setOpen}
                        openCross={openCross}
                        setOpenCross={setOpenCross}
                        openBreak={openBreak}
                        setOpenBreak={setOpenBreak}
                        showPointTag={showPointTag}
                        setShowPointTag={setShowPointTag}
                        showChunkTag={showChunkTag}
                        setShowChunkTag={setShowChunkTag}
                        openBreakPoint={openBreakPoint}
                        setOpenBreakPoint={setOpenBreakPoint}
                        onRestore={() => renderRef.current?.restore?.()}
                        onClearBreakPoint={() => renderRef.current?.clearBreakPoint?.()}
                        isLocked={isLocked}
                        setIsLocked={setIsLocked}
                        isMarking={isMarking}
                        onActivateMarking={handleActivateMarking}
                        onStopMarking={handleStopMarking}
                        capture
                    />
                )
            }

            {
                open && (
                    <Setting
                        open={open}
                        setOpen={setOpen}
                        config={initalConfig}
                        updateConfig={(newConfig) => {
                            updateConfig({
                                ...newConfig,
                                compStatus
                            })
                        }}
                        isBufferCurve={isBufferCurve}
                    />
                )
            }

            {
                markingModalOpen && (
                    <MarkingModal
                        open={markingModalOpen}
                        setOpen={setMarkingModalOpen}
                        onOk={handleMarkingOk}
                    />
                )
            }
        </Container>
    )
}

export default CurveDoubleArray
