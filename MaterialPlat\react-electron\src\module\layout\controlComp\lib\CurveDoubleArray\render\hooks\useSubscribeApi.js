import { useMemo, useRef, useEffect } from 'react'
import { useSelector } from 'react-redux'

import useLifecycleAPI, { DATA_SROUCE_TYPE_MAP } from '@/hooks/controlComp/useLifecycleAPI'
import useSample from '@/hooks/useSample'
import { message } from 'antd'

import { SOURCE_TYPE } from '../../constants/constants'

const useSubscribeApi = ({
    isBufferCurve,
    id, config, chartXYRef
}) => {
    const openExperiment = useSelector(state => state.subTask.openExperiment)
    const optSample = useSelector(state => state.project.optSample)
    const multiSample = useSelector(state => state.project.multiSample)
    const { handleSampleData, getSamples } = useSample()

    // 当前曲线所有通道
    const dataCodes = useMemo(() => {
        if (!config?.curveGroup) return []

        const codes = new Set()

        Object.values(config.curveGroup).forEach(curveItem => {
            // 只处理启用的曲线
            if (curveItem.isEnable) {
                // 添加x轴信号
                if (curveItem.xSignal) {
                    codes.add(curveItem.xSignal)
                }

                // 添加y轴信号
                if (curveItem.ySignal && Array.isArray(curveItem.ySignal)) {
                    curveItem.ySignal.forEach(yAxisCode => {
                        if (yAxisCode) {
                            codes.add(yAxisCode)
                        }
                    })
                }
            }
        })

        return Array.from(codes)
    }, [config])

    // 数据类型
    const dataSourceType = useMemo(() => {
        if (isBufferCurve) {
            return DATA_SROUCE_TYPE_MAP.daqbuffer
        }

        return config?.base?.sourceType === SOURCE_TYPE.多数据源 ? DATA_SROUCE_TYPE_MAP.二维数组集合 : DATA_SROUCE_TYPE_MAP.二维数组
    }, [config?.base?.sourceType, isBufferCurve])

    // daq指定的试样code
    const daqCurveSelectedSampleCodes = useMemo(() => {
        if (isBufferCurve && config?.base?.sourceType === SOURCE_TYPE.多数据源) {
            const allSample = getSamples(true)
            if (multiSample?.length > 0) {
                return allSample.filter(f => multiSample.includes(f.id)).map(f => f.code)
            }

            return getSamples(true).map(s => s.code)
        }

        return [optSample.code]
    }, [optSample.code, config?.base?.sourceType, multiSample, getSamples, isBufferCurve])

    // 试验状态
    const testStatus = useMemo(() => {
        // // buffer 结果文件
        // if (isBufferCurve && config?.base?.sourceType === SOURCE_TYPE.多数据源) {
        //     return 0
        // }

        if (openExperiment) {
            return 1
        }

        return 0
    }, [isBufferCurve, openExperiment, config?.base?.sourceType])

    // 订阅
    const { targetRef } = useLifecycleAPI({
        controlCompId: id,
        dataSourceType,
        dataSourceCode: config?.base?.sourceInputCode,
        dataCodes,
        timer: openExperiment ? config?.base?.updateFreq : -1,
        number: -1,
        testStatus,
        daqCurveSelectedSampleCodes
    }, () => {
        // 参数变化清空曲线数据
        chartXYRef.current?.clearAllLine?.()

        // 执行恢复
        chartXYRef.current?.restore?.()
    })

    useEffect(() => {
        if (openExperiment && isBufferCurve && config.base.sourceType === SOURCE_TYPE.多数据源) {
            message.info('buffer曲线结果文件下试验中不显示实时曲线，在试验结束后会呈现在曲线图中')
        }
    }, [config, openExperiment, isBufferCurve])

    return {
        targetRef
    }
}

export default useSubscribeApi
