import React, {
    useEffect, useMemo, useState, useRef
} from 'react'
import {
    Form, Row, Col, Select, Checkbox, InputNumber, Tree, Input, Switch
} from 'antd'
import cloneDeep from 'lodash/cloneDeep'
import { useSelector } from 'react-redux'

import VModal from '@/components/vModal/index'
import { useTranslation } from 'react-i18next'
import VButton from '@/components/vButton/index'
import { rem } from '@/style/index'
import LineElement from '@/pages/dialog/staticCurveManage/components/line'
import ColorSelector from '@/components/colorSelector/index'

import {
    LINE_STYLE_TYPE, SIGN_STYLE_TYPE
} from '../../../../constants'

const { Item } = Form

const PreviewLine = ({ value }) => {
    let sign

    switch (value?.signType) {
    case SIGN_STYLE_TYPE.o:
        sign = '●'
        break
    case SIGN_STYLE_TYPE['▽']:
        sign = '▼'
        break
    case SIGN_STYLE_TYPE['□']:
        sign = '■'
        break
    default:
        sign = null
        break
    }

    return (
        <LineElement
            color={value?.color}
            thickness={value?.isLine ? value?.lineThickness : 0}
            border={value?.lineType}
            sign={value?.isSign ? sign : null}
        />
    )
}

const CurveSettingButton = ({
    channels, open, setOpen, value, onChange, treeData
}) => {
    const { t } = useTranslation()

    const [form] = Form.useForm()
    const unitList = useSelector(state => state.global.unitList)

    const [selectedKey, setSelectedKey] = useState(null)

    const { colCode, dataSourceKey, colIndex } = useMemo(() => {
        const item = treeData?.map(m => m.children)?.flat()?.find(i => i.key === selectedKey)

        if (!selectedKey || !item) {
            return {}
        }

        return {
            colCode: item.code,
            dataSourceKey: item.dataSourceKey,
            colIndex: item.index
        }
    }, [treeData, selectedKey])

    const units = useMemo(() => {
        const dimensionId = channels?.find(f => f.code === colCode)?.dimensionId
        return unitList.find(f => f.id === dimensionId)?.units ?? []
    }, [channels, unitList, colCode])

    useEffect(() => {
        setSelectedKey(null)

        form.setFieldsValue({ value })
    }, [open])

    const onSubmit = async () => {
        const values = await form.validateFields()

        onChange(values.value)
        setOpen(false)
    }

    return (
        <VModal
            open={open}
            title={t('曲线属性')}
            onCancel={() => setOpen(false)}
            width={rem('1300px')}
            footer={null}
        >
            <div style={{ display: 'flex', flexDirection: 'column' }}>
                <Form
                    form={form}
                    labelAlign="left"
                    disabled={!selectedKey}
                    onValuesChange={(changedValues, allValues) => {
                        // 检查是否有值发生变化
                        if (!changedValues?.value) return

                        // 变化的数据源key
                        const changedDataSourceKey = Object.keys(changedValues.value)[0]

                        const changedLineIndex = changedValues.value[changedDataSourceKey].lines.length - 1

                        // 变化的数据源中的线配置
                        const lineConfig = allValues.value[changedDataSourceKey].lines[changedLineIndex]

                        const lineChangedConfig = changedValues.value[changedDataSourceKey].lines[changedLineIndex]

                        if (!lineConfig) return

                        // 获取当前line的完整配置

                        if (!lineChangedConfig?.yUnit && !lineConfig?.isApply) {
                            return
                        }

                        const newValue = cloneDeep(allValues)

                        if (lineChangedConfig?.yUnit) {
                            // 同步多数据源线y轴单位
                            Object.keys(newValue.value).forEach(dsk => {
                                const lines = newValue.value[dsk]?.lines

                                if (!lines) return

                                lines.forEach((l, index) => {
                                    if (l.code === lineConfig.code) {
                                        // eslint-disable-next-line no-param-reassign
                                        newValue.value[dsk].lines[index].yUnit = lineChangedConfig.yUnit
                                    }
                                })
                            })
                        }

                        // 同步 应用全部
                        if (lineConfig?.isApply) {
                            // 需要排除的字段
                            const excludeFields = ['isApply', 'yUnit']

                            // 获取需要同步的字段
                            const fieldsToSync = Object.keys(lineChangedConfig).filter(field => !excludeFields.includes(field))

                            if (fieldsToSync.length !== 0) {
                                // 同步到所有lines
                                Object.keys(newValue.value).forEach(targetDataSourceKey => {
                                    const targetDataSource = newValue.value[targetDataSourceKey]

                                    if (targetDataSource?.lines) {
                                        targetDataSource.lines.forEach((targetLine, targetLineIndex) => {
                                            if (!targetLine) return

                                            // 同步字段值
                                            fieldsToSync.forEach(field => {
                                                // eslint-disable-next-line no-param-reassign
                                                targetLine[field] = lineChangedConfig[field]
                                            })
                                        })
                                    }
                                })
                            }
                        }

                        // 更新表单值
                        form.setFieldsValue(newValue)
                    }}
                >
                    <div style={{ display: 'flex', height: '500px', overflow: 'hidden' }}>
                        <div
                            style={{
                                width: '300px', borderRight: '1px solid #f0f0f0', overflow: 'scroll'
                            }}
                        >
                            <Item name="value">
                                <Tree
                                    treeData={treeData}
                                    blockNode
                                    defaultExpandAll
                                    selectedKeys={selectedKey ? [selectedKey] : []}
                                    onSelect={(selectedKeys) => {
                                        setSelectedKey(selectedKeys?.[0])
                                    }}
                                />
                            </Item>
                        </div>

                        <div style={{ flex: 1, padding: '16px' }}>
                            <div style={{ marginBottom: '16px' }}>
                                <Row gutter={16}>
                                    <Col span={8}>
                                        <Item
                                            name={['value', dataSourceKey, 'lines', colIndex, 'name']}
                                            label={t('曲线名称')}
                                        >
                                            <Input />
                                        </Item>
                                    </Col>
                                    <Col span={9}>
                                        <Item
                                            name={['value', dataSourceKey, 'lines', colIndex, 'yUnit']}
                                            label={t('单位')}
                                        >
                                            <Select
                                                options={units}
                                                fieldNames={{ label: 'name', value: 'id' }}
                                            />
                                        </Item>
                                    </Col>
                                </Row>
                            </div>

                            <div style={{ display: 'flex', alignItems: 'center', gap: '3px' }}>
                                <Item
                                    name={['value', dataSourceKey, 'lines', colIndex, 'isApply']}
                                    valuePropName="checked"
                                >
                                    <Switch />
                                </Item>
                                <Item label={t('开启时的后续修改会同步到所有曲线')} colon={false} />
                            </div>

                            <div style={{ marginBottom: '16px' }}>
                                <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>{t('曲线点之间的连线')}</div>
                                <Row gutter={16}>
                                    <Col span={6}>
                                        <Item
                                            name={['value', dataSourceKey, 'lines', colIndex, 'isLine']}
                                            valuePropName="checked"
                                        >
                                            <Checkbox>
                                                {t('显示连线')}
                                            </Checkbox>
                                        </Item>
                                    </Col>
                                    <Col span={9}>
                                        <Item
                                            name={['value', dataSourceKey, 'lines', colIndex, 'lineType']}
                                            label={t('线型')}
                                        >
                                            <Select options={Object.keys(LINE_STYLE_TYPE).map(m => ({ label: m, value: LINE_STYLE_TYPE[m] }))} />
                                        </Item>
                                    </Col>
                                    <Col span={9}>
                                        <Item
                                            name={['value', dataSourceKey, 'lines', colIndex, 'lineThickness']}
                                            label={t('线宽')}
                                        >
                                            <InputNumber min={1} addonAfter="mm" />
                                        </Item>
                                    </Col>
                                </Row>
                            </div>
                            <div style={{ marginBottom: '16px' }}>
                                <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>{t('曲线点符号')}</div>
                                <Row gutter={16}>
                                    <Col span={6}>
                                        <Item
                                            name={['value', dataSourceKey, 'lines', colIndex, 'isSign']}
                                            valuePropName="checked"
                                        >
                                            <Checkbox>
                                                {t('显示符号')}
                                            </Checkbox>
                                        </Item>
                                    </Col>
                                    <Col span={9}>
                                        <Item
                                            name={['value', dataSourceKey, 'lines', colIndex, 'signType']}
                                            label={t('符号类型')}
                                        >
                                            <Select options={Object.keys(SIGN_STYLE_TYPE).map(m => ({ label: m, value: SIGN_STYLE_TYPE[m] }))} />
                                        </Item>
                                    </Col>
                                    <Col span={9}>
                                        <Item
                                            name={['value', dataSourceKey, 'lines', colIndex, 'signEach']}
                                            valuePropName="checked"
                                        >
                                            <Checkbox>
                                                {t('每个点显示')}
                                            </Checkbox>
                                        </Item>
                                    </Col>
                                </Row>
                            </div>
                            <div>
                                <Row gutter={16}>
                                    <Col span={12}>
                                        <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>{t('颜色')}</div>
                                        <Item
                                            name={['value', dataSourceKey, 'lines', colIndex, 'color']}
                                        >
                                            <ColorSelector disabled={dataSourceKey === -1} />
                                        </Item>
                                    </Col>
                                    <Col span={12}>
                                        <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>{t('示例预览')}</div>
                                        <Item
                                            name={['value', dataSourceKey, 'lines', colIndex]}
                                        >
                                            <PreviewLine />
                                        </Item>
                                    </Col>
                                </Row>
                            </div>
                        </div>
                    </div>
                </Form>
                <div
                    style={{
                        display: 'flex',
                        justifyContent: 'flex-end', // 水平居中
                        alignItems: 'center', // 垂直居中
                        padding: '16px',
                        borderTop: '1px solid #f0f0f0'
                    }}
                >
                    <VButton onClick={onSubmit} type="primary" style={{ marginRight: '8px' }}>{t('确定')}</VButton>
                    <VButton onClick={() => setOpen(false)} style={{ marginRight: '8px' }}>{t('取消')}</VButton>
                    <VButton>{t('帮助')}</VButton>
                </div>

            </div>
        </VModal>
    )
}

export default CurveSettingButton
