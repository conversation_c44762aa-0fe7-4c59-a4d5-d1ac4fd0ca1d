/* eslint-disable consistent-return */
/* eslint-disable prefer-destructuring */
import React, { useState, useEffect } from 'react'
import {
    Space, Button, Transfer, message
} from 'antd'
import { useTranslation } from 'react-i18next'
import { DeleteOutlined } from '@ant-design/icons'
import { isEmpty } from '@/utils/utils'
import { TransferContentContainer } from './style'

const WayElement = ({
    data,
    noDeleteDatas,
    selectOneWay,
    rowKey,
    onChangeOneWay,
    onChangeDelOneWay,
    oneWayLabel,
    wayRender = () => ''
}) => {
    const getWayClass = (item) => {
        return `list-layout ${selectOneWay?.[rowKey] === item[rowKey] ? 'select' : ''}`
    }

    const { t } = useTranslation()
    return (
        <div className="way-right-layout">
            {data?.map(item => {
                return (
                    <div
                        key={item[rowKey]}
                        className={getWayClass(item)}
                        onClick={(e) => onChangeOneWay(e, item)}
                    >
                        <div
                            className="way-title"
                            title={wayRender(item) || item[oneWayLabel]}
                        >
                            {wayRender(item) || t(item[oneWayLabel])}
                        </div>
                        {
                            !noDeleteDatas?.includes(item[rowKey])
                                ? <DeleteOutlined onClick={(e) => onChangeDelOneWay(e, item)} /> : null
                        }

                    </div>
                )
            })}
        </div>
    )
}

/**
 * @param {Array} targetKeys // 目标源数组
 * @param {Button} isMove // 是否上移，下移, 需要配合onChangeMove回调传递给父组件
 * @param {Func} onChangeMove // 上移下移事件
 * @param {Button} oneWay // 是否右边单选
 * @param {String} oneWayLabel // oneWay渲染列表的名字
 * @param {String} rowKey // key
 * @param {Func} onChangeWay // 右边单选点击事件
 * @param {Func} onChangeDelWay // 右边单选删除事件
 * @returns
 */

const VTransfer = ({
    isMove = false,
    children,
    oneWay,
    oneWayLabel = 'label',
    // 用于触发filterOption的关键字段
    searchValueKey,
    rowKey = 'id',
    onChange,
    targetKeys: initTargetKeys,
    onChangeWay,
    select,
    onChangeDelWay,
    onChangeMove,
    moveChildren,
    wayRender,
    noDeleteDatas,
    ...rest
}) => {
    const { t } = useTranslation()
    const [selectOneWay, setSelectOneWay] = useState({})
    const [targetKeys, setTargetKeys] = useState([])

    useEffect(() => {
        setTargetKeys(initTargetKeys)
    }, [initTargetKeys])
    useEffect(() => {
        setSelectOneWay(select)
    }, [select])

    const onBtnUp = () => {
        if (!isEmpty(selectOneWay)) {
            const fieldData = [...targetKeys]
            const index = fieldData.findIndex(f => f === selectOneWay?.[rowKey])
            if (index !== 0) {
                fieldData[index] = fieldData.splice(index - 1, 1, fieldData[index])[0]
            }
            setTargetKeys(fieldData)
            if (onChangeMove) {
                onChangeMove(fieldData)
            }
        } else {
            message.error(t('请选择'))
        }
    }

    const onBtnDown = () => {
        if (!isEmpty(selectOneWay)) {
            const fieldData = [...targetKeys]
            const index = fieldData.findIndex(f => f === selectOneWay?.[rowKey])
            if (index !== fieldData.length - 1) {
                fieldData[index] = fieldData.splice(index + 1, 1, fieldData[index])[0]
            }
            setTargetKeys(fieldData)
            if (onChangeMove) {
                onChangeMove(fieldData)
            }
        } else {
            message.error(t('请选择'))
        }
    }

    const transferAttributes = () => {
        return {
            listStyle: {
                width: '16vw',
                height: '40vh'
            },
            showSearch: true,
            titles: [t('源数据列表'), t('目标数据列表')],
            showSelectAll: true,
            targetKeys,
            oneWay,
            rowKey: (item) => item[rowKey]
        }
    }

    const onChangeOneWay = (e, data) => {
        e.stopPropagation()
        setSelectOneWay(data)
        if (onChangeWay) {
            onChangeWay(data)
        }
    }

    const onChangeDelOneWay = (e, data) => {
        e.stopPropagation()
        setTargetKeys(targetKeys.filter(f => f !== data[rowKey]))
        if (selectOneWay?.[rowKey] === data[rowKey]) {
            setSelectOneWay({})
            if (onChangeWay) {
                onChangeWay()
            }
        }
        if (onChangeDelWay) {
            onChangeDelWay(data)
        }
    }

    const handleChange = (nextTargetKeys, direction, moveKeys) => {
        setTargetKeys(nextTargetKeys)
        onChange(nextTargetKeys, direction, moveKeys)
    }

    // 自定义过滤函数，实现不区分大小写搜索
    const filterOption = (inputValue, option) => {
        return option?.[searchValueKey]?.toLowerCase().indexOf(inputValue?.toLowerCase()) >= 0
    }

    return (
        <TransferContentContainer>
            <div className="transfer-content">
                <Transfer
                    filterOption={searchValueKey ? filterOption : undefined}
                    {...transferAttributes()}
                    {...rest}
                    onChange={handleChange}
                >
                    {({
                        direction, filteredItems
                    }) => {
                        if (direction === 'right' && oneWay) {
                            return (
                                <WayElement
                                    noDeleteDatas={noDeleteDatas}
                                    data={filteredItems}
                                    selectOneWay={selectOneWay}
                                    rowKey={rowKey}
                                    oneWayLabel={oneWayLabel}
                                    onChangeOneWay={(e, data) => onChangeOneWay(e, data)}
                                    onChangeDelOneWay={(e, data) => onChangeDelOneWay(e, data)}
                                    wayRender={wayRender}
                                />
                            )
                        }
                    }}
                </Transfer>
                {(isMove && oneWay)
                    && (
                        <div className="layout-right">
                            <Space direction="vertical">
                                <Button block onClick={onBtnUp}>{t('上移')}</Button>
                                <Button block onClick={onBtnDown}>{t('下移')}</Button>
                                {moveChildren}
                            </Space>
                        </div>
                    )}

            </div>
        </TransferContentContainer>
    )
}

export default VTransfer
