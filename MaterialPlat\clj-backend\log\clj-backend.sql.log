2025-09-03 09:10:21,298 [main] INFO  jdbc.sqlonly - PRAGMA cache_size = -16000 
 
2025-09-03 09:10:21,300 [main] INFO  jdbc.sqlonly - PRAGMA journal_mode = WAL 
 
2025-09-03 09:10:21,311 [main] INFO  jdbc.sqlonly - PRAGMA synchronous = NORMAL 
 
2025-09-03 09:10:21,311 [main] INFO  jdbc.sqlonly - PRAGMA temp_store = MEMORY 
 
2025-09-03 09:10:21,311 [main] INFO  jdbc.sqlonly - PRAGMA mmap_size = 134217728 
 
2025-09-03 09:10:21,312 [main] INFO  jdbc.sqlonly - PRAGMA page_size = 4096 
 
2025-09-03 09:10:21,372 [main] INFO  jdbc.sqlonly - SELECT * FROM t_system_version WHERE 1 = 1 LIMIT 1; ; 
 
2025-09-03 09:10:21,409 [chime-1] INFO  jdbc.sqlonly - SELECT item.*, lastInspect.inspect_time as last_inspect_time, lastInspectRecord.limit_inspect_time 
as last_limit_inspect_time, device.inspection_device_type_id, device.name as inspection_device_type_name, 
inspection_frequency_days, inspection_remind_days FROM t_inspection_item item left join ( select 
record.inspection_item_id, max(inspect_time) as inspect_time from t_inspection_record record 
INNER JOIN t_inspection_item item on item.delete_flag = 0 and item.inspection_item_id = record.inspection_item_id 
where (record.status != 'changed' or record.status is null) and record.delete_flag = 0 GROUP 
BY record.inspection_item_id ) lastInspect on lastInspect.inspection_item_id = item.inspection_item_id 
left join ( select record.inspection_item_id, max(record.created_time) as created_time, max(record.limit_inspect_time) 
as limit_inspect_time from t_inspection_record record INNER JOIN t_inspection_item item on 
item.delete_flag = 0 and item.inspection_item_id = record.inspection_item_id where (record.status 
!= 'changed' or record.status is null) and record.delete_flag = 0 GROUP BY record.inspection_item_id 
) lastInspectRecord on lastInspectRecord.inspection_item_id = item.inspection_item_id INNER 
JOIN t_inspection_device_type device on device.inspection_device_type_id = item.inspection_device_type_id 
and device.delete_flag = 0 where item.delete_flag = 0 ; 
 
2025-09-03 09:10:21,425 [chime-2] INFO  jdbc.sqlonly - select record.*, lastInspect.inspect_time as last_inspect_time, floor(julianday('now') - julianday(substr(limit_inspect_time,1,10))) 
AS days_difference, (inspection_frequency_days - inspection_remind_days) as remind_period_days 
from t_inspection_record record left join ( select record.inspection_item_id, max(inspect_time) 
as inspect_time from t_inspection_record record INNER JOIN t_inspection_item item on item.delete_flag 
= 0 and item.inspection_item_id = record.inspection_item_id GROUP BY record.inspection_item_id 
) lastInspect on lastInspect.inspection_item_id = record.inspection_item_id where days_difference 
< remind_period_days and (days_difference >= 0 and (record.inspect_time is null or (record.inspect_time 
is not null and floor(julianday('now') - julianday(substr(record.inspect_time,1,10))) = 0))) 
AND record.inspect_time is null order by limit_inspect_time desc, inspection_frequency_days 
asc 
 
2025-09-03 09:10:21,427 [chime-2] INFO  jdbc.sqlonly - select record.*, lastInspect.inspect_time as last_inspect_time, floor(julianday('now') - julianday(substr(limit_inspect_time,1,10))) 
AS days_difference, (inspection_frequency_days - inspection_remind_days) as remind_period_days 
from t_inspection_record record left join ( select record.inspection_item_id, max(inspect_time) 
as inspect_time from t_inspection_record record INNER JOIN t_inspection_item item on item.delete_flag 
= 0 and item.inspection_item_id = record.inspection_item_id GROUP BY record.inspection_item_id 
) lastInspect on lastInspect.inspection_item_id = record.inspection_item_id where days_difference 
< remind_period_days and (days_difference < 0 and (record.inspect_time is null or (record.inspect_time 
is not null and floor(julianday('now') - julianday(substr(record.inspect_time,1,10))) = 0))) 
AND record.inspect_time is null order by limit_inspect_time desc, inspection_frequency_days 
asc 
 
