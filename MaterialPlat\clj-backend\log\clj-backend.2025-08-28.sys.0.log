2025-08-28 10:56:32,385 [XNIO-1 task-4] INFO  clj-backend.env - 
-=[clj-backend has shut down successfully]=- 
2025-08-28 10:56:32,389 [XNIO-1 task-4] INFO  clj-backend.env - 
-=[clj-backend started successfully using the development profile]=- 
2025-08-28 10:59:16,098 [main] INFO  clj-backend.core - -main running ~~ 
2025-08-28 10:59:16,488 [main] INFO  clj-backend.env - 
-=[clj-backend started successfully using the development profile]=- 
2025-08-28 10:59:17,396 [main] INFO  clj-backend.core - Starting monitoring server on port 8181 
2025-08-28 10:59:17,715 [main] INFO  io.undertow - starting server: Undertow - 2.2.20.Final 
2025-08-28 10:59:17,740 [main] INFO  org.xnio - XNIO version 3.8.7.Final 
2025-08-28 10:59:17,942 [main] INFO  org.jboss.threads - JBoss Threads version 3.1.0.Final 
2025-08-28 10:59:18,074 [main] INFO  luminus.http-server - server started on port 3000 
2025-08-28 10:59:18,075 [main] INFO  clj-backend.nrepl - starting nREPL server on port 7000 
2025-08-28 10:59:18,092 [main] INFO  clj-backend.core - #'clj-backend.db.connections/*db-connections started 
2025-08-28 10:59:18,092 [main] INFO  clj-backend.core - #'clj-backend.common.template-utils/connect-template started 
2025-08-28 10:59:18,092 [main] INFO  clj-backend.core - #'clj-backend.events/project-life-cycle-channel started 
2025-08-28 10:59:18,092 [main] INFO  clj-backend.core - #'clj-scheduler.mq/schedulers started 
2025-08-28 10:59:18,092 [main] INFO  clj-backend.core - #'clj-backend.modules.script.service/precompile-scripts started 
2025-08-28 10:59:18,092 [main] INFO  clj-backend.core - #'clj-backend.common.trial/end-time started 
2025-08-28 10:59:18,092 [main] INFO  clj-backend.core - #'clj-backend.common.trial/end-time-time-timestamp started 
2025-08-28 10:59:18,093 [main] INFO  clj-backend.core - #'clj-backend.modules.project.project-service/project-event-listener started 
2025-08-28 10:59:18,093 [main] INFO  clj-backend.core - #'clj-backend.modules.inspection.inspection-record-routes/generate-inspection-record started 
2025-08-28 10:59:18,093 [main] INFO  clj-backend.core - #'clj-backend.modules.inspection.inspection-record-routes/notify-inspect started 
2025-08-28 10:59:18,093 [main] INFO  clj-backend.core - #'clj-backend.handler/init-app started 
2025-08-28 10:59:18,093 [main] INFO  clj-backend.core - #'clj-backend.handler/app-routes started 
2025-08-28 10:59:18,093 [main] INFO  clj-backend.core - #'clj-scheduler.core/scheduler-socket-server started 
2025-08-28 10:59:18,093 [main] INFO  clj-backend.core - #'clj-backend.core/monitoring-server started 
2025-08-28 10:59:18,094 [main] INFO  clj-backend.core - #'clj-backend.core/http-server started 
2025-08-28 10:59:18,094 [main] INFO  clj-backend.core - #'clj-backend.core/repl-server started 
2025-08-28 11:02:02,579 [main] INFO  clj-backend.core - -main running ~~ 
2025-08-28 11:02:02,793 [main] INFO  clj-backend.env - 
-=[clj-backend started successfully using the development profile]=- 
2025-08-28 11:02:03,628 [main] INFO  clj-backend.core - Starting monitoring server on port 8181 
2025-08-28 11:02:03,919 [main] INFO  io.undertow - starting server: Undertow - 2.2.20.Final 
2025-08-28 11:02:03,935 [main] INFO  org.xnio - XNIO version 3.8.7.Final 
2025-08-28 11:02:04,117 [main] INFO  org.jboss.threads - JBoss Threads version 3.1.0.Final 
2025-08-28 11:02:04,239 [main] INFO  luminus.http-server - server started on port 3000 
2025-08-28 11:02:04,241 [main] INFO  clj-backend.nrepl - starting nREPL server on port 7000 
2025-08-28 11:02:04,258 [main] INFO  clj-backend.core - #'clj-backend.db.connections/*db-connections started 
2025-08-28 11:02:04,258 [main] INFO  clj-backend.core - #'clj-backend.common.template-utils/connect-template started 
2025-08-28 11:02:04,258 [main] INFO  clj-backend.core - #'clj-backend.events/project-life-cycle-channel started 
2025-08-28 11:02:04,258 [main] INFO  clj-backend.core - #'clj-scheduler.mq/schedulers started 
2025-08-28 11:02:04,259 [main] INFO  clj-backend.core - #'clj-backend.modules.script.service/precompile-scripts started 
2025-08-28 11:02:04,259 [main] INFO  clj-backend.core - #'clj-backend.common.trial/end-time started 
2025-08-28 11:02:04,259 [main] INFO  clj-backend.core - #'clj-backend.common.trial/end-time-time-timestamp started 
2025-08-28 11:02:04,259 [main] INFO  clj-backend.core - #'clj-backend.modules.project.project-service/project-event-listener started 
2025-08-28 11:02:04,259 [main] INFO  clj-backend.core - #'clj-backend.modules.inspection.inspection-record-routes/generate-inspection-record started 
2025-08-28 11:02:04,259 [main] INFO  clj-backend.core - #'clj-backend.modules.inspection.inspection-record-routes/notify-inspect started 
2025-08-28 11:02:04,259 [main] INFO  clj-backend.core - #'clj-backend.handler/init-app started 
2025-08-28 11:02:04,259 [main] INFO  clj-backend.core - #'clj-backend.handler/app-routes started 
2025-08-28 11:02:04,259 [main] INFO  clj-backend.core - #'clj-scheduler.core/scheduler-socket-server started 
2025-08-28 11:02:04,259 [main] INFO  clj-backend.core - #'clj-backend.core/monitoring-server started 
2025-08-28 11:02:04,259 [main] INFO  clj-backend.core - #'clj-backend.core/http-server started 
2025-08-28 11:02:04,260 [main] INFO  clj-backend.core - #'clj-backend.core/repl-server started 
2025-08-28 14:29:34,101 [main] INFO  clj-backend.core - -main running ~~ 
2025-08-28 14:29:34,333 [main] INFO  clj-backend.env - 
-=[clj-backend started successfully using the development profile]=- 
2025-08-28 14:29:35,180 [main] INFO  clj-backend.core - Starting monitoring server on port 8181 
2025-08-28 14:29:35,463 [main] INFO  io.undertow - starting server: Undertow - 2.2.20.Final 
2025-08-28 14:29:35,488 [main] INFO  org.xnio - XNIO version 3.8.7.Final 
2025-08-28 14:29:35,662 [main] INFO  org.jboss.threads - JBoss Threads version 3.1.0.Final 
2025-08-28 14:29:35,782 [main] INFO  luminus.http-server - server started on port 3000 
2025-08-28 14:29:35,782 [main] INFO  clj-backend.nrepl - starting nREPL server on port 7000 
2025-08-28 14:29:35,799 [main] INFO  clj-backend.core - #'clj-backend.db.connections/*db-connections started 
2025-08-28 14:29:35,800 [main] INFO  clj-backend.core - #'clj-backend.common.template-utils/connect-template started 
2025-08-28 14:29:35,800 [main] INFO  clj-backend.core - #'clj-backend.events/project-life-cycle-channel started 
2025-08-28 14:29:35,800 [main] INFO  clj-backend.core - #'clj-scheduler.mq/schedulers started 
2025-08-28 14:29:35,800 [main] INFO  clj-backend.core - #'clj-backend.modules.script.service/precompile-scripts started 
2025-08-28 14:29:35,800 [main] INFO  clj-backend.core - #'clj-backend.common.trial/end-time started 
2025-08-28 14:29:35,800 [main] INFO  clj-backend.core - #'clj-backend.common.trial/end-time-time-timestamp started 
2025-08-28 14:29:35,801 [main] INFO  clj-backend.core - #'clj-backend.modules.project.project-service/project-event-listener started 
2025-08-28 14:29:35,801 [main] INFO  clj-backend.core - #'clj-backend.modules.inspection.inspection-record-routes/generate-inspection-record started 
2025-08-28 14:29:35,801 [main] INFO  clj-backend.core - #'clj-backend.modules.inspection.inspection-record-routes/notify-inspect started 
2025-08-28 14:29:35,801 [main] INFO  clj-backend.core - #'clj-backend.handler/init-app started 
2025-08-28 14:29:35,801 [main] INFO  clj-backend.core - #'clj-backend.handler/app-routes started 
2025-08-28 14:29:35,801 [main] INFO  clj-backend.core - #'clj-scheduler.core/scheduler-socket-server started 
2025-08-28 14:29:35,801 [main] INFO  clj-backend.core - #'clj-backend.core/monitoring-server started 
2025-08-28 14:29:35,801 [main] INFO  clj-backend.core - #'clj-backend.core/http-server started 
2025-08-28 14:29:35,801 [main] INFO  clj-backend.core - #'clj-backend.core/repl-server started 
2025-08-28 15:00:16,346 [main] INFO  clj-backend.core - -main running ~~ 
2025-08-28 15:00:16,611 [main] INFO  clj-backend.env - 
-=[clj-backend started successfully using the development profile]=- 
2025-08-28 15:00:17,688 [main] INFO  clj-backend.core - Starting monitoring server on port 8181 
2025-08-28 15:00:18,015 [main] INFO  io.undertow - starting server: Undertow - 2.2.20.Final 
2025-08-28 15:00:18,073 [main] INFO  org.xnio - XNIO version 3.8.7.Final 
2025-08-28 15:00:18,282 [main] INFO  org.jboss.threads - JBoss Threads version 3.1.0.Final 
2025-08-28 15:00:18,434 [main] INFO  luminus.http-server - server started on port 3000 
2025-08-28 15:00:18,435 [main] INFO  clj-backend.nrepl - starting nREPL server on port 7000 
2025-08-28 15:00:18,456 [main] INFO  clj-backend.core - #'clj-backend.db.connections/*db-connections started 
2025-08-28 15:00:18,457 [main] INFO  clj-backend.core - #'clj-backend.common.template-utils/connect-template started 
2025-08-28 15:00:18,457 [main] INFO  clj-backend.core - #'clj-backend.events/project-life-cycle-channel started 
2025-08-28 15:00:18,457 [main] INFO  clj-backend.core - #'clj-scheduler.mq/schedulers started 
2025-08-28 15:00:18,457 [main] INFO  clj-backend.core - #'clj-backend.modules.script.service/precompile-scripts started 
2025-08-28 15:00:18,457 [main] INFO  clj-backend.core - #'clj-backend.common.trial/end-time started 
2025-08-28 15:00:18,457 [main] INFO  clj-backend.core - #'clj-backend.common.trial/end-time-time-timestamp started 
2025-08-28 15:00:18,457 [main] INFO  clj-backend.core - #'clj-backend.modules.project.project-service/project-event-listener started 
2025-08-28 15:00:18,458 [main] INFO  clj-backend.core - #'clj-backend.modules.inspection.inspection-record-routes/generate-inspection-record started 
2025-08-28 15:00:18,458 [main] INFO  clj-backend.core - #'clj-backend.modules.inspection.inspection-record-routes/notify-inspect started 
2025-08-28 15:00:18,458 [main] INFO  clj-backend.core - #'clj-backend.handler/init-app started 
2025-08-28 15:00:18,458 [main] INFO  clj-backend.core - #'clj-backend.handler/app-routes started 
2025-08-28 15:00:18,458 [main] INFO  clj-backend.core - #'clj-scheduler.core/scheduler-socket-server started 
2025-08-28 15:00:18,458 [main] INFO  clj-backend.core - #'clj-backend.core/monitoring-server started 
2025-08-28 15:00:18,460 [main] INFO  clj-backend.core - #'clj-backend.core/http-server started 
2025-08-28 15:00:18,460 [main] INFO  clj-backend.core - #'clj-backend.core/repl-server started 
2025-08-28 15:07:39,476 [XNIO-1 task-6] INFO  clj-backend.env - 
-=[clj-backend has shut down successfully]=- 
2025-08-28 15:07:39,478 [XNIO-1 task-6] INFO  clj-backend.env - 
-=[clj-backend started successfully using the development profile]=- 
2025-08-28 15:08:38,091 [main] INFO  clj-backend.core - -main running ~~ 
2025-08-28 15:08:38,347 [main] INFO  clj-backend.env - 
-=[clj-backend started successfully using the development profile]=- 
2025-08-28 15:08:39,291 [main] INFO  clj-backend.core - Starting monitoring server on port 8181 
2025-08-28 15:08:39,585 [main] INFO  io.undertow - starting server: Undertow - 2.2.20.Final 
2025-08-28 15:08:39,602 [main] INFO  org.xnio - XNIO version 3.8.7.Final 
2025-08-28 15:08:39,723 [main] INFO  org.jboss.threads - JBoss Threads version 3.1.0.Final 
2025-08-28 15:08:39,900 [main] INFO  luminus.http-server - server started on port 3000 
2025-08-28 15:08:39,901 [main] INFO  clj-backend.nrepl - starting nREPL server on port 7000 
2025-08-28 15:08:39,920 [main] INFO  clj-backend.core - #'clj-backend.db.connections/*db-connections started 
2025-08-28 15:08:39,921 [main] INFO  clj-backend.core - #'clj-backend.common.template-utils/connect-template started 
2025-08-28 15:08:39,921 [main] INFO  clj-backend.core - #'clj-backend.events/project-life-cycle-channel started 
2025-08-28 15:08:39,921 [main] INFO  clj-backend.core - #'clj-scheduler.mq/schedulers started 
2025-08-28 15:08:39,921 [main] INFO  clj-backend.core - #'clj-backend.modules.script.service/precompile-scripts started 
2025-08-28 15:08:39,923 [main] INFO  clj-backend.core - #'clj-backend.common.trial/end-time started 
2025-08-28 15:08:39,923 [main] INFO  clj-backend.core - #'clj-backend.common.trial/end-time-time-timestamp started 
2025-08-28 15:08:39,923 [main] INFO  clj-backend.core - #'clj-backend.modules.project.project-service/project-event-listener started 
2025-08-28 15:08:39,923 [main] INFO  clj-backend.core - #'clj-backend.modules.inspection.inspection-record-routes/generate-inspection-record started 
2025-08-28 15:08:39,923 [main] INFO  clj-backend.core - #'clj-backend.modules.inspection.inspection-record-routes/notify-inspect started 
2025-08-28 15:08:39,923 [main] INFO  clj-backend.core - #'clj-backend.handler/init-app started 
2025-08-28 15:08:39,923 [main] INFO  clj-backend.core - #'clj-backend.handler/app-routes started 
2025-08-28 15:08:39,923 [main] INFO  clj-backend.core - #'clj-scheduler.core/scheduler-socket-server started 
2025-08-28 15:08:39,923 [main] INFO  clj-backend.core - #'clj-backend.core/monitoring-server started 
2025-08-28 15:08:39,924 [main] INFO  clj-backend.core - #'clj-backend.core/http-server started 
2025-08-28 15:08:39,924 [main] INFO  clj-backend.core - #'clj-backend.core/repl-server started 
2025-08-28 16:39:23,783 [XNIO-1 task-5] INFO  clj-backend.env - 
-=[clj-backend has shut down successfully]=- 
2025-08-28 16:39:23,784 [XNIO-1 task-5] INFO  clj-backend.env - 
-=[clj-backend started successfully using the development profile]=- 
2025-08-28 16:44:11,877 [main] INFO  clj-backend.core - -main running ~~ 
2025-08-28 16:44:12,133 [main] INFO  clj-backend.env - 
-=[clj-backend started successfully using the development profile]=- 
2025-08-28 16:44:13,076 [main] INFO  clj-backend.core - Starting monitoring server on port 8181 
2025-08-28 16:44:13,380 [main] INFO  io.undertow - starting server: Undertow - 2.2.20.Final 
2025-08-28 16:44:13,417 [main] INFO  org.xnio - XNIO version 3.8.7.Final 
2025-08-28 16:44:13,607 [main] INFO  org.jboss.threads - JBoss Threads version 3.1.0.Final 
2025-08-28 16:44:13,757 [main] INFO  luminus.http-server - server started on port 3000 
2025-08-28 16:44:13,757 [main] INFO  clj-backend.nrepl - starting nREPL server on port 7000 
2025-08-28 16:44:13,777 [main] INFO  clj-backend.core - #'clj-backend.db.connections/*db-connections started 
2025-08-28 16:44:13,778 [main] INFO  clj-backend.core - #'clj-backend.common.template-utils/connect-template started 
2025-08-28 16:44:13,778 [main] INFO  clj-backend.core - #'clj-backend.events/project-life-cycle-channel started 
2025-08-28 16:44:13,778 [main] INFO  clj-backend.core - #'clj-scheduler.mq/schedulers started 
2025-08-28 16:44:13,778 [main] INFO  clj-backend.core - #'clj-backend.modules.script.service/precompile-scripts started 
2025-08-28 16:44:13,778 [main] INFO  clj-backend.core - #'clj-backend.common.trial/end-time started 
2025-08-28 16:44:13,778 [main] INFO  clj-backend.core - #'clj-backend.common.trial/end-time-time-timestamp started 
2025-08-28 16:44:13,778 [main] INFO  clj-backend.core - #'clj-backend.modules.project.project-service/project-event-listener started 
2025-08-28 16:44:13,778 [main] INFO  clj-backend.core - #'clj-backend.modules.inspection.inspection-record-routes/generate-inspection-record started 
2025-08-28 16:44:13,780 [main] INFO  clj-backend.core - #'clj-backend.modules.inspection.inspection-record-routes/notify-inspect started 
2025-08-28 16:44:13,780 [main] INFO  clj-backend.core - #'clj-backend.handler/init-app started 
2025-08-28 16:44:13,780 [main] INFO  clj-backend.core - #'clj-backend.handler/app-routes started 
2025-08-28 16:44:13,780 [main] INFO  clj-backend.core - #'clj-scheduler.core/scheduler-socket-server started 
2025-08-28 16:44:13,780 [main] INFO  clj-backend.core - #'clj-backend.core/monitoring-server started 
2025-08-28 16:44:13,780 [main] INFO  clj-backend.core - #'clj-backend.core/http-server started 
2025-08-28 16:44:13,780 [main] INFO  clj-backend.core - #'clj-backend.core/repl-server started 
2025-08-28 17:14:27,974 [clojure-agent-send-off-pool-0] ERROR jdbc.audit - 11. PreparedStatement.execute() SELECT * FROM t_sample_variable WHERE sample_code = 'GLOBAL' and variable_code = 'input_test_state' 
and delete_flag = false; 
 
org.sqlite.SQLiteException: [SQLITE_BUSY]  The database file is locked (cannot commit transaction - SQL statements in progress)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.throwex(DB.java:1007)
	at org.sqlite.core.DB.ensureAutoCommit(DB.java:1097)
	at org.sqlite.core.DB.execute(DB.java:873)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.execute(JDBC3PreparedStatement.java:54)
	at net.sf.log4jdbc.PreparedStatementSpy.execute(PreparedStatementSpy.java:417)
	at next.jdbc.result_set$stmt__GT_result_set.invokeStatic(result_set.clj:663)
	at next.jdbc.result_set$stmt__GT_result_set.invoke(result_set.clj:658)
	at next.jdbc.result_set$eval4526$fn__4534.invoke(result_set.clj:891)
	at next.jdbc.protocols$eval3616$fn__3617$G__3607__3626.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:253)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at hugsql.adapter.next_jdbc.HugsqlAdapterNextJdbc.query(next_jdbc.clj:20)
	at hugsql.adapter$eval1485$fn__1501$G__1467__1506.invoke(adapter.clj:3)
	at hugsql.adapter$eval1485$fn__1501$G__1466__1512.invoke(adapter.clj:3)
	at clojure.lang.Var.invoke(Var.java:401)
	at hugsql.core$db_fn_STAR_$y__2388.doInvoke(core.clj:472)
	at clojure.lang.RestFn.invoke(RestFn.java:448)
	at hugsql.core$db_fn_STAR_$y__2388.invoke(core.clj:462)
	at conman.core$try_query$fn__4846$fn__4847.invoke(core.clj:32)
	at clojure.lang.AFn.applyToHelper(AFn.java:156)
	at clojure.lang.RestFn.applyTo(RestFn.java:135)
	at clojure.core$apply.invokeStatic(core.clj:671)
	at clojure.core$apply.invoke(core.clj:662)
	at clj_backend.modules.variable.input.input_variable_db$eval23515$f__4866__auto____23542.doInvoke(input_variable_db.clj:5)
	at clojure.lang.RestFn.invoke(RestFn.java:428)
	at clj_backend.modules.variable.variable_modified$get_sample_input_update.invokeStatic(variable_modified.clj:15)
	at clj_backend.modules.variable.variable_modified$get_sample_input_update.invoke(variable_modified.clj:11)
	at clj_backend.modules.variable.variable_modified$update_input_variable.invokeStatic(variable_modified.clj:47)
	at clj_backend.modules.variable.variable_modified$update_input_variable.invoke(variable_modified.clj:29)
	at clj_backend.modules.variable.variable_modified$variable_modiffed.invokeStatic(variable_modified.clj:134)
	at clj_backend.modules.variable.variable_modified$variable_modiffed.invoke(variable_modified.clj:122)
	at clj_scheduler.mq$start_receive$fn__32127.invoke(mq.clj:100)
	at clj_scheduler.mq$start_receive.invokeStatic(mq.clj:92)
	at clj_scheduler.mq$start_receive.invoke(mq.clj:89)
	at clj_scheduler.mq$start_script_client$fn__32141.invoke(mq.clj:116)
	at clojure.core$binding_conveyor_fn$fn__5842.invoke(core.clj:2047)
	at clojure.lang.AFn.call(AFn.java:18)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-28 17:14:27,977 [clojure-agent-send-off-pool-0] ERROR jdbc.sqltiming - 11. PreparedStatement.execute() FAILED! SELECT * FROM t_sample_variable WHERE sample_code = 'GLOBAL' and variable_code = 'input_test_state' 
and delete_flag = false; 
 {FAILED after 2 msec} 
org.sqlite.SQLiteException: [SQLITE_BUSY]  The database file is locked (cannot commit transaction - SQL statements in progress)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.throwex(DB.java:1007)
	at org.sqlite.core.DB.ensureAutoCommit(DB.java:1097)
	at org.sqlite.core.DB.execute(DB.java:873)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.execute(JDBC3PreparedStatement.java:54)
	at net.sf.log4jdbc.PreparedStatementSpy.execute(PreparedStatementSpy.java:417)
	at next.jdbc.result_set$stmt__GT_result_set.invokeStatic(result_set.clj:663)
	at next.jdbc.result_set$stmt__GT_result_set.invoke(result_set.clj:658)
	at next.jdbc.result_set$eval4526$fn__4534.invoke(result_set.clj:891)
	at next.jdbc.protocols$eval3616$fn__3617$G__3607__3626.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:253)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at hugsql.adapter.next_jdbc.HugsqlAdapterNextJdbc.query(next_jdbc.clj:20)
	at hugsql.adapter$eval1485$fn__1501$G__1467__1506.invoke(adapter.clj:3)
	at hugsql.adapter$eval1485$fn__1501$G__1466__1512.invoke(adapter.clj:3)
	at clojure.lang.Var.invoke(Var.java:401)
	at hugsql.core$db_fn_STAR_$y__2388.doInvoke(core.clj:472)
	at clojure.lang.RestFn.invoke(RestFn.java:448)
	at hugsql.core$db_fn_STAR_$y__2388.invoke(core.clj:462)
	at conman.core$try_query$fn__4846$fn__4847.invoke(core.clj:32)
	at clojure.lang.AFn.applyToHelper(AFn.java:156)
	at clojure.lang.RestFn.applyTo(RestFn.java:135)
	at clojure.core$apply.invokeStatic(core.clj:671)
	at clojure.core$apply.invoke(core.clj:662)
	at clj_backend.modules.variable.input.input_variable_db$eval23515$f__4866__auto____23542.doInvoke(input_variable_db.clj:5)
	at clojure.lang.RestFn.invoke(RestFn.java:428)
	at clj_backend.modules.variable.variable_modified$get_sample_input_update.invokeStatic(variable_modified.clj:15)
	at clj_backend.modules.variable.variable_modified$get_sample_input_update.invoke(variable_modified.clj:11)
	at clj_backend.modules.variable.variable_modified$update_input_variable.invokeStatic(variable_modified.clj:47)
	at clj_backend.modules.variable.variable_modified$update_input_variable.invoke(variable_modified.clj:29)
	at clj_backend.modules.variable.variable_modified$variable_modiffed.invokeStatic(variable_modified.clj:134)
	at clj_backend.modules.variable.variable_modified$variable_modiffed.invoke(variable_modified.clj:122)
	at clj_scheduler.mq$start_receive$fn__32127.invoke(mq.clj:100)
	at clj_scheduler.mq$start_receive.invokeStatic(mq.clj:92)
	at clj_scheduler.mq$start_receive.invoke(mq.clj:89)
	at clj_scheduler.mq$start_script_client$fn__32141.invoke(mq.clj:116)
	at clojure.core$binding_conveyor_fn$fn__5842.invoke(core.clj:2047)
	at clojure.lang.AFn.call(AFn.java:18)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-28 17:14:28,016 [XNIO-1 task-2] ERROR jdbc.audit - 11. Connection.setAutoCommit(false) 
org.sqlite.SQLiteException: [SQLITE_ERROR] SQL error or missing database (cannot start a transaction within a transaction)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.throwex(DB.java:1007)
	at org.sqlite.core.DB.exec(DB.java:178)
	at org.sqlite.SQLiteConnection.setAutoCommit(SQLiteConnection.java:337)
	at net.sf.log4jdbc.ConnectionSpy.setAutoCommit(ConnectionSpy.java:763)
	at next.jdbc.transaction$transact_STAR_.invokeStatic(transaction.clj:70)
	at next.jdbc.transaction$transact_STAR_.invoke(transaction.clj:51)
	at next.jdbc.transaction$eval4714$fn__4715.invoke(transaction.clj:121)
	at next.jdbc.protocols$eval3706$fn__3707$G__3697__3716.invoke(protocols.clj:57)
	at next.jdbc$transact.invokeStatic(jdbc.clj:381)
	at next.jdbc$transact.invoke(jdbc.clj:373)
	at clj_backend.common.template_utils$use_template_execute.invokeStatic(template_utils.clj:84)
	at clj_backend.common.template_utils$use_template_execute.doInvoke(template_utils.clj:77)
	at clojure.lang.RestFn.invoke(RestFn.java:445)
	at clj_backend.modules.sample_instance.sample_instance_routes$routes$fn__42308.invoke(sample_instance_routes.clj:196)
	at clj_backend.common.trial$trial_middleware$fn__42702.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52556$fn__52558$fn__52559.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52579$fn__52581$fn__52582.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49535$fn__49536.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52774.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52697.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52701.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52694.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290.invoke(session.clj:88)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18384.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18442.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18740.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18059.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18800.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18883.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18907.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18864.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.adapter.undertow$undertow_handler$fn$reify__58454.handleRequest(undertow.clj:40)
	at io.undertow.server.session.SessionAttachmentHandler.handleRequest(SessionAttachmentHandler.java:68)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:387)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-28 17:14:28,016 [XNIO-1 task-2] ERROR jdbc.sqltiming - 11. Connection.setAutoCommit(false) 
org.sqlite.SQLiteException: [SQLITE_ERROR] SQL error or missing database (cannot start a transaction within a transaction)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.throwex(DB.java:1007)
	at org.sqlite.core.DB.exec(DB.java:178)
	at org.sqlite.SQLiteConnection.setAutoCommit(SQLiteConnection.java:337)
	at net.sf.log4jdbc.ConnectionSpy.setAutoCommit(ConnectionSpy.java:763)
	at next.jdbc.transaction$transact_STAR_.invokeStatic(transaction.clj:70)
	at next.jdbc.transaction$transact_STAR_.invoke(transaction.clj:51)
	at next.jdbc.transaction$eval4714$fn__4715.invoke(transaction.clj:121)
	at next.jdbc.protocols$eval3706$fn__3707$G__3697__3716.invoke(protocols.clj:57)
	at next.jdbc$transact.invokeStatic(jdbc.clj:381)
	at next.jdbc$transact.invoke(jdbc.clj:373)
	at clj_backend.common.template_utils$use_template_execute.invokeStatic(template_utils.clj:84)
	at clj_backend.common.template_utils$use_template_execute.doInvoke(template_utils.clj:77)
	at clojure.lang.RestFn.invoke(RestFn.java:445)
	at clj_backend.modules.sample_instance.sample_instance_routes$routes$fn__42308.invoke(sample_instance_routes.clj:196)
	at clj_backend.common.trial$trial_middleware$fn__42702.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52556$fn__52558$fn__52559.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52579$fn__52581$fn__52582.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49535$fn__49536.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52774.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52697.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52701.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52694.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290.invoke(session.clj:88)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18384.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18442.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18740.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18059.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18800.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18883.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18907.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18864.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.adapter.undertow$undertow_handler$fn$reify__58454.handleRequest(undertow.clj:40)
	at io.undertow.server.session.SessionAttachmentHandler.handleRequest(SessionAttachmentHandler.java:68)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:387)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-28 17:14:28,019 [XNIO-1 task-2] ERROR clj-backend.middleware.exception - [SQLITE_ERROR] SQL error or missing database (cannot start a transaction within a transaction) 
org.sqlite.SQLiteException: [SQLITE_ERROR] SQL error or missing database (cannot start a transaction within a transaction)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.throwex(DB.java:1007)
	at org.sqlite.core.DB.exec(DB.java:178)
	at org.sqlite.SQLiteConnection.setAutoCommit(SQLiteConnection.java:337)
	at net.sf.log4jdbc.ConnectionSpy.setAutoCommit(ConnectionSpy.java:763)
	at next.jdbc.transaction$transact_STAR_.invokeStatic(transaction.clj:70)
	at next.jdbc.transaction$transact_STAR_.invoke(transaction.clj:51)
	at next.jdbc.transaction$eval4714$fn__4715.invoke(transaction.clj:121)
	at next.jdbc.protocols$eval3706$fn__3707$G__3697__3716.invoke(protocols.clj:57)
	at next.jdbc$transact.invokeStatic(jdbc.clj:381)
	at next.jdbc$transact.invoke(jdbc.clj:373)
	at clj_backend.common.template_utils$use_template_execute.invokeStatic(template_utils.clj:84)
	at clj_backend.common.template_utils$use_template_execute.doInvoke(template_utils.clj:77)
	at clojure.lang.RestFn.invoke(RestFn.java:445)
	at clj_backend.modules.sample_instance.sample_instance_routes$routes$fn__42308.invoke(sample_instance_routes.clj:196)
	at clj_backend.common.trial$trial_middleware$fn__42702.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52556$fn__52558$fn__52559.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52579$fn__52581$fn__52582.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49535$fn__49536.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52774.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52697.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52701.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52694.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290.invoke(session.clj:88)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18384.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18442.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18740.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18059.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18800.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18883.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18907.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18864.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.adapter.undertow$undertow_handler$fn$reify__58454.handleRequest(undertow.clj:40)
	at io.undertow.server.session.SessionAttachmentHandler.handleRequest(SessionAttachmentHandler.java:68)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:387)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-28 17:14:28,086 [XNIO-1 task-4] ERROR jdbc.audit - 11. PreparedStatement.execute() PRAGMA wal_checkpoint(TRUNCATE) 
 
org.sqlite.SQLiteException: [SQLITE_LOCKED]  A table in the database is locked (database table is locked)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.execute(DB.java:881)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.execute(JDBC3PreparedStatement.java:54)
	at net.sf.log4jdbc.PreparedStatementSpy.execute(PreparedStatementSpy.java:417)
	at next.jdbc.result_set$stmt__GT_result_set.invokeStatic(result_set.clj:663)
	at next.jdbc.result_set$stmt__GT_result_set.invoke(result_set.clj:658)
	at next.jdbc.result_set$eval4526$fn__4534.invoke(result_set.clj:891)
	at next.jdbc.protocols$eval3616$fn__3617$G__3607__3626.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:250)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint$fn__47922.invoke(cache_utils.clj:19)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invokeStatic(cache_utils.clj:13)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invoke(cache_utils.clj:9)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invokeStatic(cache_utils.clj:49)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invoke(cache_utils.clj:40)
	at clj_backend.modules.project.project_service$save_project.invokeStatic(project_service.clj:414)
	at clj_backend.modules.project.project_service$save_project.invoke(project_service.clj:400)
	at clj_backend.modules.project.project_routes$routes$fn__48216.invoke(project_routes.clj:79)
	at clj_backend.common.trial$trial_middleware$fn__42702.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52556$fn__52558$fn__52559.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52579$fn__52581$fn__52582.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49535$fn__49536.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52774.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52697.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52701.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52694.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290.invoke(session.clj:88)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18384.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18442.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18740.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18059.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18800.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18883.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18907.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18864.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.adapter.undertow$undertow_handler$fn$reify__58454.handleRequest(undertow.clj:40)
	at io.undertow.server.session.SessionAttachmentHandler.handleRequest(SessionAttachmentHandler.java:68)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:387)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-28 17:14:28,087 [XNIO-1 task-4] ERROR jdbc.sqltiming - 11. PreparedStatement.execute() FAILED! PRAGMA wal_checkpoint(TRUNCATE) 
 {FAILED after 0 msec} 
org.sqlite.SQLiteException: [SQLITE_LOCKED]  A table in the database is locked (database table is locked)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.execute(DB.java:881)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.execute(JDBC3PreparedStatement.java:54)
	at net.sf.log4jdbc.PreparedStatementSpy.execute(PreparedStatementSpy.java:417)
	at next.jdbc.result_set$stmt__GT_result_set.invokeStatic(result_set.clj:663)
	at next.jdbc.result_set$stmt__GT_result_set.invoke(result_set.clj:658)
	at next.jdbc.result_set$eval4526$fn__4534.invoke(result_set.clj:891)
	at next.jdbc.protocols$eval3616$fn__3617$G__3607__3626.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:250)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint$fn__47922.invoke(cache_utils.clj:19)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invokeStatic(cache_utils.clj:13)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invoke(cache_utils.clj:9)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invokeStatic(cache_utils.clj:49)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invoke(cache_utils.clj:40)
	at clj_backend.modules.project.project_service$save_project.invokeStatic(project_service.clj:414)
	at clj_backend.modules.project.project_service$save_project.invoke(project_service.clj:400)
	at clj_backend.modules.project.project_routes$routes$fn__48216.invoke(project_routes.clj:79)
	at clj_backend.common.trial$trial_middleware$fn__42702.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52556$fn__52558$fn__52559.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52579$fn__52581$fn__52582.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49535$fn__49536.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52774.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52697.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52701.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52694.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290.invoke(session.clj:88)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18384.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18442.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18740.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18059.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18800.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18883.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18907.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18864.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.adapter.undertow$undertow_handler$fn$reify__58454.handleRequest(undertow.clj:40)
	at io.undertow.server.session.SessionAttachmentHandler.handleRequest(SessionAttachmentHandler.java:68)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:387)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-28 17:17:25,852 [XNIO-1 task-4] ERROR jdbc.audit - 11. PreparedStatement.execute() PRAGMA wal_checkpoint(TRUNCATE) 
 
org.sqlite.SQLiteException: [SQLITE_LOCKED]  A table in the database is locked (database table is locked)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.execute(DB.java:881)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.execute(JDBC3PreparedStatement.java:54)
	at net.sf.log4jdbc.PreparedStatementSpy.execute(PreparedStatementSpy.java:417)
	at next.jdbc.result_set$stmt__GT_result_set.invokeStatic(result_set.clj:663)
	at next.jdbc.result_set$stmt__GT_result_set.invoke(result_set.clj:658)
	at next.jdbc.result_set$eval4526$fn__4534.invoke(result_set.clj:891)
	at next.jdbc.protocols$eval3616$fn__3617$G__3607__3626.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:250)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at clj_backend.db.connections$try_close_db_conn.invokeStatic(connections.clj:112)
	at clj_backend.db.connections$try_close_db_conn.invoke(connections.clj:103)
	at clj_backend.modules.project.project_service$delete_db.invokeStatic(project_service.clj:237)
	at clj_backend.modules.project.project_service$delete_db.invoke(project_service.clj:231)
	at clj_backend.modules.project.project_service$close_project.invokeStatic(project_service.clj:397)
	at clj_backend.modules.project.project_service$close_project.invoke(project_service.clj:376)
	at clj_backend.modules.project.project_routes$routes$fn__48221.invoke(project_routes.clj:85)
	at clj_backend.common.trial$trial_middleware$fn__42702.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52556$fn__52558$fn__52559.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52579$fn__52581$fn__52582.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49535$fn__49536.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52774.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52697.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52701.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52694.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290.invoke(session.clj:88)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18384.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18442.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18740.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18059.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18800.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18883.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18907.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18864.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.adapter.undertow$undertow_handler$fn$reify__58454.handleRequest(undertow.clj:40)
	at io.undertow.server.session.SessionAttachmentHandler.handleRequest(SessionAttachmentHandler.java:68)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:387)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-28 17:17:25,853 [XNIO-1 task-4] ERROR jdbc.sqltiming - 11. PreparedStatement.execute() FAILED! PRAGMA wal_checkpoint(TRUNCATE) 
 {FAILED after 0 msec} 
org.sqlite.SQLiteException: [SQLITE_LOCKED]  A table in the database is locked (database table is locked)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.execute(DB.java:881)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.execute(JDBC3PreparedStatement.java:54)
	at net.sf.log4jdbc.PreparedStatementSpy.execute(PreparedStatementSpy.java:417)
	at next.jdbc.result_set$stmt__GT_result_set.invokeStatic(result_set.clj:663)
	at next.jdbc.result_set$stmt__GT_result_set.invoke(result_set.clj:658)
	at next.jdbc.result_set$eval4526$fn__4534.invoke(result_set.clj:891)
	at next.jdbc.protocols$eval3616$fn__3617$G__3607__3626.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:250)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at clj_backend.db.connections$try_close_db_conn.invokeStatic(connections.clj:112)
	at clj_backend.db.connections$try_close_db_conn.invoke(connections.clj:103)
	at clj_backend.modules.project.project_service$delete_db.invokeStatic(project_service.clj:237)
	at clj_backend.modules.project.project_service$delete_db.invoke(project_service.clj:231)
	at clj_backend.modules.project.project_service$close_project.invokeStatic(project_service.clj:397)
	at clj_backend.modules.project.project_service$close_project.invoke(project_service.clj:376)
	at clj_backend.modules.project.project_routes$routes$fn__48221.invoke(project_routes.clj:85)
	at clj_backend.common.trial$trial_middleware$fn__42702.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52556$fn__52558$fn__52559.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52579$fn__52581$fn__52582.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49535$fn__49536.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52774.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52697.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52701.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52694.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290.invoke(session.clj:88)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18384.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18442.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18740.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18059.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18800.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18883.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18907.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18864.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.adapter.undertow$undertow_handler$fn$reify__58454.handleRequest(undertow.clj:40)
	at io.undertow.server.session.SessionAttachmentHandler.handleRequest(SessionAttachmentHandler.java:68)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:387)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-28 17:23:32,596 [XNIO-1 task-3] INFO  clj-backend.env - 
-=[clj-backend has shut down successfully]=- 
2025-08-28 17:23:32,596 [XNIO-1 task-3] INFO  clj-backend.env - 
-=[clj-backend started successfully using the development profile]=- 
2025-08-28 17:29:06,434 [main] INFO  clj-backend.core - -main running ~~ 
2025-08-28 17:29:06,681 [main] INFO  clj-backend.env - 
-=[clj-backend started successfully using the development profile]=- 
2025-08-28 17:29:07,653 [main] INFO  clj-backend.core - Starting monitoring server on port 8181 
2025-08-28 17:29:07,995 [main] INFO  io.undertow - starting server: Undertow - 2.2.20.Final 
2025-08-28 17:29:08,055 [main] INFO  org.xnio - XNIO version 3.8.7.Final 
2025-08-28 17:29:08,219 [main] INFO  org.jboss.threads - JBoss Threads version 3.1.0.Final 
2025-08-28 17:29:08,344 [main] INFO  luminus.http-server - server started on port 3000 
2025-08-28 17:29:08,346 [main] INFO  clj-backend.nrepl - starting nREPL server on port 7000 
2025-08-28 17:29:08,375 [main] INFO  clj-backend.core - #'clj-backend.db.connections/*db-connections started 
2025-08-28 17:29:08,375 [main] INFO  clj-backend.core - #'clj-backend.common.template-utils/connect-template started 
2025-08-28 17:29:08,376 [main] INFO  clj-backend.core - #'clj-backend.events/project-life-cycle-channel started 
2025-08-28 17:29:08,376 [main] INFO  clj-backend.core - #'clj-scheduler.mq/schedulers started 
2025-08-28 17:29:08,376 [main] INFO  clj-backend.core - #'clj-backend.modules.script.service/precompile-scripts started 
2025-08-28 17:29:08,376 [main] INFO  clj-backend.core - #'clj-backend.common.trial/end-time started 
2025-08-28 17:29:08,376 [main] INFO  clj-backend.core - #'clj-backend.common.trial/end-time-time-timestamp started 
2025-08-28 17:29:08,376 [main] INFO  clj-backend.core - #'clj-backend.modules.project.project-service/project-event-listener started 
2025-08-28 17:29:08,377 [main] INFO  clj-backend.core - #'clj-backend.modules.inspection.inspection-record-routes/generate-inspection-record started 
2025-08-28 17:29:08,377 [main] INFO  clj-backend.core - #'clj-backend.modules.inspection.inspection-record-routes/notify-inspect started 
2025-08-28 17:29:08,377 [main] INFO  clj-backend.core - #'clj-backend.handler/init-app started 
2025-08-28 17:29:08,377 [main] INFO  clj-backend.core - #'clj-backend.handler/app-routes started 
2025-08-28 17:29:08,377 [main] INFO  clj-backend.core - #'clj-scheduler.core/scheduler-socket-server started 
2025-08-28 17:29:08,377 [main] INFO  clj-backend.core - #'clj-backend.core/monitoring-server started 
2025-08-28 17:29:08,377 [main] INFO  clj-backend.core - #'clj-backend.core/http-server started 
2025-08-28 17:29:08,377 [main] INFO  clj-backend.core - #'clj-backend.core/repl-server started 
