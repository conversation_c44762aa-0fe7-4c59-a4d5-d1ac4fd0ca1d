import { useEffect, useMemo, useRef } from 'react'
import { useSelector } from 'react-redux'
import { cloneDeep, isEqual } from 'lodash'

import { config2ChartOption } from '../utils/config2ChartOption'

const usePointTagDynamic = ({
    chartOption, config, chartXYRef, compStatus, updateCompStatus
}) => {
    const currentConfig = useRef()

    const optSample = useSelector(state => state.project.optSample)
    const resultHistoryData = useSelector(state => state.project.resultHistoryData)
    const resultData = useSelector(state => state.template.resultData)

    const pointTagPositionRef = useRef()

    useEffect(() => {
        currentConfig.current = chartOption.markerPoint.map((i) => ([i.id]))
    }, [chartOption])

    useEffect(() => {
        const { markerPoint } = config2ChartOption(config, compStatus)

        if (isEqual(markerPoint, currentConfig.current)) {
            return
        }

        markerPoint.forEach((i) => {
            if (isEqual(currentConfig.current?.find((j) => j.id === i.id), i)) {
                return
            }

            chartXYRef.current.updateAnnotationPosition(i.id, i.pointIndex, i.title, i.position)
        })

        currentConfig.current = markerPoint
    }, [optSample, resultHistoryData, resultData, compStatus])

    // 处理点标签位置持久化
    const updatePointTagPosition = (c) => {
        const newCompStatus = {
            ...(compStatus ?? {}),
            pointTag: {
                ...(compStatus?.pointTag ?? {}),
                position: {
                    ...(compStatus?.pointTag?.position ?? {}),
                    // 保存
                    [c.id]: c.position
                }
            }
        }

        pointTagPositionRef.current = cloneDeep(newCompStatus.pointTag.position)

        updateCompStatus(newCompStatus)
    }

    return {
        updatePointTagPosition
    }
}

export default usePointTagDynamic
