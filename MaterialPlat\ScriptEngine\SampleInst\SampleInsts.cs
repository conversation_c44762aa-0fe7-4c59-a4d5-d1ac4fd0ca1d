using System.Text.Json;
using Dapper;
using Logging;
using Microsoft.Data.Sqlite;
using ScriptEngine.DBHandler.TableWriters;
using ScriptEngine.InputVar;
using ScriptEngine.InputVar.InputVars;
using ScriptEngine.ResultVar;
using Scripting;
using static ScriptEngine.InputVar.InputVar;

namespace ScriptEngine.SampleInst;

public class SampleInst : IDisposable
{
    /// <summary>
    /// 试样Code
    /// </summary>
    public string Code { get; private set; }

    /// <summary>
    /// 试样名称
    /// </summary>
    public string Name { get; private set; }

    /// <summary>
    /// 试样所属分组名
    /// </summary>
    public string GroupName { get; private set; }

    private string _state;

    /// <summary>
    /// 试样状态: "READY" "RUNNING" "FINISHED"
    /// </summary>
    public string State
    {
        get => _state;
        set => _state = value switch
        {
            "running" or "RUNNING" => "RUNNING",
            "finished" or "FINISHED" => "FINISHED",
            _ => "READY"
        };
    }

    /// <summary>
    /// 试样可用性
    /// TODO: 后续可能需要使用脚本修改试样的可用性, 通知UI和后台试样实例被禁用
    /// </summary>
    public bool Available { get; set; }

    /// <summary>
    /// 试样被选中
    /// </summary>
    public bool Checked { get; private set; }

    public bool Selected
    {
        get => Checked;
        internal set => Checked = value;
    }

    /// <summary>
    /// 试样类型
    /// </summary>
    public string SampleType { get; private set; }

    /// <summary>
    /// 试样所属模板名
    /// </summary>
    private string TemplateName { get; set; }

    /// <summary>
    /// 所属模板对象
    /// </summary>
    private ITemplate ParentTemplate { get; set; }

    /// <summary>
    /// 实例化试样是否初始化
    /// </summary>
    internal bool Initialized { get; set; }

    /// <summary>
    /// 试样属性, 需要在脚本中访问
    /// </summary>
    public Dictionary<string, SampleParams> Parameters { get; }

    /// <summary>
    /// 试样输入变量
    /// </summary>
    public Dictionary<string, IInputVar> InputVars { get; }

    /// <summary>
    /// 获取试样的所有Buffer类型InputVar
    /// </summary>
    public BufferInputVar[] GetBufferInputVars()
    {
        return InputVars.Values.Where(inputVar => inputVar.Type == "Buffer")
            .Select(inputVar => (BufferInputVar)inputVar)
            .ToArray();
    }

    /// <summary>
    /// 试样下结果变量
    /// </summary>
    public Dictionary<string, ResultVar.ResultVar> ResultVars { get; private set; }

    /// <summary>
    /// 试样的表写入对象
    /// </summary>
    public Dictionary<string, ITableWriter> TableWriters { get; private set; } = new();

    /// <summary>
    /// 删除试样对象时销毁
    /// </summary>
    public void Dispose()
    {
        foreach (var (_, tableWriter) in TableWriters)
        {
            tableWriter.Dispose();
        }
    }

    /// <summary>
    /// 刷新试样中的需要脚本计算的参数
    /// </summary>
    internal void RefreshScriptParams()
    {
        foreach (var param in Parameters.Select(kvp => kvp.Value).Where(param => param.IsCalculated))
        {
            param.Value = this.RunCode<double>(param.Script);
        }
    }

    /// <summary>
    /// 获取试样下的变量(在脚本中使用)
    /// </summary>
    /// <param name="inputVarName">变量名</param>
    /// <returns>变量值</returns>
    public T GetVarByName<T>(string inputVarName)
    {
        return (T)InputVars[inputVarName];
    }

    /// <summary>
    /// 刷新Writers中的一个Writer
    /// </summary>
    /// <param name="template"></param>
    /// <param name="bufferCode"></param>
    /// <param name="newFields"></param>
    public void RefreshTableWriter(ITemplate template, string bufferCode, string[] newFields)
    {
        var tableName = Code + bufferCode;
        TableWriters[tableName] = new DynamicDataWriter(tableName, newFields);
        template.Db.ResetWriters(TableWriters.Values.ToArray());
    }
    /// <summary>
    /// 重置buffer中的数据
    /// </summary>
    /// <param name="sampleCode">试样Code</param>
    /// <param name="bufferCode">BufferCode</param>
    public virtual void RestartBuffer(string bufferCode)
    {
        ParentTemplate.Db.RestartBuffer(Code, bufferCode);
    }
    /// <summary>
    /// 获取试样在数据库中的历史数据 (脚本中使用)
    /// </summary>
    /// <param name="signalVarCode">信号变量Code</param>
    /// <param name="bufferCode">bufferCode</param>
    public double[] FetchDBSignalVar(string signalVarCode, string bufferCode)
    {
        return ParentTemplate.Db.GetHistoricalData(Code, bufferCode, signalVarCode).ToArray();
    }
    /// <summary>
    /// 获取试样在数据库中的历史数据Object (脚本中使用)
    /// </summary>
    /// <param name="signalVarCode">信号变量Code</param>
    /// <param name="bufferCode">bufferCode</param>
    public object[] FetchDBSignalVarObject(string signalVarCode, string bufferCode)
    {
        return ParentTemplate.Db.GetHistoricalDataObject(Code, bufferCode, signalVarCode).ToArray();
    }
    /// <summary>
    /// 获取多个维度object[] 历史数据
    /// </summary>
    /// <param name="bufferCode">bufferCode</param>
    /// <param name="signalCodes">string[] 信号变量Codes</param>
    public List<object[]> GetHistoricalData(string bufferCode, string[] signalCodes)
    {
        return ParentTemplate.Db.GetHistoricalData(Code + bufferCode, signalCodes).ToList();
    }
    /// <summary>
    /// 获取历史数据总行数
    /// </summary>
    /// <param name="bufferCode">bufferCode</param>
    public int GetHistoricalDataCount(string bufferCode)
    {
        return ParentTemplate.Db.GetHistoricalDataCount(Code + bufferCode);
    }
    /// <summary>
    /// 分页获取历史数据object
    /// </summary>
    /// <param name="tableName">bufferCode</param>
    /// <param name="signalCodes">string[] 信号变量Codes</param>
    /// <param name="pageIndex">第几页</param>
    /// <param name="pageSize">每页数量</param>
    public List<object[]> GetHistoricalDataPaged(string bufferCode, string[] signalCodes, int pageIndex, int pageSize)
    {
        return ParentTemplate.Db.GetHistoricalDataPaged(Code + bufferCode, signalCodes, pageIndex, pageSize).ToList();
    }
    /// <summary>
    /// 获取多个个纬度的历史数据double,用起始终止index获取，startIndex=0,endIndex=100表示获取1-100行数据
    /// </summary>
    /// <param name="bufferCode">bufferCode</param>
    /// <param name="signalCodes">List<string>信号变量Codes</param>
    /// <param name="startIndex">起始Index</param>
    /// <param name="endIndex">结束Index</param>
    public List<double[]> GetHistoricalDataOnlyDouble(string bufferCode, List<string> signalCodes, int startIndex, int endIndex)
    {
        return ParentTemplate.Db.GetHistoricalDataOnlyDouble(Code + bufferCode, signalCodes, startIndex, endIndex).ToList();
    }
    public SampleInst() { }
    public SampleInst(JsonElement sampleInfo, ITemplate template,
        Dictionary<string, IInputVar> globalInputVars, Dictionary<string, DbResultVar>? instResultVars)
    {
        CCSSLogger.Logger.Debug("实例化试样:" + sampleInfo);

        Code = sampleInfo.GetProperty("sample_code").GetString();
        Name = sampleInfo.GetProperty("sample_name").GetString();
        GroupName = sampleInfo.GetProperty("group_name").GetString();
        Available = sampleInfo.GetProperty("available").GetBoolean();
        Checked = sampleInfo.GetProperty("checked").GetBoolean();
        SampleType = sampleInfo.GetProperty("sample_type").GetString();
        State = sampleInfo.GetProperty("state").GetString();
        TemplateName = template.TemplateName;
        ParentTemplate = template;
        Parameters = new Dictionary<string, SampleParams>();
        InputVars = new Dictionary<string, IInputVar>();
        ResultVars = new Dictionary<string, ResultVar.ResultVar>();

        //试样基本参数
        foreach (var element in sampleInfo.GetProperty("sample_params").EnumerateArray())
        {
            var paramName = element.GetProperty("param_name").GetString();
            Parameters[paramName] = new SampleParams(element, TemplateName, this.Code);
        }

        // 试样的输入变量
        foreach (var element in sampleInfo.GetProperty("sample_input_vars").EnumerateArray())
        {
            // 全局变量索引到模板的全局变量
            if (element.GetProperty("IsOverall").GetBoolean())
            {
                var varName = element.GetProperty("Code").GetString();
                InputVars[varName] = globalInputVars[varName];
            }
            // 局部变量在试样中构造
            else
            {
                var inputVar = InputVarFactory.CreateInputVar(element);
                InputVars[inputVar.Code] = inputVar;
            }
        }

        // 试样的结果变量
        foreach (var element in sampleInfo.GetProperty("sample_result_vars").EnumerateArray())
        {
            var resultVarName = element.GetProperty("result_name").GetString();
            DbResultVar dbResultVar = null;
            instResultVars?.TryGetValue(resultVarName, out dbResultVar);
            var resultVar = new ResultVar.ResultVar(element, Code, TemplateName, dbResultVar);
            ResultVars[resultVarName] = resultVar;
        }
        RefreshScriptParams();
    }
    /// <summary>
    /// 结果变量保存到DB
    /// </summary>
    /// <param name="resultVarCodes">结果变量Code数组</param>
    /// <returns></returns> <summary>
    public bool ResultsSaveDB(string[] resultVarCodes)
    {
        Dictionary<string, ResultVar.ResultVar> resultVars = ResultVars
            .Where(x => resultVarCodes.Contains(x.Key)).ToList()
            .ToDictionary(x => x.Key, x => x.Value);
        // 结果变量 -> 数据库结果变变量
        List<DBResultVar> resultVarList = new();
        CCSSLogger.Logger.Info("结果变量计算子任务 存数据库: " + resultVars);
        foreach (var keyValuePair in resultVars)
        {
            resultVarList.Add(new(keyValuePair.Value));
        }
        using (SqliteConnection connection = ParentTemplate.Db.Connection)
        {
            connection.Open();
            CCSSLogger.Logger.Info("数据库连接");
            // 插入示例数据
            var data = new
            {
                createtime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString(),
                sample_instance_code = Code,
                message = JsonSerializer.Serialize(resultVarList),
                // daqCode = daqCode
            };
            connection.Execute("INSERT INTO Results (createtime, sample_instance_code, message) VALUES (@createtime, @sample_instance_code, @message)", data);
        }

        return true;
    }

    /// <summary>
    /// 试样实例的标准构造
    /// </summary>
    public SampleInst(ITemplate template, SampleInstDTO dto,
        Dictionary<string, IInputVar> globalInputVars)
    {
        CCSSLogger.Logger.Debug($"模板{template.TemplateName} 实例化试样: \n {dto}");

        // 试样属性
        Code = dto.Code;
        Name = dto.Name;
        GroupName = dto.GroupName;
        SampleType = dto.SampleType;
        Available = dto.Available ?? true;
        Checked = dto.Checked ?? false;
        State = dto.State ?? "READY";
        Parameters = new Dictionary<string, SampleParams>();
        InputVars = new Dictionary<string, IInputVar>();
        ResultVars = new Dictionary<string, ResultVar.ResultVar>();

        // 试样所属模板
        TemplateName = template.TemplateName;
        ParentTemplate = template;

        // 试样参数
        foreach (var (code, parameterDTO) in dto.Parameters)
            if (parameterDTO != null) Parameters[code] = new SampleParams(parameterDTO, TemplateName, Code);
        // 输入变量
        foreach (var (code, inputVarDTO) in dto.InputVars)
        {
             // 全局变量索引到模板的全局变量
            if (inputVarDTO.IsOverall == true)
            {

                InputVars.Add(code , globalInputVars[code]);
            }
            // 局部变量在试样中构造
            else
            {
                InputVars.Add(code, InputVarFactory.CreateInputVar(inputVarDTO));
              
            }
        }
        // 结果变量
        foreach (var (code, resultVarDTO) in dto.ResultVars)
            if (resultVarDTO != null) ResultVars.Add(code, new ResultVar.ResultVar(resultVarDTO, TemplateName, Code));

    }

    public void UpdateSampleInst(ITemplate template, SampleInstDTO dto)
    {
        CCSSLogger.Logger.Debug($"模板{template.TemplateName} 编辑实例化试样: \n {dto}");

        // 试样属性
        Name = dto.Name;
        GroupName = dto.GroupName;
        SampleType = dto.SampleType;
        if (dto.Available != null) Available = (bool)dto.Available;
        if (dto.Checked != null) Checked = dto.Checked.Value;
        State = dto.State ?? "READY";

        // 试样所属模板
        TemplateName = template.TemplateName;
        ParentTemplate = template;

        // 试样参数
        foreach (var (code, parameterDTO) in dto.Parameters)
        {
            if (Parameters.TryGetValue(code, out var sampleParam))
                sampleParam.UpdateSampleParams(parameterDTO);
            else
                Parameters.Add(code, new SampleParams(parameterDTO, TemplateName, Code));
        }
        // 输入变量
        foreach (var (code, inputVarDTO) in dto.InputVars)
        {
            if (InputVars.TryGetValue(code, out var inputVar))
                InputVarFactory.UpdateInputVar(inputVar, inputVarDTO);
            else
            {
                if (inputVarDTO == null) InputVars.Remove(code);
                else InputVars.Add(code, InputVarFactory.CreateInputVar(inputVarDTO));
            }
        }
        // 结果变量
        foreach (var (code, resultVarDTO) in dto.ResultVars)
        {
            if (ResultVars.TryGetValue(code, out var resultVar))
                resultVar.UpdateResultVar(resultVarDTO);
            else
            {
                if (resultVarDTO == null) ResultVars.Remove(code);
                else ResultVars.Add(code, new ResultVar.ResultVar(resultVarDTO, TemplateName, Code));
            }
        }
    }

}
