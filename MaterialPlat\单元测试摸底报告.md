# MaterialPlat项目单元测试摸底报告

## 概述

本报告对MaterialPlat目录下所有C#单元测试项目进行了全面摸底调研，为下一步单测整合改造提供基础信息。

**调研时间**: 2025年1月
**目标框架**: .NET 6.0
**主要测试框架**: NUnit 3.x

## 测试项目总览

### 已识别的测试项目列表

| 序号 | 项目名称 | 目标项目 | 测试框架 | 状态 | 调整建议 | 备注 |
|------|----------|----------|----------|------|----------|------|
| 1 | DBUtils.Test | DBUtils | NUnit 3.13.2 | 活跃 | **合并到DBUtils.Tests** | Excel图表和SQLite连接测试 |
| 2 | DBUtils.Tests | DBUtils | NUnit 3.13.3 | 活跃 | **保留(合并目标)** | 数据库操作和NetMQ消息测试 |
| 3 | FuncLibs.Tests | FuncLibs | NUnit 3.13.2 | 简单 | **合并到FuncLibsTests** | 仅JSON性能测试 |
| 4 | FuncLibsTests | FuncLibs | NUnit 3.13.2 | 活跃 | **保留(合并目标)** | 大量性能和序列化测试 |
| 5 | HardwareConnector.Tests | HardwareConnector | NUnit 3.13.3 | 活跃 | **可忽略** | 硬件连接器测试 |
| 6 | HwSim.Test | HwSim16 | NUnit 3.13.2 | 活跃 | **可忽略** | 硬件模拟器测试 |
| 7 | Logger.Tests | Logger | NUnit 3.13.2 | 活跃 | **保留** | 日志系统测试 |
| 8 | ScriptEngine.Test | ScriptEngine | NUnit 3.13.3 | 活跃 | **合并到ScriptEngine.Tests** | 脚本引擎测试 |
| 9 | ScriptEngine.Tests | ScriptEngine | xUnit 2.7.0 | 活跃 | **保留(合并目标)** | 脚本引擎扩展测试(使用xUnit) |
| 10 | SubTasks.Tests | SubTasks | NUnit 3.13.3 | 活跃 | **保留** | 子任务测试 |
| 11 | SubTaskList.Tests | SubTaskList | xUnit 2.4.2 | 活跃 | **保留** | 子任务列表测试(使用xUnit) |
| 12 | DbHandlerPerformanceTests | ScriptEngine | 性能测试 | 活跃 | **可忽略** | 数据库性能基准测试 |

## 详细分析

### 1. DBUtils.Test 项目

**项目路径**: `MaterialPlat\DBUtils.Test\`
**目标框架**: .NET 6.0
**测试框架**: NUnit 3.13.2

**主要依赖包**:
- ClosedXML 0.102.3 (Excel操作)
- Dapper 2.0.123 (ORM)
- Microsoft.Data.Sqlite 7.0.10
- NPOI 2.7.1 (Office文档处理)
- System.Data.SQLite 1.0.118

**测试内容**:
- `ExcelChartTest.cs`: Excel图表创建测试，使用NPOI库测试线图创建功能
- `SqliteConnectionTest.cs`: SQLite数据库连接测试，包括频繁连接创建、多线程连接测试

**测试特点**:
- 重点测试数据库连接性能和稳定性
- 包含Excel文件操作的集成测试
- 有多线程并发测试场景

### 2. DBUtils.Tests 项目

**项目路径**: `MaterialPlat\DBUtils.Tests\`
**目标框架**: .NET 6.0
**测试框架**: NUnit 3.13.3

**主要依赖包**:
- NetMQ ******** (消息队列)
- System.Reactive 5.0.0 (响应式编程)
- NLog 5.1.1 (日志)

**测试内容**:
- `DatabaseTest.cs`: 数据库操作测试，包括Observable写入数据库、NetMQ消息序列化/反序列化
- 测试NetMQ消息的数据库存储和检索
- 包含信号变量值更新测试

**测试特点**:
- 集成了消息队列和数据库的测试
- 使用响应式编程模式进行测试
- 部分测试被标记为Ignore（需要本地SQLite数据库）

### 3. FuncLibs.Tests 项目

**项目路径**: `MaterialPlat\FuncLibs.Tests\`
**目标框架**: .NET 6.0
**测试框架**: NUnit 3.13.2

**测试内容**:
- `JsonPerformanceTest.cs`: JSON反序列化性能测试

**测试特点**:
- 项目规模较小，仅包含性能测试
- 专注于JSON处理性能优化

### 4. FuncLibsTests 项目

**项目路径**: `MaterialPlat\FuncLibsTests\`
**目标框架**: .NET 6.0
**测试框架**: NUnit 3.13.2

**主要依赖包**:
- MessagePack 3.1.3 (序列化)
- Moq 4.18.4 (模拟框架)

**测试内容**:
- `FunctionsTest.cs`: 数学函数测试（Abs, Ceiling, Floor, Round等）
- `JsonPerformanceTest.cs`: JSON序列化性能测试
- `MessagePackPerformanceBenchmark.cs`: MessagePack性能基准测试
- `SerializationPerformanceTest.cs`: 序列化性能对比测试
- `SignalVarFlatTests.cs`: 信号变量扁平化测试
- 多个MessagePack优化相关测试文件

**测试特点**:
- 大量性能基准测试
- 专注于序列化/反序列化优化
- 包含数学函数的单元测试
- 使用TestCase特性进行参数化测试

### 5. HardwareConnector.Tests 项目

**项目路径**: `MaterialPlat\HardwareConnector.Tests\`
**目标框架**: .NET 6.0-windows
**测试框架**: NUnit 3.13.3

**主要依赖包**:
- Sylvan.Data.Csv 1.2.4 (CSV处理)
- System.Reactive 5.0.0
- TMCiPro.dll (外部硬件库)

**测试内容**:
- `UnitTest1.cs`: CSV文件读取测试、周期性数据生成测试
- `TestJsonMessage.cs`, `TestMessage.cs`, `TestSocket.cs`: 消息和网络通信测试
- `TestUtils.cs`: 工具类测试

**测试特点**:
- 包含硬件相关的集成测试
- 测试CSV数据处理功能
- 使用响应式编程进行数据流测试

### 6. HwSim.Test 项目

**项目路径**: `MaterialPlat\HardwareSim\HwSim.Test\`
**目标框架**: .NET 6.0
**测试框架**: NUnit 3.13.2

**主要依赖包**:
- NetMQ ********

**测试内容**:
- `UnitTest1.cs`: 硬件模拟器基本功能测试
- `FlatCDataBlockTests.cs`: 扁平化数据块测试
- `MessagePackPerformanceTests.cs`: MessagePack性能测试
- `ZMQPerformanceTests.cs`: ZeroMQ性能测试

**测试特点**:
- 测试硬件模拟器的核心功能
- 包含异步操作测试
- 专注于数据序列化和网络通信性能

### 7. Logger.Tests 项目

**项目路径**: `MaterialPlat\Logger.Tests\`
**目标框架**: .NET 6.0
**测试框架**: NUnit 3.13.2

**测试内容**:
- `UnitTest1.cs`: NLog配置和日志记录测试

**测试特点**:
- 测试日志系统的基本功能
- 验证NLog配置的正确性

### 8. ScriptEngine.Test 项目

**项目路径**: `MaterialPlat\ScriptEngine.Test\`
**目标框架**: .NET 6.0
**测试框架**: NUnit 3.13.3

**主要依赖包**:
- NetMQ ********
- System.Reactive 5.0.0

**测试内容**:
- `BufferTest.cs`: 缓冲区测试
- `GenScriptTest.cs`: 脚本生成测试
- `JsonToCsharpTest.cs`: JSON到C#转换测试
- `PropertiesTest.cs`: 属性测试
- `ResultTest.cs`: 结果处理测试
- `RoslynTest.cs`: Roslyn编译器测试

**测试特点**:
- 测试脚本引擎的核心功能
- 包含动态编译和代码生成测试
- 使用Roslyn进行运行时编译测试

### 9. ScriptEngine.Tests 项目

**项目路径**: `MaterialPlat\ScriptEngine.Tests\`
**目标框架**: .NET 6.0
**测试框架**: xUnit 2.7.0

**主要依赖包**:
- Microsoft.NET.Test.Sdk 17.9.0
- Moq 4.18.2
- coverlet.collector 6.0.1

**测试内容**:
- `BuffersTest.cs`: 缓冲区功能测试，包括环形缓冲区和非重复循环缓冲区
- `DynamicDaqHandlerTest.cs`: 动态数据采集处理器测试
- `InputVars/NumberInputVarTests.cs`: 数字输入变量测试
- `InputVars/UnitDimensionManagerTests.cs`: 单位维度管理器测试
- `MainStreamMultipleAuxiliaryStreamsTest.cs`: 主流多辅助流测试
- `DBHandler/`: 数据库处理器相关测试

**测试特点**:
- 使用xUnit测试框架（与其他项目不同）
- 包含详细的缓冲区算法测试
- 测试覆盖数据流处理和数据库操作
- 使用Fact和Theory特性进行测试标记

### 10. SubTasks.Tests 项目

**项目路径**: `MaterialPlat\SubTasks.Tests\`
**目标框架**: .NET 6.0
**测试框架**: NUnit 3.13.3

**主要依赖包**:
- Autofac 6.5.0 (依赖注入)
- System.Reactive 5.0.0
- coverlet.collector 3.1.2

**测试内容**:
- `FunctionsDynamicTests.cs`: 动态函数测试
- `SubTaskTimerTests.cs`: 子任务定时器测试
- `UtilsTest.cs`: 工具类测试

**测试特点**:
- 使用依赖注入框架Autofac
- 测试子任务系统的核心功能
- 包含定时器和动态函数测试

### 11. SubTaskList.Tests 项目

**项目路径**: `MaterialPlat\SubTaskList.Tests\`
**目标框架**: .NET 6.0
**测试框架**: xUnit 2.4.2

**主要依赖包**:
- Lib.Harmony 2.3.6 (运行时代码修改)
- Moq 4.18.2
- coverlet.collector 3.1.2

**测试内容**:
- `SubTaskCreepSignalCheckTests.cs`: 蠕变信号检查测试
- `Tasks/SubTaskCreepChangeTempParamsTests.cs`: 蠕变温度参数变更测试
- `Tasks/SubTaskCreepCycleSignalGenTests.cs`: 蠕变循环信号生成测试
- `Tasks/SubTaskDAQTests.cs`: 数据采集测试
- `Tasks/SubTaskHighFreqJiaoBianDAQTests.cs`: 高频交变数据采集测试
- `Tasks/SubTaskHighFrequencyControlTests.cs`: 高频控制测试
- `Tasks/SubTaskHightGatherDataTests.cs`: 高频数据收集测试

**测试特点**:
- 使用xUnit测试框架
- 使用Harmony库进行运行时代码修改测试
- 包含复杂的材料测试相关业务逻辑
- 测试涵盖蠕变、高频控制等专业测试场景
- 部分测试使用Skip特性跳过（需要环境配置）

### 12. 其他测试项目

**DbHandlerPerformanceTests**: 数据库处理性能基准测试
**ZMQPerformanceTest**: ZeroMQ性能测试项目

## 测试框架和工具统计

### 测试框架使用情况
- **NUnit**: 10个项目 (83%)
- **xUnit**: 2个项目 (17%)
- **版本分布**:
  - NUnit 3.13.2: 5个项目
  - NUnit 3.13.3: 5个项目
  - xUnit 2.4.2: 1个项目 (SubTaskList.Tests)
  - xUnit 2.7.0: 1个项目 (ScriptEngine.Tests)

### 常用测试工具和库
- **Moq**: 1个项目 (模拟框架)
- **NetMQ**: 4个项目 (消息队列测试)
- **System.Reactive**: 3个项目 (响应式编程)
- **MessagePack**: 2个项目 (序列化性能测试)
- **Dapper**: 1个项目 (ORM测试)
- **SQLite**: 2个项目 (数据库测试)

### 性能测试覆盖
- **序列化性能**: FuncLibsTests, HwSim.Test
- **数据库性能**: DBUtils.Test, DbHandlerPerformanceTests
- **网络通信性能**: HwSim.Test, HardwareConnector.Tests
- **JSON处理性能**: FuncLibs.Tests, FuncLibsTests

## 问题和改进建议

### 发现的问题

1. **项目命名不一致**
   - 存在`DBUtils.Test`和`DBUtils.Tests`两个类似项目
   - 建议统一使用`.Tests`后缀

2. **测试框架混合使用**
   - 项目中同时使用NUnit和xUnit两种测试框架
   - NUnit: 10个项目 (83%)
   - xUnit: 2个项目 (17%)
   - 建议统一使用一种测试框架以降低维护成本

3. **测试框架版本不统一**
   - NUnit版本在3.13.2和3.13.3之间不一致
   - xUnit版本在2.4.2和2.7.0之间不一致
   - 建议统一升级到最新稳定版本

4. **部分测试被忽略**
   - 一些测试标记为`[Ignore]`(NUnit)或`[Skip]`(xUnit)，原因是需要外部依赖
   - 建议使用模拟对象或测试容器解决

5. **缺少代码覆盖率工具**
   - 虽然引用了`coverlet.collector`，但未见覆盖率报告
   - 建议配置CI/CD中的覆盖率收集

6. **测试数据管理**
   - 一些测试硬编码了文件路径
   - 建议使用相对路径或配置文件

7. **依赖包版本不一致**
   - Microsoft.NET.Test.Sdk版本从17.1.0到17.9.0不等
   - Moq版本在4.18.2和4.18.4之间不一致
   - coverlet.collector版本从3.1.2到6.0.1不等

### 改进建议

#### 1. 统一测试框架选择
**建议选择NUnit作为统一测试框架**，原因：
- 83%的项目已经使用NUnit
- NUnit在.NET生态系统中更成熟稳定
- 支持更丰富的断言和测试特性
- 迁移成本相对较低

**xUnit迁移到NUnit的对照表**：
```csharp
// xUnit -> NUnit
[Fact] -> [Test]
[Theory] -> [TestCase] 或 [TestCaseSource]
[Skip] -> [Ignore]
Assert.True() -> Assert.IsTrue()
Assert.Equal() -> Assert.AreEqual()
```

#### 2. 统一测试项目结构
```
建议的项目命名规范:
- [ProjectName].Tests (单元测试)
- [ProjectName].IntegrationTests (集成测试，如需要)
- [ProjectName].PerformanceTests (性能测试，如需要)
```

#### 3. 升级和统一依赖
```xml
推荐的测试包版本:
- Microsoft.NET.Test.Sdk: 17.9.0
- NUnit: 4.0.1
- NUnit3TestAdapter: 4.5.0
- coverlet.collector: 6.0.1
- Moq: 4.20.69
- System.Reactive: 6.0.0
```

#### 4. 建立测试分类体系
```csharp
[Category("Unit")]        // 单元测试
[Category("Integration")] // 集成测试
[Category("Performance")] // 性能测试
[Category("E2E")]         // 端到端测试
```

#### 5. 配置测试运行器
创建`runsettings`文件统一配置测试运行参数：
```xml
<?xml version="1.0" encoding="utf-8"?>
<RunSettings>
  <DataCollectionRunSettings>
    <DataCollectors>
      <DataCollector friendlyName="Code Coverage" uri="datacollector://Microsoft/CodeCoverage/2.0" assemblyQualifiedName="Microsoft.VisualStudio.Coverage.DynamicCoverageDataCollector, Microsoft.VisualStudio.TraceCollector, Version=11.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
        <Configuration>
          <CodeCoverage>
            <ModulePaths>
              <Exclude>
                <ModulePath>.*Tests.dll</ModulePath>
              </Exclude>
            </ModulePaths>
          </CodeCoverage>
        </Configuration>
      </DataCollector>
    </DataCollectors>
  </DataCollectionRunSettings>
</RunSettings>
```

#### 6. 建立CI/CD集成
```yaml
# 建议的测试流水线步骤
- 编译所有测试项目
- 运行单元测试
- 运行集成测试
- 生成覆盖率报告
- 运行性能基准测试
- 发布测试报告
```

## 项目整合调整计划

### 项目合并策略

#### 1. DBUtils测试项目合并
**目标**：将`DBUtils.Test`合并到`DBUtils.Tests`
- **保留项目**：`DBUtils.Tests` (作为合并目标)
- **合并内容**：
  - 将`ExcelChartTest.cs`从DBUtils.Test迁移到DBUtils.Tests
  - 将`SqliteConnectionTest.cs`从DBUtils.Test迁移到DBUtils.Tests
  - 统一依赖包版本到NUnit 3.13.3
- **删除项目**：`DBUtils.Test`

#### 2. FuncLibs测试项目合并
**目标**：将`FuncLibsTests`重命名为`FuncLibs.Tests`并合并内容
- **保留项目**：`FuncLibs.Tests` (重命名后的项目)
- **合并内容**：
  - 将`FuncLibsTests`重命名为`FuncLibs.Tests`以符合命名规范
  - 将`JsonPerformanceTest.cs`从原FuncLibs.Tests迁移过来
  - 避免重复的性能测试代码
- **删除项目**：原`FuncLibs.Tests`

#### 3. ScriptEngine测试项目合并
**目标**：将`ScriptEngine.Test`合并到`ScriptEngine.Tests`
- **保留项目**：`ScriptEngine.Tests` (作为合并目标，使用xUnit)
- **合并内容**：
  - 将NUnit测试转换为xUnit格式
  - 迁移所有测试文件到ScriptEngine.Tests
  - 统一使用xUnit 2.7.0测试框架
- **删除项目**：`ScriptEngine.Test`

### 可忽略项目说明

以下项目在整合过程中可以暂时忽略，不纳入主要的单元测试体系：

1. **HardwareConnector.Tests**：硬件相关测试，依赖外部硬件环境
2. **HwSim.Test**：硬件模拟器测试，属于集成测试范畴
3. **DbHandlerPerformanceTests**：性能基准测试，独立运行

### 整合后的项目结构

整合完成后，核心单元测试项目将简化为：

| 序号 | 项目名称 | 目标项目 | 测试框架 | 状态 | 备注 |
|------|----------|----------|----------|------|------|
| 1 | DBUtils.Tests | DBUtils | NUnit 4.0.1 | 活跃 | 合并后的数据库测试 |
| 2 | FuncLibs.Tests | FuncLibs | NUnit 4.0.1 | 活跃 | 合并后的函数库测试 |
| 3 | Logger.Tests | Logger | NUnit 4.0.1 | 活跃 | 日志系统测试 |
| 4 | ScriptEngine.Tests | ScriptEngine | NUnit 4.0.1 | 活跃 | 合并后的脚本引擎测试(从xUnit迁移) |
| 5 | SubTasks.Tests | SubTasks | NUnit 4.0.1 | 活跃 | 子任务测试 |
| 6 | SubTaskList.Tests | SubTaskList | NUnit 4.0.1 | 活跃 | 子任务列表测试(从xUnit迁移) |

## 下一步行动计划

### 短期目标（1-2周）
1. **项目合并和命名规范**：按照上述策略完成三组项目的合并
   - DBUtils.Test -> DBUtils.Tests
   - FuncLibsTests -> FuncLibs.Tests (重命名并合并)
   - ScriptEngine.Test -> ScriptEngine.Tests
2. **测试框架统一**：将所有项目统一到NUnit 4.0.1
   - 将ScriptEngine.Tests从xUnit迁移到NUnit
   - 将SubTaskList.Tests从xUnit迁移到NUnit
3. **版本统一**：升级所有保留项目的依赖包版本到推荐版本
   - Microsoft.NET.Test.Sdk: 17.9.0
   - NUnit: 4.0.1
   - NUnit3TestAdapter: 4.5.0
4. **验证测试**：确保合并后的测试项目能正常运行

### 中期目标（1个月）
1. 建立测试分类体系
2. 增加缺失的单元测试
3. 优化性能测试的基准设置
4. 建立测试数据管理规范

### 长期目标（2-3个月）
1. 集成到CI/CD流水线
2. 建立测试质量门禁
3. 定期性能回归测试
4. 建立测试文档和最佳实践

## 总结

MaterialPlat项目已经具备了较为完善的单元测试基础，共有12个测试项目覆盖了主要的业务模块。测试框架统一使用NUnit，并且包含了丰富的性能测试。主要需要改进的是项目命名规范、版本统一和测试分类管理。通过系统性的整合改造，可以进一步提升测试质量和开发效率。