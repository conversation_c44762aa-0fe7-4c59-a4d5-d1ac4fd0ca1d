25-09-03 09:10:56:843 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/host/instance 
 - 参数: 
 {:Hosts [{:HostId "2d06ec3a-9ecd-422c-a76a-1afc16b47b24", :HostName "1", :ConfigId nil, :StationHardware nil} {:HostId "da28d67a-f0fc-46a6-ab37-d412e6ab9f26", :HostName "2", :ConfigId nil, :StationHardware nil}]} 
 =============================================================

25-09-03 09:10:57:167 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/hardware 
 - 参数: 
 {:HardwareDtos {"hardware_simulator-0000-hw-ccss-id-0000" {:DaChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :AdChannels [{:Sensors ["传感器0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0}], :CreepAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 10, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 11, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 12, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 13, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 14, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 15, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 16, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 17, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 18, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 19, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}], :HandBoxChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil}], :Devices ({:Hwkey "hardware_simulator", :SubId 0, :DeviceId "9cdeaaab-9005-4fb9-b11a-279864d5c0cd"}), :ServoAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 7, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0}], :UpDirectValue 5, :TensileUpOrDown 7, :PressUpOrDown 6} {:Hwkey "hardware_simulator", :RealIndex 3, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0}], :UpDirectValue 5, :TensileUpOrDown 5, :PressUpOrDown 5}], :InputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :OutputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :TempAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 10, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}]}}} 
 =============================================================

25-09-03 09:10:57:310 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/api/appserver/config/info 
 - 参数: 
 {:AppServerIp "*************", :AppServerPort 3000, :ComputerId "MP2QAVV0", :AppServerApiPort 3000} 
 =============================================================

25-09-03 09:10:57:427 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 登录"}
25-09-03 09:10:58:116 DESKTOP-3BSREDP INFO [clj-backend.modules.standby.service:160] - 运行 sync-to-share-http (HTTP POST)...
25-09-03 09:10:58:121 DESKTOP-3BSREDP WARN [clj-backend.modules.standby.service:170] - 从节点 IP 或端口未配置. 跳过 sync-to-share-http. 
25-09-03 09:11:05:028 DESKTOP-3BSREDP WARN [clj-backend.db.connections:40] - 已为数据库连接 project_27 应用性能优化配置
25-09-03 09:11:05:029 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - 创建 project_27 项目库连接
25-09-03 09:11:05:303 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "打开项目 project_27"}
25-09-03 09:11:05:341 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:861] - 模板开始实例化
25-09-03 09:11:09:822 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "62795a17-93cc-4736-b188-23ffa1b927ee", :code 0, :msg "模板生成成功"}
25-09-03 09:11:09:824 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-62795a17-93cc-4736-b188-23ffa1b927ee 中添加消息 {:ProcessId "62795a17-93cc-4736-b188-23ffa1b927ee", :code 0, :msg "模板生成成功"}
25-09-03 09:11:09:828 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "62795a17-93cc-4736-b188-23ffa1b927ee", :code 0, :msg "模板生成成功"}
25-09-03 09:11:11:122 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/sample-inst/select 
 - 参数: 
 {:ClassName "project_27", :CurrentInstCode "sample_14785d372", :SelectedInstCodes []} 
 =============================================================

25-09-03 09:11:18:762 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【打开项目默认执行动作】"}
25-09-03 09:11:18:775 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "running"} 
 =============================================================

25-09-03 09:11:20:013 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【打开项目默认执行动作】"}
25-09-03 09:11:20:015 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "finished"} 
 =============================================================

25-09-03 09:17:22:451 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【裂纹检查新版流程图】"}
25-09-03 09:17:22:458 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "running"} 
 =============================================================

25-09-03 09:17:22:689 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_27", :ScriptId "73f7de68-e31e-4aea-9ff5-578f4a8bfe5a", :Result false}
25-09-03 09:17:22:690 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-73f7de68-e31e-4aea-9ff5-578f4a8bfe5a 中添加消息 {:ProcessId "project_27", :ScriptId "73f7de68-e31e-4aea-9ff5-578f4a8bfe5a", :Result false}
25-09-03 09:17:22:691 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_27", :ScriptId "73f7de68-e31e-4aea-9ff5-578f4a8bfe5a", :Result false}
25-09-03 09:17:22:720 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_27", :ScriptId "722d6276-38e4-42ba-96f5-e33fda25a871", :Result false}
25-09-03 09:17:22:720 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-722d6276-38e4-42ba-96f5-e33fda25a871 中添加消息 {:ProcessId "project_27", :ScriptId "722d6276-38e4-42ba-96f5-e33fda25a871", :Result false}
25-09-03 09:17:22:721 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_27", :ScriptId "722d6276-38e4-42ba-96f5-e33fda25a871", :Result false}
25-09-03 09:17:26:132 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【联机】"}
25-09-03 09:17:26:134 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "running"} 
 =============================================================

25-09-03 09:17:28:382 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【启动】"}
25-09-03 09:17:28:385 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-679f70fa-d870-40d9-b121-c759b28044ed", :State "running"} 
 =============================================================

25-09-03 09:17:30:204 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_27", :ScriptId "8cb9aa23-8453-4c9d-ae6e-a5b86d3ee2df", :Result false}
25-09-03 09:17:30:205 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-8cb9aa23-8453-4c9d-ae6e-a5b86d3ee2df 中添加消息 {:ProcessId "project_27", :ScriptId "8cb9aa23-8453-4c9d-ae6e-a5b86d3ee2df", :Result false}
25-09-03 09:17:30:207 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_27", :ScriptId "8cb9aa23-8453-4c9d-ae6e-a5b86d3ee2df", :Result false}
25-09-03 09:17:33:787 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【启动】"}
25-09-03 09:17:33:790 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-679f70fa-d870-40d9-b121-c759b28044ed", :State "finished"} 
 =============================================================

25-09-03 09:17:37:851 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【终止裂纹长度检查】"}
25-09-03 09:17:37:854 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :State "running"} 
 =============================================================

25-09-03 09:17:37:935 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_27", :ProcessID "project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :SubTaskID "onlyAction-0fd5b761-7c8e-432f-b8e5-5b70cd023b0d", :MsgBody {:Cmd "abort", :InstCode "sample_14785d372", :ActionID "d2a28ac5-6be5-4a4c-806f-424fc39131ee"}}
25-09-03 09:17:37:943 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【裂纹检查新版流程图】"}
25-09-03 09:17:37:945 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "finished"} 
 =============================================================

25-09-03 09:17:38:002 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_27", :ProcessID "project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :SubTaskID "onlyAction-7ab30145-6f3a-4ac1-a4c1-6d9ff9ba49cf", :MsgBody {:Cmd "start", :InstCode "sample_14785d372", :ActionID "da399466-da97-449d-b998-0d7a0823cdd0"}}
25-09-03 09:17:38:008 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【横梁停止】"}
25-09-03 09:17:38:010 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-da399466-da97-449d-b998-0d7a0823cdd0", :State "running"} 
 =============================================================

25-09-03 09:17:38:145 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_27", :ScriptId "0866a284-1bdc-47a7-8c61-16f140f5237d", :Result true}
25-09-03 09:17:38:146 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-0866a284-1bdc-47a7-8c61-16f140f5237d 中添加消息 {:ProcessId "project_27", :ScriptId "0866a284-1bdc-47a7-8c61-16f140f5237d", :Result true}
25-09-03 09:17:38:148 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_27", :ScriptId "0866a284-1bdc-47a7-8c61-16f140f5237d", :Result true}
25-09-03 09:18:38:974 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【裂纹检查新版流程图】"}
25-09-03 09:18:38:979 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "running"} 
 =============================================================

25-09-03 09:18:39:079 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_27", :ScriptId "df594848-6331-4a6d-8866-9671aaa8ccee", :Result false}
25-09-03 09:18:39:080 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-df594848-6331-4a6d-8866-9671aaa8ccee 中添加消息 {:ProcessId "project_27", :ScriptId "df594848-6331-4a6d-8866-9671aaa8ccee", :Result false}
25-09-03 09:18:39:081 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_27", :ScriptId "df594848-6331-4a6d-8866-9671aaa8ccee", :Result false}
25-09-03 09:18:41:325 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_27", :ScriptId "bb321806-4edd-4bf4-90c1-e354e537f636", :Result true}
25-09-03 09:18:41:328 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-bb321806-4edd-4bf4-90c1-e354e537f636 中添加消息 {:ProcessId "project_27", :ScriptId "bb321806-4edd-4bf4-90c1-e354e537f636", :Result true}
25-09-03 09:18:41:330 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_27", :ScriptId "bb321806-4edd-4bf4-90c1-e354e537f636", :Result true}
25-09-03 09:18:42:252 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【启动】"}
25-09-03 09:18:42:254 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-679f70fa-d870-40d9-b121-c759b28044ed", :State "running"} 
 =============================================================

25-09-03 09:18:42:556 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【启动】"}
25-09-03 09:18:42:560 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-679f70fa-d870-40d9-b121-c759b28044ed", :State "finished"} 
 =============================================================

25-09-03 09:18:44:010 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:level "error", :content "流程正在执行中(状态异常)"}
25-09-03 09:18:44:013 DESKTOP-3BSREDP ERROR [clj-backend.common.biz-error:55] - 发生系统错误. 
错误码: 1502 
错误描述: 流程正在执行中(状态异常) 
错误数据: #clj_scheduler.context.multi-task-mgr{:process-id "project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :context [{:template-info {:project_id 27}, :name "开始", :params nil, :action-name "裂纹检查新版流程图", :task-type :start, :sample-code nil, :status :ready, :id "start-node-action", :class-name "project_27", :process-id "project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee"} {:name "条件-5.9", :params nil, :task-type :if-else, :expr "int flag = 0;\r\n\r\nint deviceId = 0;\r\nint ret = Model.station.Ccss_Connected(deviceId);\r\nif (ret == 0) {\r\n  /* 已连接 */ \r\n  flag++;\r\n} else { \r\n  /* 未连接 */ \r\n  Model.GetVarByName<TextInputVar>(\"input_yzlwcsts\").Value = \"控制器连接失败，请重新连接控制器\";\r\n  return false;\r\n}\r\nint ret1 = Model.station.Ccss_DriveReady(deviceId);\r\nif (ret1 == 0) { \r\n  /* 准备就绪 */ \r\n  flag++;\r\n} else {\r\n  /* 未就绪 */ \r\n  Model.GetVarByName<TextInputVar>(\"input_yzlwcsts\").Value = \"控制器未在启动状态\";\r\n  return false;\r\n}\r\nreturn true;", :true-tasks [{:name "脚本执行子任务", :params {:schedule {:control_input_script {:valueType "string", :variable "control_input_script", :isCheck false, :unit "", :value "string ydfs = \"负荷\";\r\nif(Model.GetVarByName<SelectInputVar>(\"input_LWCDJC_YDFS\").Value[2] == 0){\r\n  ydfs = \"位移\";\r\n}\r\nif(Model.GetVarByName<SelectInputVar>(\"input_LWCDJC_YDFS\").Value[2] == 1){\r\n  ydfs = \"负荷\";\r\n}\r\nif(Model.GetVarByName<SelectInputVar>(\"input_LWCDJC_YDFS\").Value[2] == 2){\r\n  ydfs = \"变形\";\r\n}\r\n\r\nstring significance = \"高\";\r\nstring type = \"信息\";\r\nstring grade = \"信息\";\r\nstring content = \"平面应变断裂韧度试验 裂纹长度检查操作试验运行 \"\r\n                 + Model.CurrentInst.Name\r\n                 + \",作动器控制 作动器运动开始 \"\r\n                 + \",运动方式：\"\r\n                 + ydfs\r\n                 + \",运动速度：\"\r\n                 + Model.GetVarByName<NumberInputVar>(\"input_LWCDJC_SD\").DisplayValue\r\n                 + Model.GetVarByName<NumberInputVar>(\"input_LWCDJC_SD\").DisplayUnitName   //单位\r\n                 + \",目标值:\"\r\n                 + Model.GetVarByName<NumberInputVar>(\"input_LWCDJC_MBZ\").DisplayValue\r\n                 + Model.GetVarByName<NumberInputVar>(\"input_LWCDJC_SD\").DisplayUnitName   //单位\r\n                 ;\r\n\r\nvar r = Model.RecordLog(significance, type, grade, content);\r\n\r\nConsole.WriteLine(\"记录控件日志是否成功：\" + r);\r\n\r\nreturn false;", :mode "无", :type "常量", :unitTypeCode "", :varType "Text", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "脚本执行子任务709fdee762"}, :nameShow "脚本执行子任务", :task-type :SubTaskEvalScript, :status :ready, :id "SubTaskEvalScript-cc94c47e-def9-4413-a314-5379c15e2989"}], :status :ready, :id "if-else-0c336739-59ca-414f-b5af-804dc945aeee", :false-tasks [{:name "对话框子任务", :params {:schedule {:control_input_ButtonType {:valueType "string", :variable "control_input_ButtonType", :isCheck false, :unit "", :value "OK", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_Script {:valueType "string", :variable "control_input_Script", :isCheck false, :unit "", :value "return Model.GetVarByName<TextInputVar>(\"input_yzlwcsts\").Value;", :mode "无", :type "常量", :unitTypeCode "", :varType "Text", :unitCode "", :unitType ""}, :control_input_BindVarCode {:valueType "string", :variable "control_input_BindVarCode", :isCheck false, :unit "", :value "input_one", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "对话框子任务b2f3569311"}, :nameShow "对话框子任务", :task-type :SubtaskDialogBox, :status :ready, :id "SubtaskDialogBox-c57a9ae0-38a2-40a2-a47c-a56ea9f9d378"}]} {:name "脚本执行子任务", :params {:schedule {:control_input_script {:valueType "string", :variable "control_input_script", :isCheck false, :unit "", :value "\n// 全局变量赋值，记录推送给二维数组集合曲线的上一次推送的index\nModel.iVariable[\"lastTail1\"]=0;\nModel.iVariable[\"lastTail2\"]=0;\nModel.iVariable[\"lastTail3\"]=0;\nModel.SignalVars[\"signal_time\"].ResetZero(Model);\n\n\n// 实时数据二维数组集合Model.GetVarByName<DoubleArrayListInputVar>(\"input_lwkz_curve_doubleArrayList\")\nvar doubleArrayList = Model.GetVarByName<DoubleArrayListInputVar>(\"input_lwkz_curve_doubleArrayList\");\n\n\n// 清空后端数据\ndoubleArrayList.ClearDoubleArrayListData();\n// 清空ui数据\ndoubleArrayList.SendClearDataCmdToUI();\n\n\n// 裂纹长度检查结果二维数组初始化\nvar LWCDJCJG_INPUT = Model.GetVarByName<DoubleArrayInputVar>(\"input_liewenchangdujieguo_doubleArray\");\n// 清空数据\nLWCDJCJG_INPUT.ClearDoubleArrayData();\n// 清空ui数据\nLWCDJCJG_INPUT.SendClearDataCmdToUI();\n\n// 初始化一些信息，方便后面的一些公式调用，这些参数先写死\nbyte sampleType = 0; \nint res1 = 0;\nif (Model.CurrentInst.SampleType == \"sampleType_jincou\"){\n   sampleType = 0; \n   res1 =\n      TestExpert.PlaneStrainFractureToughnessMethod.Commons\n\t.PlaneStrainFractureToughnessMethod.InitializeSpecimen(\n          Model.CurrentInst.Name,\n          sampleType,\n          (double)Model.CurrentInst.Parameters[\"sampleParam_width\"].Value,\n          (double)Model.CurrentInst.Parameters[\"sampleParam_thickness\"].Value,\n          (double)Model.CurrentInst.Parameters[\"sampleParam_notchLength\"].Value,\n          (double)Model.CurrentInst.Parameters[\"sampleParam_elasticModulus\"].Value,\n          (double)Model.CurrentInst.Parameters[\"sampleParam_yieldStrength\"].Value,\n          0\n      );\n}\nif (Model.CurrentInst.SampleType == \"sampleType_danbian\"){\n   sampleType = 1; \n   res1 =\n      TestExpert.PlaneStrainFractureToughnessMethod.Commons\n\t.PlaneStrainFractureToughnessMethod.InitializeSpecimen(\n          Model.CurrentInst.Name,\n          sampleType,\n          (double)Model.CurrentInst.Parameters[\"sampleParam_width\"].Value,\n          (double)Model.CurrentInst.Parameters[\"sampleParam_thickness\"].Value,\n          (double)Model.CurrentInst.Parameters[\"sampleParam_notchLength\"].Value,\n          (double)Model.CurrentInst.Parameters[\"sampleParam_elasticModulus\"].Value,\n          (double)Model.CurrentInst.Parameters[\"sampleParam_yieldStrength\"].Value,\n          (double)Model.CurrentInst.Parameters[\"sampleParam_span\"].Value\n      );\n}\n\nif(Model.CurrentInst.SampleType == \"sampleType_danbian\"){\n  var coefficientResult = TestExpert.PlaneStrainFractureToughnessMethod.Commons\n  .PlaneStrainFractureToughnessMethod.SetCoefficient(0.99975, -3.9504, 2.9821, -3.2141, 51.516, -113.03);\n}\nif(Model.CurrentInst.SampleType == \"sampleType_jincou\"){\n  if(Model.CurrentInst.Parameters[\"sampleParam_measurementPosition\"].Value == \"-0.345\"){ //vx1\n    var coefficientResult = TestExpert.PlaneStrainFractureToughnessMethod.Commons\n  \t.PlaneStrainFractureToughnessMethod.SetCoefficient(1.0012,-4.9165,23.057,-323.91, 1798.3, -3513.2);\n  }\n  if(Model.CurrentInst.Parameters[\"sampleParam_measurementPosition\"].Value == \"-0.25\"){ //v0\n    var coefficientResult = TestExpert.PlaneStrainFractureToughnessMethod.Commons\n  \t.PlaneStrainFractureToughnessMethod.SetCoefficient(1.001,-4.6695,18.46,-236.82,1214.9,-2143.6);\n  }\n  if(Model.CurrentInst.Parameters[\"sampleParam_measurementPosition\"].Value == \"-0.1576\"){ //v1\n    var coefficientResult = TestExpert.PlaneStrainFractureToughnessMethod.Commons\n  \t.PlaneStrainFractureToughnessMethod.SetCoefficient(1.0008,-4.4473,15.4,-180.55,870.92,-1411.3);\n  }\n  if(Model.CurrentInst.Parameters[\"sampleParam_measurementPosition\"].Value == \"0\"){ //vll\n    var coefficientResult = TestExpert.PlaneStrainFractureToughnessMethod.Commons\n  \t.PlaneStrainFractureToughnessMethod.SetCoefficient(1.0002,-4.0632,11.242,-106.04,464.33,-650.68);\n  }\n\n}\n\n\nModel.dVariable[\"lwjc_all_index\"] = -1.0; \n\nModel.dVariable[\"aaa\"] = 0.0; \n\nvar ss = Model.GetVarByName<DoubleArrayInputVar>(\"input_lwcdjc_ss\");\nss.UpdateSendMode(0);\nss.ClearDoubleArrayData();\nss.SendClearDataCmdToUI();\nss.SendValueToUI(Model.TemplateName);\n\nModel.PubtaskStatus=true;\n\nreturn false;", :mode "无", :type "常量", :unitTypeCode "", :varType "Text", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "脚本执行子任务4b2834d246"}, :nameShow "脚本执行子任务", :task-type :SubTaskEvalScript, :status :ready, :id "SubTaskEvalScript-d38715eb-7301-437d-b693-3e546d3997f9"} {:branch-idxs [0], :name "并行-1.3", :params nil, :branches [[{:name "组合波开始", :params {:schedule {:control_input_deviceid {:valueType "object[]", :variable "control_input_deviceid", :isCheck false, :unit "", :value ["Servo" 0], :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_cycle {:valueType "string", :variable "control_input_cycle", :isCheck false, :unit "", :value 1, :mode "无", :type "常量", :unitTypeCode "", :varType "Number", :unitCode "", :unitType nil}}, :edition "standard", :daq_code "组合波开始19e0b17d88"}, :nameShow "组合波开始", :task-type :combinedWaveStartControl, :status :ready, :id "combinedWaveStartControl-5da849dd-b2c1-47db-8def-f3243aae6634"} {:name "斜波子任务", :params {:schedule {:control_input_targetValue {:valueType "", :variable "control_input_targetValue", :isCheck false, :unit "", :value "input_LWCDJC_MBZ", :mode "无", :type "变量", :unitTypeCode "", :varType "Number", :unitCode "", :unitType ""}, :control_input_waitpos {:valueType "string", :variable "control_input_waitpos", :isCheck false, :unit "", :value false, :mode "无", :type "常量", :unitTypeCode "", :varType "Boolean", :unitCode "", :unitType ""}, :control_input_errorCode {:valueType "string", :variable "control_input_errorCode", :isCheck false, :unit "", :value "input_isXieBo", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_controlMode {:valueType "object[]", :variable "control_input_controlMode", :isCheck false, :unit "", :value ["Servo" 0 1], :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_speed {:valueType "", :variable "control_input_speed", :isCheck false, :unit "", :value "input_LWCDJC_SD", :mode "无", :type "变量", :unitTypeCode "", :varType "Number", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "斜波子任务e6ede7931f"}, :nameShow "斜波子任务", :task-type :xiebo, :status :ready, :id "xiebo-7a9b46e2-ce29-4ac0-a55d-be7b124e697d"} {:name "斜波子任务", :params {:schedule {:control_input_targetValue {:valueType "", :variable "control_input_targetValue", :isCheck false, :unit "", :value "input_LWCUJC_QSZ", :mode "无", :type "变量", :unitTypeCode "", :varType "Number", :unitCode "", :unitType ""}, :control_input_waitpos {:valueType "string", :variable "control_input_waitpos", :isCheck false, :unit "", :value false, :mode "无", :type "常量", :unitTypeCode "", :varType "Boolean", :unitCode "", :unitType ""}, :control_input_errorCode {:valueType "string", :variable "control_input_errorCode", :isCheck false, :unit "", :value "input_isXieBo", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_controlMode {:valueType "object[]", :variable "control_input_controlMode", :isCheck false, :unit "", :value ["Servo" 0 1], :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_speed {:valueType "", :variable "control_input_speed", :isCheck false, :unit "", :value "input_LWCDJC_SD", :mode "无", :type "变量", :unitTypeCode "", :varType "Number", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "斜波子任务e146d2e9e8"}, :nameShow "斜波子任务", :task-type :xiebo, :status :ready, :id "xiebo-ec32daaf-a89d-4394-9719-b18e311efb7a"} {:name "斜波子任务", :params {:schedule {:control_input_targetValue {:valueType "", :variable "control_input_targetValue", :isCheck false, :unit "", :value "input_LWCDJC_MBZ", :mode "无", :type "变量", :unitTypeCode "", :varType "Number", :unitCode "", :unitType ""}, :control_input_waitpos {:valueType "string", :variable "control_input_waitpos", :isCheck false, :unit "", :value false, :mode "无", :type "常量", :unitTypeCode "", :varType "Boolean", :unitCode "", :unitType ""}, :control_input_errorCode {:valueType "string", :variable "control_input_errorCode", :isCheck false, :unit "", :value "input_isXieBo", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_controlMode {:valueType "object[]", :variable "control_input_controlMode", :isCheck false, :unit "", :value ["Servo" 0 1], :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_speed {:valueType "", :variable "control_input_speed", :isCheck false, :unit "", :value "input_LWCDJC_SD", :mode "无", :type "变量", :unitTypeCode "", :varType "Number", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "斜波子任务aa8a83b7fd"}, :nameShow "斜波子任务", :task-type :xiebo, :status :ready, :id "xiebo-c39d0131-3fdb-46d8-af94-0668968e4756"} {:name "斜波子任务", :params {:schedule {:control_input_targetValue {:valueType "", :variable "control_input_targetValue", :isCheck false, :unit "", :value "input_LWCUJC_QSZ", :mode "无", :type "变量", :unitTypeCode "", :varType "Number", :unitCode "", :unitType ""}, :control_input_waitpos {:valueType "string", :variable "control_input_waitpos", :isCheck false, :unit "", :value false, :mode "无", :type "常量", :unitTypeCode "", :varType "Boolean", :unitCode "", :unitType ""}, :control_input_errorCode {:valueType "string", :variable "control_input_errorCode", :isCheck false, :unit "", :value "input_isXieBo", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_controlMode {:valueType "object[]", :variable "control_input_controlMode", :isCheck false, :unit "", :value ["Servo" 0 1], :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_speed {:valueType "", :variable "control_input_speed", :isCheck false, :unit "", :value "input_LWCDJC_SD", :mode "无", :type "变量", :unitTypeCode "", :varType "Number", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "斜波子任务8517da3675"}, :nameShow "斜波子任务", :task-type :xiebo, :status :ready, :id "xiebo-b7edfcb1-c345-463c-aacd-c8d2eb206635"} {:name "斜波子任务", :params {:schedule {:control_input_targetValue {:valueType "", :variable "control_input_targetValue", :isCheck false, :unit "", :value "input_LWCDJC_MBZ", :mode "无", :type "变量", :unitTypeCode "", :varType "Number", :unitCode "", :unitType ""}, :control_input_waitpos {:valueType "string", :variable "control_input_waitpos", :isCheck false, :unit "", :value false, :mode "无", :type "常量", :unitTypeCode "", :varType "Boolean", :unitCode "", :unitType ""}, :control_input_errorCode {:valueType "string", :variable "control_input_errorCode", :isCheck false, :unit "", :value "input_isXieBo", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_controlMode {:valueType "object[]", :variable "control_input_controlMode", :isCheck false, :unit "", :value ["Servo" 0 1], :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_speed {:valueType "", :variable "control_input_speed", :isCheck false, :unit "", :value "input_LWCDJC_SD", :mode "无", :type "变量", :unitTypeCode "", :varType "Number", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "斜波子任务541540d663"}, :nameShow "斜波子任务", :task-type :xiebo, :status :ready, :id "xiebo-333a9f87-cae8-4b89-ab58-dd716d819b58"} {:name "斜波子任务", :params {:schedule {:control_input_targetValue {:valueType "", :variable "control_input_targetValue", :isCheck false, :unit "", :value "input_LWCUJC_QSZ", :mode "无", :type "变量", :unitTypeCode "", :varType "Number", :unitCode "", :unitType ""}, :control_input_waitpos {:valueType "string", :variable "control_input_waitpos", :isCheck false, :unit "", :value false, :mode "无", :type "常量", :unitTypeCode "", :varType "Boolean", :unitCode "", :unitType ""}, :control_input_errorCode {:valueType "string", :variable "control_input_errorCode", :isCheck false, :unit "", :value "input_isXieBo", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_controlMode {:valueType "object[]", :variable "control_input_controlMode", :isCheck false, :unit "", :value ["Servo" 0 1], :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_speed {:valueType "", :variable "control_input_speed", :isCheck false, :unit "", :value "input_LWCDJC_SD", :mode "无", :type "变量", :unitTypeCode "", :varType "Number", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "斜波子任务c4f736223b"}, :nameShow "斜波子任务", :task-type :xiebo, :status :ready, :id "xiebo-3756a5ec-49f2-4c21-a287-b92e938f5c45"} {:branch-idxs [0], :name "并行-8.7", :params nil, :branches [[{:name "组合波结束", :params {:schedule {:control_input_deviceid {:valueType "object[]", :variable "control_input_deviceid", :isCheck false, :unit "", :value ["Servo" 0], :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "组合波结束ca84fca62a"}, :nameShow "组合波结束", :task-type :combinedWaveEndControl, :status :ready, :id "combinedWaveEndControl-07f9477f-ddab-4730-ac75-81ca40937ab5"} {:name "等待某个事件", :params {:schedule {:control_input_interval {:valueType "string", :variable "control_input_interval", :isCheck false, :unit "", :value "50", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_script {:valueType "", :variable "control_input_script", :isCheck false, :unit "", :value "var doubleArray = Model.GetVarByName<DoubleArrayInputVar>(\"input_lwcdjc_ss\");\nint index = doubleArray.GetDoubleArrayRowCount()-1;\n//当二维数组记录的负荷与起始值的差的绝对值<0.01时结束记录\nint CtrlMode = (int)Model.GetVarByName<SelectInputVar>(\"input_LWCDJC_YDFS\").Value[2];\ndouble offset = 0;\nif(CtrlMode == 1){\n  offset = Math.Abs((double)doubleArray.GetCellValue(index,\"load\") - Model.GetVarByName<NumberInputVar>(\"input_LWCUJC_QSZ\").Value);\n}else if(CtrlMode == 0){\n  offset = Math.Abs((double)doubleArray.GetCellValue(index,\"pos\") - Model.GetVarByName<NumberInputVar>(\"input_LWCUJC_QSZ\").Value);\n}else if(CtrlMode == 2){\n  offset = Math.Abs((double)doubleArray.GetCellValue(index,\"ext\") - Model.GetVarByName<NumberInputVar>(\"input_LWCUJC_QSZ\").Value);\n}\nif (offset < 0.01){\n  return true;\n}\n return false;", :mode "无", :type "常量", :unitTypeCode "", :varType "Text", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "等待某个事件2c19cb9002"}, :nameShow "等待某个事件", :task-type :waitEvent, :status :ready, :id "waitEvent-00671a03-a007-4507-85a2-e7ad8bcd2c4c"}] [{:name "DAQ", :params {:schedule {:control_input_move_interval {:valueType "string", :variable "control_input_move_interval", :isCheck false, :unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :value 0, :mode "signal_load", :type "常量", :unitTypeCode "dimension_Force", :varType "Number", :unitCode "unit_cdw13c2640", :axisType "signal_load", :unitType "56019f36-d734-40c0-8872-73b60304f80a"}, :control_input_bufferMode {:valueType "string", :variable "control_input_bufferMode", :isCheck false, :unit "", :value "RESTART", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_timeResetZero {:valueType "string", :variable "control_input_timeResetZero", :isCheck false, :unit "", :value "", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_bufferCode {:valueType "string", :variable "control_input_bufferCode", :isCheck false, :unit "", :value "input_LWCDJC_ALL_DAQ", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_keepStream {:valueType "string", :variable "control_input_keepStream", :isCheck false, :unit "", :value false, :mode "无", :type "常量", :unitTypeCode "", :varType "Boolean", :unitCode "", :unitType ""}, :control_input_load_interval {:valueType "string", :variable "control_input_load_interval", :isCheck false, :unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :value 0, :mode "signal_load", :type "常量", :unitTypeCode "dimension_Force", :varType "Number", :unitCode "unit_cdw13c2640", :axisType "signal_load", :unitType "56019f36-d734-40c0-8872-73b60304f80a"}, :control_input_saveDB {:valueType "", :variable "control_input_saveDB", :isCheck true, :unit "", :value true, :mode "无", :type "常量", :unitTypeCode "", :varType "Boolean", :unitCode "", :unitType ""}, :control_input_strain_interval {:valueType "string", :variable "control_input_strain_interval", :isCheck false, :unit "e655c1f0-26c0-41aa-9d1d-25c94c059d2e", :value 0, :mode "signal_ext", :type "常量", :unitTypeCode "dimension_Displacement", :varType "Number", :unitCode "unit_cdw4b9c1d4", :axisType "signal_pos", :unitType "6420a172-7a93-45c6-a8da-7ccb275a1aad"}, :control_input_time_interval {:valueType "string", :variable "control_input_time_interval", :isCheck true, :unit "f9b2c0be-a829-4072-878e-f5f592308f79", :value 0.001, :mode "signal_time", :type "常量", :unitTypeCode "dimension_Time", :varType "Number", :unitCode "unit_cdw3860fb2", :axisType "signal_time", :unitType "54891d75-4375-4253-8a5b-068423501a0a"}}, :edition "standard", :daq_code "DAQf413946b3d"}, :nameShow "DAQ", :task-type :daq, :status :ready, :id "daq-a47ba0de-3727-4ea7-a523-2fd2fbd8fb7b"}] [{:name "等待某个事件", :params {:schedule {:control_input_interval {:valueType "string", :variable "control_input_interval", :isCheck false, :unit "", :value "500", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_script {:valueType "", :variable "control_input_script", :isCheck false, :unit "", :value "try{\r\n  // FuncLibs.Logger.Error(\"裂纹长度检查实时曲线-1\");\r\n  // 获取二维数组变量\r\n  var doubleArrayInput = Model.GetVarByName<DoubleArrayInputVar>(\"input_lwcdjc_ss\");\r\n  // 获取周期产生相关的Buffer输入变量\r\n  var periodicBufferVar = Model.GetVarByName<BufferInputVar>(\"input_LWCDJC_ALL_DAQ\");\r\n  \r\n  // --- 设置二维数组发送模式为增量 ---\r\n  doubleArrayInput.UpdateSendMode(1); // 0: 全量, 1: 增量\r\n  \r\n  // --- 管理上次成功添加到二维数组的周期号 ---\r\n  // 使用Model.dVariable来持久化上次处理的最后一个周期号\r\n  string lastProcessedCycleKey = \"lwjc_all_index\"; // 确保key的唯一性\r\n  double lastSuccessfullyAddedCycleToDoubleArray = -1; // 初始化为-1, 表示还没有处理过任何周期\r\n  \r\n  if (Model.dVariable.ContainsKey(lastProcessedCycleKey))\r\n  {\r\n      lastSuccessfullyAddedCycleToDoubleArray = Model.dVariable[lastProcessedCycleKey];\r\n  }\r\n  else\r\n  {\r\n      Model.dVariable[lastProcessedCycleKey] = -1.0; // 首次运行时初始化为-1.0 (as double)\r\n      // 注意：如果这是第一次切换到增量模式，并且希望doubleArrayInput从空开始，\r\n      // 你可能需要在这里或通过外部逻辑调用一次 doubleArrayInput.ClearDoubleArrayData();\r\n      // doubleArrayInput.ClearDoubleArrayData(); // 如果需要在首次增量运行时清空，取消此行注释\r\n  }\r\n  \r\n  // --- 从Buffer获取所需数据列 ---\r\n  // 关键的周期号数据\r\n  var extBufferData = periodicBufferVar.Value[\"signal_ext\"].GetBuffer();\r\n  var timeBufferData = periodicBufferVar.Value[\"signal_time\"].GetBuffer();\r\n  var posBufferData = periodicBufferVar.Value[\"signal_pos\"].GetBuffer();\r\n  var loadBufferData = periodicBufferVar.Value[\"signal_load\"].GetBuffer();\r\n  var mingLing = periodicBufferVar.Value[\"signal_command\"].GetBuffer();\r\n  \r\n  // FuncLibs.Logger.Error(\"裂纹长度检查实时曲线-2\");\r\n  \r\n  // 如果时间数据Buffer为空，则不进行后续处理\r\n  if (timeBufferData.Length == 0)\r\n  {\r\n      FuncLibs.Logger.Info(\"裂纹长度检查实时曲线 - 'signal_time' Buffer为空，跳过处理。\");\r\n      return false;\r\n  }\r\n  \r\n  // 计算所有相关Buffer的最小长度，以确保安全访问\r\n  int minBufferLength = timeBufferData.Length;\r\n  minBufferLength = Math.Min(minBufferLength, posBufferData.Length);\r\n  minBufferLength = Math.Min(minBufferLength, loadBufferData.Length);\r\n  minBufferLength = Math.Min(minBufferLength, extBufferData.Length);\r\n  minBufferLength = Math.Min(minBufferLength, mingLing.Length);\r\n  \r\n  if (minBufferLength == 0)\r\n  {\r\n      FuncLibs.Logger.Info(\"裂纹长度检查实时曲线 - 一个或多个数据Buffer有效长度为0，跳过处理。\");\r\n      return false;\r\n  }\r\n  \r\n  int newRowsAdded = 0;\r\n  double maxProcessedTimeInCurrentRun = lastSuccessfullyAddedCycleToDoubleArray;\r\n  \r\n  // 遍历数据点，使用minBufferLength作为安全上限\r\n  for (int i = 0; i < minBufferLength; i++)\r\n  {\r\n      // 只处理那些周期号大于上次已成功添加到二维数组的周期号的数据点\r\n      if (timeBufferData[i] > lastSuccessfullyAddedCycleToDoubleArray)\r\n      {\r\n        if(timeBufferData[i]!=0){ // 原始条件保留\r\n          // 创建要添加到二维数组的行数据\r\n          Dictionary<string, object> rowData = new()\r\n          {\r\n              { \"time\", timeBufferData[i] },\r\n              { \"pos\", posBufferData[i] },\r\n              { \"load\", loadBufferData[i] },\r\n              { \"ext\", extBufferData[i] },\r\n              { \"ml\", mingLing[i] }\r\n          };\r\n          // 向二维数组添加行数据\r\n          doubleArrayInput.AddRowData(rowData);\r\n          newRowsAdded++;\r\n          if (timeBufferData[i] > maxProcessedTimeInCurrentRun)\r\n          {\r\n              maxProcessedTimeInCurrentRun = timeBufferData[i];\r\n          }\r\n        }\r\n      }\r\n  }\r\n  // FuncLibs.Logger.Error(\"裂纹长度检查实时曲线-3\");\r\n  \r\n  // 如果有新数据加入到doubleArrayInput，则更新持久化的\"上次成功添加的周期号\"并通知UI\r\n  if (newRowsAdded > 0)\r\n  {\r\n      Model.dVariable[lastProcessedCycleKey] = maxProcessedTimeInCurrentRun; // 使用循环中实际处理的最大时间戳\r\n      doubleArrayInput.SendValueToUI(Model.TemplateName);\r\n  }\r\n  else\r\n  {\r\n      FuncLibs.Logger.Error(\"裂纹长度检查实时曲线-未更新数据\");\r\n  }\r\n  // FuncLibs.Logger.Error(\"裂纹长度检查实时曲线-4\");\r\n} catch(Exception ex){\r\n  FuncLibs.Logger.Error($\"ex.Message: {ex.Message}\");\r\n  FuncLibs.Logger.Error($\"ex.StackTrace: {ex.StackTrace}\");\r\n  System.Diagnostics.StackTrace stackTrace = new System.Diagnostics.StackTrace(ex, true);\r\n  System.Diagnostics.StackFrame frame = stackTrace.GetFrame(0);\r\n  FuncLibs.Logger.Error($\"错误发生在文件: {frame.GetFileName()}, 行号: {frame.GetFileLineNumber()}\");\r\n}\r\n\r\n\r\nreturn false;", :mode "无", :type "常量", :unitTypeCode "", :varType "Text", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "等待某个事件26c0083cbf"}, :nameShow "等待某个事件", :task-type :waitEvent, :status :ready, :id "waitEvent-93759e23-f394-4cb3-9acb-381a8c9be348"}]], :task-type :parallels, :status :ready, :id "parallels-2b5d2fc6-d916-46ae-8155-5fba6aade7fe"}] [{:name "条件-4.4", :params nil, :task-type :if-else, :expr "var blockline = Model.dVariable[\"blockline\"];\nif(blockline==1){\n  return true;\n}\nreturn false;", :true-tasks [], :status :ready, :id "if-else-d9b9633e-d94c-4fbd-8f35-4969fb92685d", :false-tasks [{:name "信号变量检测", :params {:schedule {:control_input_bufferCode {:valueType "string", :variable "control_input_bufferCode", :isCheck false, :unit "", :value "input_LWCDJC_ALL_DAQ", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_condition {:valueType "string", :variable "control_input_condition", :isCheck false, :unit "", :value "1", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_signalCode {:valueType "string", :variable "control_input_signalCode", :isCheck false, :unit "", :value "signal_blockline", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "信号变量检测4533546a79"}, :nameShow "信号变量检测", :task-type :creepSignalCheck, :status :ready, :id "creepSignalCheck-9a1c7b0d-1760-4d5a-90d7-af26123789a5"}]} {:name "等待某个事件", :params {:schedule {:control_input_interval {:valueType "string", :variable "control_input_interval", :isCheck false, :unit "", :value "100", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_script {:valueType "", :variable "control_input_script", :isCheck false, :unit "", :value "try{\n  var doubleArrayList = Model.GetVarByName<DoubleArrayListInputVar>(\"input_lwkz_curve_doubleArrayList\");\n  doubleArrayList.UpdateDoubleArrayListSendMode(1);\n  \n  int  lastTail = Model.iVariable[\"lastTail1\"];\n  var buffer = Model.GetVarByName<BufferInputVar>(\"input_LWCDJC_ALL_DAQ\");\n  var bufferBlockline = buffer.Value[\"signal_blockline\"].GetBuffer();\n  var valueBlockline = buffer.Value[\"signal_blockline\"].GetValue();\n  \n  var index = bufferBlockline.Length - 1;\n  if (index >= 0)\n  {\n  \tfor (int i = lastTail; i < (int)index; i++)\n  \t{\n      if (bufferBlockline[i]==1 || bufferBlockline[i]==2) {\t\t\n        var loadBufferdata =buffer.Value[\"signal_load\"].GetBuffer()[i];\n    \t\tvar extBufferdata = buffer.Value[\"signal_ext\"].GetBuffer()[i];\n    \n    \t\tDictionary<string, object> row = new()\n    \t\t{\n    \t\t\t{ \"col_x\", extBufferdata },\n    \t\t\t{ \"col_y\", loadBufferdata }\n    \t\t};\n    \t\tdoubleArrayList.AddDoubleArrayListData(0, row);\n      }\n  \t}\n  \tModel.iVariable[\"lastTail1\"] = index;\n  }\n  doubleArrayList.SendValueToUI(Model.TemplateName);\n  \n  if(valueBlockline==3){\n    return true;\n  }\n} catch (Exception e)\n{\n  FuncLibs.Logger.Error($\"裂纹长度检查，等待某个事件子任务，waitEvent-54a9826e-7e5e-4b46-a94a-3868836dcd6f脚本报错信息：{e.ToString()} , {e.StackTrace}\");\n}\n\nreturn false;", :mode "无", :type "常量", :unitTypeCode "", :varType "Text", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "等待某个事件7d2400876a"}, :nameShow "等待某个事件(负责更新曲线)", :task-type :waitEvent, :status :ready, :id "waitEvent-54a9826e-7e5e-4b46-a94a-3868836dcd6f"} {:name "等待某个事件", :params {:schedule {:control_input_interval {:valueType "string", :variable "control_input_interval", :isCheck false, :unit "", :value "100", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_script {:valueType "", :variable "control_input_script", :isCheck false, :unit "", :value "\r\n\r\nvar doubleArrayList = Model.GetVarByName<DoubleArrayListInputVar>(\"input_lwkz_curve_doubleArrayList\");\r\ndoubleArrayList.UpdateDoubleArrayListSendMode(1);\r\n\r\nint  lastTail = Model.iVariable[\"lastTail2\"];\r\nvar buffer = Model.GetVarByName<BufferInputVar>(\"input_LWCDJC_ALL_DAQ\");\r\nvar bufferBlockline = buffer.Value[\"signal_blockline\"].GetBuffer();\r\nvar valueBlockline = buffer.Value[\"signal_blockline\"].GetValue();\r\n\r\nvar index = bufferBlockline.Length - 1;\r\nif (index >= 0)\r\n{\r\n\tfor (int i = lastTail; i < (int)index; i++)\r\n\t{\r\n    if (bufferBlockline[i]==3 || bufferBlockline[i]==4) {\t\t\r\n      var loadBufferdata =buffer.Value[\"signal_load\"].GetBuffer()[i];\r\n  \t\tvar extBufferdata = buffer.Value[\"signal_ext\"].GetBuffer()[i];\r\n  \r\n  \t\tDictionary<string, object> row = new()\r\n  \t\t{\r\n  \t\t\t{ \"col_x\", extBufferdata },\r\n  \t\t\t{ \"col_y\", loadBufferdata }\r\n  \t\t};\r\n  \t\tdoubleArrayList.AddDoubleArrayListData(1, row);\r\n    }\r\n\t}\r\n\tModel.iVariable[\"lastTail2\"] = index;\r\n}\r\ndoubleArrayList.SendValueToUI(Model.TemplateName);\r\n\r\nif(valueBlockline==5){\r\n  return true;\r\n}\r\nreturn false;", :mode "无", :type "常量", :unitTypeCode "", :varType "Text", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "等待某个事件e1b4d85b0d"}, :nameShow "等待某个事件", :task-type :waitEvent, :status :ready, :id "waitEvent-ffdc7080-10a2-478e-9add-ad7db595b4ee"} {:name "等待某个事件", :params {:schedule {:control_input_interval {:valueType "string", :variable "control_input_interval", :isCheck false, :unit "", :value "100", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_script {:valueType "", :variable "control_input_script", :isCheck false, :unit "", :value "var doubleArrayList = Model.GetVarByName<DoubleArrayListInputVar>(\"input_lwkz_curve_doubleArrayList\");\r\ndoubleArrayList.UpdateDoubleArrayListSendMode(1);\r\n\r\nint  lastTail = Model.iVariable[\"lastTail3\"];\r\nvar buffer = Model.GetVarByName<BufferInputVar>(\"input_LWCDJC_ALL_DAQ\");\r\nvar bufferBlockline = buffer.Value[\"signal_blockline\"].GetBuffer();\r\nvar valueBlockline = buffer.Value[\"signal_blockline\"].GetValue();\r\n\r\nvar index = bufferBlockline.Length - 1;\r\nif (index >= 0)\r\n{\r\n\tfor (int i = lastTail; i < (int)index; i++)\r\n\t{\r\n    if (bufferBlockline[i]==5 || bufferBlockline[i]==6) {\t\t\r\n      var loadBufferdata =buffer.Value[\"signal_load\"].GetBuffer()[i];\r\n  \t\tvar extBufferdata = buffer.Value[\"signal_ext\"].GetBuffer()[i];\r\n  \r\n  \t\tDictionary<string, object> row = new()\r\n  \t\t{\r\n  \t\t\t{ \"col_x\", extBufferdata },\r\n  \t\t\t{ \"col_y\", loadBufferdata }\r\n  \t\t};\r\n  \t\tdoubleArrayList.AddDoubleArrayListData(2, row);\r\n    }\r\n\t}\r\n\tModel.iVariable[\"lastTail3\"] = index;\r\n}\r\ndoubleArrayList.SendValueToUI(Model.TemplateName);\r\n\r\nreturn false;", :mode "无", :type "常量", :unitTypeCode "", :varType "Text", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "等待某个事件961148a40c"}, :nameShow "等待某个事件", :task-type :waitEvent, :status :ready, :id "waitEvent-3989f720-9718-4165-ae9b-bb1c936ce43b"}] [{:name "等待某个事件", :params {:schedule {:control_input_interval {:valueType "string", :variable "control_input_interval", :isCheck false, :unit "", :value "50", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_script {:valueType "", :variable "control_input_script", :isCheck false, :unit "", :value "try{\n  var blockline = Model.GetVarByName<BufferInputVar>(\"input_LWCDJC_ALL_DAQ\").Value[\"signal_blockline\"].GetValue();\n  if(blockline==1){\n    return true;\n  }\n} catch (Exception e){\n  FuncLibs.Logger.Error($\"裂纹长度检查，等待某个事件子任务，blockline脚本报错信息：{e.ToString()} , {e.StackTrace}\");\n}\n\nreturn false;", :mode "无", :type "常量", :unitTypeCode "", :varType "Text", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "等待某个事件f47e394a04"}, :nameShow "等待某个事件", :task-type :waitEvent, :status :ready, :id "waitEvent-426ba70c-a598-491b-ae16-d5a27715763f"} {:branch-idxs [0], :name "并行-3.2", :params nil, :branches [[{:name "信号变量检测", :params {:schedule {:control_input_bufferCode {:valueType "string", :variable "control_input_bufferCode", :isCheck false, :unit "", :value "input_LWCDJC_ALL_DAQ", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_condition {:valueType "string", :variable "control_input_condition", :isCheck false, :unit "", :value "1", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_signalCode {:valueType "string", :variable "control_input_signalCode", :isCheck false, :unit "", :value "signal_blockline", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "信号变量检测b810c74e20"}, :nameShow "信号变量检测", :task-type :creepSignalCheck, :status :ready, :id "creepSignalCheck-8d313bcb-d7cc-424e-bb19-d7d82337452b"}] [{:name "DAQ", :params {:schedule {:control_input_move_interval {:valueType "string", :variable "control_input_move_interval", :isCheck false, :unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :value 0, :mode "signal_load", :type "常量", :unitTypeCode "dimension_Force", :varType "Number", :unitCode "unit_cdw13c2640", :axisType "signal_load", :unitType "56019f36-d734-40c0-8872-73b60304f80a"}, :control_input_bufferMode {:valueType "string", :variable "control_input_bufferMode", :isCheck false, :unit "", :value "RESTART", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_timeResetZero {:valueType "string", :variable "control_input_timeResetZero", :isCheck false, :unit "", :value "", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_bufferCode {:valueType "string", :variable "control_input_bufferCode", :isCheck false, :unit "", :value "input_lwjc_no1_down", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_keepStream {:valueType "string", :variable "control_input_keepStream", :isCheck false, :unit "", :value false, :mode "无", :type "常量", :unitTypeCode "", :varType "Boolean", :unitCode "", :unitType ""}, :control_input_load_interval {:valueType "string", :variable "control_input_load_interval", :isCheck false, :unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :value 0, :mode "signal_load", :type "常量", :unitTypeCode "dimension_Force", :varType "Number", :unitCode "unit_cdw13c2640", :axisType "signal_load", :unitType "56019f36-d734-40c0-8872-73b60304f80a"}, :control_input_saveDB {:valueType "", :variable "control_input_saveDB", :isCheck true, :unit "", :value true, :mode "无", :type "常量", :unitTypeCode "", :varType "Boolean", :unitCode "", :unitType ""}, :control_input_strain_interval {:valueType "string", :variable "control_input_strain_interval", :isCheck false, :unit "e655c1f0-26c0-41aa-9d1d-25c94c059d2e", :value 0, :mode "signal_ext", :type "常量", :unitTypeCode "dimension_Displacement", :varType "Number", :unitCode "unit_cdw4b9c1d4", :axisType "signal_pos", :unitType "6420a172-7a93-45c6-a8da-7ccb275a1aad"}, :control_input_time_interval {:valueType "string", :variable "control_input_time_interval", :isCheck true, :unit "f9b2c0be-a829-4072-878e-f5f592308f79", :value 0.001, :mode "signal_time", :type "常量", :unitTypeCode "dimension_Time", :varType "Number", :unitCode "unit_cdw3860fb2", :axisType "signal_time", :unitType "54891d75-4375-4253-8a5b-068423501a0a"}}, :edition "standard", :daq_code "DAQ2e9ff75f34"}, :nameShow "DAQ", :task-type :daq, :status :ready, :id "daq-0ac09652-82d1-4130-a028-2e081713c233"}]], :task-type :parallels, :status :ready, :id "parallels-065884ba-5163-40ad-80d3-d98a12352be8"} {:name "脚本执行子任务", :params {:schedule {:control_input_script {:valueType "string", :variable "control_input_script", :isCheck false, :unit "", :value "\n  // 计算第一段的值\n\n// 裂纹长度检查斜波下降段daq\nvar LWCDJC_DOWN_DAQ = Model.GetVarByName<BufferInputVar>(\"input_lwjc_no1_down\");\n\n// 客户填写的两个输入变量\nvar cankaotanxingmuliang = Model.GetVarByName<NumberInputVar>(\"input_LWCDJC_CKTXML\").Value;\nvar shijiliewenchangdu = Model.GetVarByName<NumberInputVar>(\"input_LWCDJC_SJCD\").Value;\n\n\n// 获取下降段的负荷和变形数据\n\nvar loadBufferdata = LWCDJC_DOWN_DAQ[\"signal_load\"];\nvar extBufferdata = LWCDJC_DOWN_DAQ[\"signal_ext\"];\nint length=loadBufferdata.Length;\nif(length == 0){\n  FuncLibs.Logger.Error(\"裂纹长度检查未采集到数!\");\n  return false;\n}\nfor (int i = 0; i < length; i++)\n{\n    loadBufferdata[i] = Math.Abs(loadBufferdata[i]);\n}\nFuncLibs.Logger.Error(\"裂纹检查第一个下降段的负荷数组长度:\"+length);\n// foreach(var item in loadBufferdata){\n//   FuncLibs.Logger.Error(item + \",\");\n// }\nFuncLibs.Logger.Error(\"裂纹检查第一个下降段的变形数组长度:\"+length);\n// foreach(var item in extBufferdata){\n//   FuncLibs.Logger.Error(item + \",\");\n// }\n\n  FuncLibs.Logger.Error(\"=== 第一段GetCrackLength函数参数打印 ===\");\n  FuncLibs.Logger.Error(\"参数1 - extBufferdata数组长度: \" + extBufferdata.Length);\n  FuncLibs.Logger.Error(\"参数1 - extBufferdata数组内容: [\" + string.Join(\", \", extBufferdata) + \"]\");\n  FuncLibs.Logger.Error(\"参数2 - loadBufferdata数组长度: \" + loadBufferdata.Length);\n  FuncLibs.Logger.Error(\"参数2 - loadBufferdata数组内容: [\" + string.Join(\", \", loadBufferdata) + \"]\");\n  FuncLibs.Logger.Error(\"参数3 - length: \" + length);\n  FuncLibs.Logger.Error(\"=== 参数打印完毕，开始调用函数 ===\");\n//  调用公式 计算柔度和实际裂纹长度\nvar res= TestExpert.PlaneStrainFractureToughnessMethod.Commons\n.PlaneStrainFractureToughnessMethod.GetCrackLength(extBufferdata, loadBufferdata, length,out double crackLength, out double slope);\n\n// 计算弹性模量\nvar res2 = TestExpert.PlaneStrainFractureToughnessMethod.Commons\n.PlaneStrainFractureToughnessMethod.ModulusFromCrackLength( slope, cankaotanxingmuliang,  shijiliewenchangdu, out double modulus);\n\nFuncLibs.Logger.Error(\"裂纹检查第一个下降段的弹性模量:\"+modulus);\nFuncLibs.Logger.Error(\"裂纹检查第一个下降段参考弹性模量:\"+cankaotanxingmuliang);\nFuncLibs.Logger.Error(\"裂纹检查第一个下降段的计算裂纹长度:\"+shijiliewenchangdu);\nFuncLibs.Logger.Error(\"裂纹检查第一个下降段的计算cres:\"+res);\nFuncLibs.Logger.Error(\"裂纹检查第一个下降段的参考裂纹长度:\"+crackLength);\nFuncLibs.Logger.Error(\"裂纹检查第一个下降段的卸载段柔度:\"+slope);\n\n// 裂纹长度检查结果二维数组\nvar LWCDJCJG_INPUT = Model.GetVarByName<DoubleArrayInputVar>(\"input_liewenchangdujieguo_doubleArray\");\n// 设置发送模式 0全量 1增量\nLWCDJCJG_INPUT.UpdateSendMode(1);\n\nDictionary<string, object> row = new()\n        {\n          { \"LW_JCJS\", 1.0 },\n          { \"LW_XZDRD\", Math.Round(slope,6) },\n          { \"LW_CKTXML\", Math.Round(cankaotanxingmuliang,2) },\n          { \"LW_JSTXML\", Math.Round(modulus,2) },\n          { \"LW_CKLWCD\", Math.Round(shijiliewenchangdu,2) },\n          { \"LW_JSLWCD\", Math.Round(crackLength,2) }\n        };\n\n// 添加数据\nLWCDJCJG_INPUT.AddRowData(row);\n// 通知ui\nLWCDJCJG_INPUT.SendValueToUI(Model.TemplateName);\n\n\n\n\nreturn false;", :mode "无", :type "常量", :unitTypeCode "", :varType "Text", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "脚本执行子任务4e1bea2973"}, :nameShow "脚本执行子任务", :task-type :SubTaskEvalScript, :status :ready, :id "SubTaskEvalScript-2d644e51-aac7-477a-99b7-7f3efe700e51"} {:name "等待某个事件", :params {:schedule {:control_input_interval {:valueType "string", :variable "control_input_interval", :isCheck false, :unit "", :value "50", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_script {:valueType "", :variable "control_input_script", :isCheck false, :unit "", :value "var blockline = Model.GetVarByName<BufferInputVar>(\"input_LWCDJC_ALL_DAQ\").Value[\"signal_blockline\"].GetValue();\nif(blockline==3){\n  return true;\n}\nreturn false;", :mode "无", :type "常量", :unitTypeCode "", :varType "Text", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "等待某个事件9d8f82ffd4"}, :nameShow "等待某个事件", :task-type :waitEvent, :status :ready, :id "waitEvent-5cdadc8f-47b5-4639-a8b1-d26cbf98093f"} {:branch-idxs [0], :name "并行-9.6", :params nil, :branches [[{:name "信号变量检测", :params {:schedule {:control_input_bufferCode {:valueType "string", :variable "control_input_bufferCode", :isCheck false, :unit "", :value "input_LWCDJC_ALL_DAQ", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_condition {:valueType "string", :variable "control_input_condition", :isCheck false, :unit "", :value "1", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_signalCode {:valueType "string", :variable "control_input_signalCode", :isCheck false, :unit "", :value "signal_blockline", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "信号变量检测0e5548505b"}, :nameShow "信号变量检测", :task-type :creepSignalCheck, :status :ready, :id "creepSignalCheck-71a89ec2-eaf7-4ba4-aa56-76c5a8ea8f69"}] [{:name "DAQ", :params {:schedule {:control_input_move_interval {:valueType "string", :variable "control_input_move_interval", :isCheck false, :unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :value 0, :mode "signal_load", :type "常量", :unitTypeCode "dimension_Force", :varType "Number", :unitCode "unit_cdw13c2640", :axisType "signal_load", :unitType "56019f36-d734-40c0-8872-73b60304f80a"}, :control_input_bufferMode {:valueType "string", :variable "control_input_bufferMode", :isCheck false, :unit "", :value "RESTART", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_timeResetZero {:valueType "string", :variable "control_input_timeResetZero", :isCheck false, :unit "", :value "", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_bufferCode {:valueType "string", :variable "control_input_bufferCode", :isCheck false, :unit "", :value "input_lwjc_no2_down", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_keepStream {:valueType "string", :variable "control_input_keepStream", :isCheck false, :unit "", :value false, :mode "无", :type "常量", :unitTypeCode "", :varType "Boolean", :unitCode "", :unitType ""}, :control_input_load_interval {:valueType "string", :variable "control_input_load_interval", :isCheck false, :unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :value 0, :mode "signal_load", :type "常量", :unitTypeCode "dimension_Force", :varType "Number", :unitCode "unit_cdw13c2640", :axisType "signal_load", :unitType "56019f36-d734-40c0-8872-73b60304f80a"}, :control_input_saveDB {:valueType "", :variable "control_input_saveDB", :isCheck true, :unit "", :value true, :mode "无", :type "常量", :unitTypeCode "", :varType "Boolean", :unitCode "", :unitType ""}, :control_input_strain_interval {:valueType "string", :variable "control_input_strain_interval", :isCheck false, :unit "e655c1f0-26c0-41aa-9d1d-25c94c059d2e", :value 0, :mode "signal_ext", :type "常量", :unitTypeCode "dimension_Displacement", :varType "Number", :unitCode "unit_cdw4b9c1d4", :axisType "signal_pos", :unitType "6420a172-7a93-45c6-a8da-7ccb275a1aad"}, :control_input_time_interval {:valueType "string", :variable "control_input_time_interval", :isCheck true, :unit "f9b2c0be-a829-4072-878e-f5f592308f79", :value 0.001, :mode "signal_time", :type "常量", :unitTypeCode "dimension_Time", :varType "Number", :unitCode "unit_cdw3860fb2", :axisType "signal_time", :unitType "54891d75-4375-4253-8a5b-068423501a0a"}}, :edition "standard", :daq_code "DAQ66559e853b"}, :nameShow "DAQ", :task-type :daq, :status :ready, :id "daq-43bef327-78fb-4784-9a26-7efed0ab4432"}]], :task-type :parallels, :status :ready, :id "parallels-0d858c2f-1920-4025-b7b0-b6ca1bf1a3c0"} {:name "脚本执行子任务", :params {:schedule {:control_input_script {:valueType "string", :variable "control_input_script", :isCheck false, :unit "", :value "  // 计算第二段的值\n// 裂纹长度检查斜波下降段daq\nvar LWCDJC_DOWN_DAQ = Model.GetVarByName<BufferInputVar>(\"input_lwjc_no2_down\");\n\n// 客户填写的两个输入变量\nvar cankaotanxingmuliang=Model.GetVarByName<NumberInputVar>(\"input_LWCDJC_CKTXML\").Value;\nvar shijiliewenchangdu=Model.GetVarByName<NumberInputVar>(\"input_LWCDJC_SJCD\").Value;\n// Console.WriteLine(\"裂纹检查第二个下降段cankaotanxingmuliang:\"+cankaotanxingmuliang);\n// Console.WriteLine(\"裂纹检查第二个下降段的shijiliewenchangdu:\"+shijiliewenchangdu);\n\n// 获取下降段的负荷和变形数据\n\nvar loadBufferdata = LWCDJC_DOWN_DAQ[\"signal_load\"];\nvar extBufferdata = LWCDJC_DOWN_DAQ[\"signal_ext\"];\nint length=loadBufferdata.Length;\nif(length == 0){\n  FuncLibs.Logger.Error(\"裂纹长度检查未采集到数!\");\n  return false;\n}\nfor (int i = 0; i < length; i++)\n{\n    loadBufferdata[i] = Math.Abs(loadBufferdata[i]);\n}\n// FuncLibs.Logger.Error(\"裂纹检查第二个下降段的数组长度:\"+length);\n\n  FuncLibs.Logger.Error(\"=== 第二段GetCrackLength函数参数打印 ===\");\n  FuncLibs.Logger.Error(\"参数1 - extBufferdata数组长度: \" + extBufferdata.Length);\n  FuncLibs.Logger.Error(\"参数1 - extBufferdata数组内容: [\" + string.Join(\", \", extBufferdata) + \"]\");\n  FuncLibs.Logger.Error(\"参数2 - loadBufferdata数组长度: \" + loadBufferdata.Length);\n  FuncLibs.Logger.Error(\"参数2 - loadBufferdata数组内容: [\" + string.Join(\", \", loadBufferdata) + \"]\");\n  FuncLibs.Logger.Error(\"参数3 - length: \" + length);\n  FuncLibs.Logger.Error(\"=== 参数打印完毕，开始调用函数 ===\");\n\n//  调用公式 计算柔度和实际裂纹长度\nvar res= TestExpert.PlaneStrainFractureToughnessMethod.Commons\n.PlaneStrainFractureToughnessMethod.GetCrackLength(extBufferdata, loadBufferdata, length,out double crackLength, out double slope);\n// Console.WriteLine(\"裂纹检查第二个下降段的计算cres:\"+res);\n// FuncLibs.Logger.Error(\"裂纹检查第二个下降段的计算crackLength:\"+crackLength);\n// Console.WriteLine(\"裂纹检查第二个下降段的slope:\"+slope);\n\n// 计算弹性模量\nTestExpert.PlaneStrainFractureToughnessMethod.Commons\n.PlaneStrainFractureToughnessMethod.ModulusFromCrackLength( slope, cankaotanxingmuliang,  shijiliewenchangdu, out double modulus);\n// 裂纹长度检查结果二维数组\nvar LWCDJCJG_INPUT = Model.GetVarByName<DoubleArrayInputVar>(\"input_liewenchangdujieguo_doubleArray\");\n// 设置发送模式 0全量 1增量\nLWCDJCJG_INPUT.UpdateSendMode(1);\n\nFuncLibs.Logger.Error(\"裂纹检查第二个下降段的数组长度:\"+length);\n\nFuncLibs.Logger.Error(\"裂纹检查第二个下降段的弹性模量:\"+modulus);\nFuncLibs.Logger.Error(\"裂纹检查第二个下降段参考弹性模量:\"+cankaotanxingmuliang);\nFuncLibs.Logger.Error(\"裂纹检查第二个下降段的计算裂纹长度:\"+shijiliewenchangdu);\nFuncLibs.Logger.Error(\"裂纹检查第二个下降段的计算cres:\"+res);\nFuncLibs.Logger.Error(\"裂纹检查第二个下降段的参考裂纹长度:\"+crackLength);\nFuncLibs.Logger.Error(\"裂纹检查第二个下降段的卸载段柔度:\"+slope);\n\nDictionary<string, object> row = new()\n        {\n          { \"LW_JCJS\", 2.0 },\n          { \"LW_XZDRD\", Math.Round(slope,6) },\n          { \"LW_CKTXML\", Math.Round(cankaotanxingmuliang,2) },\n          { \"LW_JSTXML\", Math.Round(modulus,2)},\n          { \"LW_CKLWCD\", Math.Round(shijiliewenchangdu,2) },\n          { \"LW_JSLWCD\", Math.Round(crackLength,2) }\n        };\n\n// 添加数据\nLWCDJCJG_INPUT.AddRowData(row);\n// 通知ui\nLWCDJCJG_INPUT.SendValueToUI(Model.TemplateName);\n\n\nreturn false;", :mode "无", :type "常量", :unitTypeCode "", :varType "Text", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "脚本执行子任务2781750658"}, :nameShow "脚本执行子任务", :task-type :SubTaskEvalScript, :status :ready, :id "SubTaskEvalScript-153bfad7-49bc-43e7-a801-219b119c9104"} {:name "等待某个事件", :params {:schedule {:control_input_interval {:valueType "string", :variable "control_input_interval", :isCheck false, :unit "", :value "50", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_script {:valueType "", :variable "control_input_script", :isCheck false, :unit "", :value "var blockline = Model.GetVarByName<BufferInputVar>(\"input_LWCDJC_ALL_DAQ\").Value[\"signal_blockline\"].GetValue();\nif(blockline==5){\n  return true;\n}\nreturn false;", :mode "无", :type "常量", :unitTypeCode "", :varType "Text", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "等待某个事件d374a3bbf8"}, :nameShow "等待某个事件", :task-type :waitEvent, :status :ready, :id "waitEvent-911d94cb-a26d-4dda-bb0b-281270094ee5"} {:branch-idxs [0], :name "并行-2.5", :params nil, :branches [[{:name "信号变量检测", :params {:schedule {:control_input_bufferCode {:valueType "string", :variable "control_input_bufferCode", :isCheck false, :unit "", :value "input_LWCDJC_ALL_DAQ", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_condition {:valueType "string", :variable "control_input_condition", :isCheck false, :unit "", :value "1", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_signalCode {:valueType "string", :variable "control_input_signalCode", :isCheck false, :unit "", :value "signal_blockline", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "信号变量检测effacf4311"}, :nameShow "信号变量检测", :task-type :creepSignalCheck, :status :ready, :id "creepSignalCheck-05ef462b-d544-4857-9945-538a503ecb00"}] [{:name "DAQ", :params {:schedule {:control_input_move_interval {:valueType "string", :variable "control_input_move_interval", :isCheck false, :unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :value 0, :mode "signal_load", :type "常量", :unitTypeCode "dimension_Force", :varType "Number", :unitCode "unit_cdw13c2640", :axisType "signal_load", :unitType "56019f36-d734-40c0-8872-73b60304f80a"}, :control_input_bufferMode {:valueType "string", :variable "control_input_bufferMode", :isCheck false, :unit "", :value "RESTART", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_timeResetZero {:valueType "string", :variable "control_input_timeResetZero", :isCheck false, :unit "", :value "", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_bufferCode {:valueType "string", :variable "control_input_bufferCode", :isCheck false, :unit "", :value "input_lwjc_no3_down", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_keepStream {:valueType "string", :variable "control_input_keepStream", :isCheck false, :unit "", :value false, :mode "无", :type "常量", :unitTypeCode "", :varType "Boolean", :unitCode "", :unitType ""}, :control_input_load_interval {:valueType "string", :variable "control_input_load_interval", :isCheck false, :unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :value 0, :mode "signal_load", :type "常量", :unitTypeCode "dimension_Force", :varType "Number", :unitCode "unit_cdw13c2640", :axisType "signal_load", :unitType "56019f36-d734-40c0-8872-73b60304f80a"}, :control_input_saveDB {:valueType "", :variable "control_input_saveDB", :isCheck true, :unit "", :value true, :mode "无", :type "常量", :unitTypeCode "", :varType "Boolean", :unitCode "", :unitType ""}, :control_input_strain_interval {:valueType "string", :variable "control_input_strain_interval", :isCheck false, :unit "e655c1f0-26c0-41aa-9d1d-25c94c059d2e", :value 0, :mode "signal_ext", :type "常量", :unitTypeCode "dimension_Displacement", :varType "Number", :unitCode "unit_cdw4b9c1d4", :axisType "signal_pos", :unitType "6420a172-7a93-45c6-a8da-7ccb275a1aad"}, :control_input_time_interval {:valueType "string", :variable "control_input_time_interval", :isCheck true, :unit "f9b2c0be-a829-4072-878e-f5f592308f79", :value 0.001, :mode "signal_time", :type "常量", :unitTypeCode "dimension_Time", :varType "Number", :unitCode "unit_cdw3860fb2", :axisType "signal_time", :unitType "54891d75-4375-4253-8a5b-068423501a0a"}}, :edition "standard", :daq_code "DAQ3ee5451f4d"}, :nameShow "DAQ", :task-type :daq, :status :ready, :id "daq-32891850-ce61-48d8-98cd-20634cf737c7"}]], :task-type :parallels, :status :ready, :id "parallels-084dfc84-6967-43f6-b73e-4bff0ca007fb"}]], :task-type :parallels, :status :ready, :id "parallels-bbf6bb05-ccf9-4d31-b694-68ee26cce7d1"} {:name "脚本执行子任务", :params {:schedule {:control_input_script {:valueType "string", :variable "control_input_script", :isCheck false, :unit "", :value " // 计算第三段的值\n\n// 裂纹长度检查斜波下降段daq\nvar LWCDJC_DOWN_DAQ = Model.GetVarByName<BufferInputVar>(\"input_lwjc_no3_down\");\n\n// 客户填写的两个输入变量\nvar cankaotanxingmuliang=Model.GetVarByName<NumberInputVar>(\"input_LWCDJC_CKTXML\").Value;\nvar shijiliewenchangdu=Model.GetVarByName<NumberInputVar>(\"input_LWCDJC_SJCD\").Value;\n// Console.WriteLine(\"裂纹检查第三个下降段cankaotanxingmuliang:\"+cankaotanxingmuliang);\n// Console.WriteLine(\"裂纹检查第三个下降段的shijiliewenchangdu:\"+shijiliewenchangdu);\n\n// 获取下降段的负荷和变形数据\n\nvar loadBufferdata = LWCDJC_DOWN_DAQ[\"signal_load\"];\nvar extBufferdata = LWCDJC_DOWN_DAQ[\"signal_ext\"];\nint length=loadBufferdata.Length;\nif(length == 0){\n  FuncLibs.Logger.Error(\"裂纹长度检查未采集到数!\");\n  return false;\n}\nfor (int i = 0; i < length; i++)\n{\n    loadBufferdata[i] = Math.Abs(loadBufferdata[i]);\n}\n\n\n  FuncLibs.Logger.Error(\"=== 第三段GetCrackLength函数参数打印 ===\");\n  FuncLibs.Logger.Error(\"参数1 - extBufferdata数组长度: \" + extBufferdata.Length);\n  FuncLibs.Logger.Error(\"参数1 - extBufferdata数组内容: [\" + string.Join(\", \", extBufferdata) + \"]\");\n  FuncLibs.Logger.Error(\"参数2 - loadBufferdata数组长度: \" + loadBufferdata.Length);\n  FuncLibs.Logger.Error(\"参数2 - loadBufferdata数组内容: [\" + string.Join(\", \", loadBufferdata) + \"]\");\n  FuncLibs.Logger.Error(\"参数3 - length: \" + length);\n  FuncLibs.Logger.Error(\"=== 参数打印完毕，开始调用函数 ===\");\n//  调用公式 计算柔度和实际裂纹长度\nvar res= TestExpert.PlaneStrainFractureToughnessMethod.Commons\n.PlaneStrainFractureToughnessMethod.GetCrackLength(extBufferdata, loadBufferdata, length,out double crackLength, out double slope);\n// Console.WriteLine(\"裂纹检查第三个下降段的计算cres:\"+res);\n// Console.WriteLine(\"裂纹检查第三个下降段的计算crackLength:\"+crackLength);\n// Console.WriteLine(\"裂纹检查第三个下降段的slope:\"+slope);\n\n// 计算弹性模量\nTestExpert.PlaneStrainFractureToughnessMethod.Commons\n.PlaneStrainFractureToughnessMethod.ModulusFromCrackLength( slope, cankaotanxingmuliang,  shijiliewenchangdu, out double modulus);\n// 裂纹长度检查结果二维数组\nvar LWCDJCJG_INPUT = Model.GetVarByName<DoubleArrayInputVar>(\"input_liewenchangdujieguo_doubleArray\");\n// 设置发送模式 0全量 1增量\nLWCDJCJG_INPUT.UpdateSendMode(1);\nFuncLibs.Logger.Error(\"裂纹检查第三个下降段的数组长度:\"+length);\nFuncLibs.Logger.Error(\"裂纹检查第三个下降段的弹性模量:\"+modulus);\nFuncLibs.Logger.Error(\"裂纹检查第三个下降段参考弹性模量:\"+cankaotanxingmuliang);\nFuncLibs.Logger.Error(\"裂纹检查第三个下降段的计算裂纹长度:\"+shijiliewenchangdu);\nFuncLibs.Logger.Error(\"裂纹检查第三个下降段的计算cres:\"+res);\nFuncLibs.Logger.Error(\"裂纹检查第三个下降段的参考裂纹长度:\"+crackLength);\nFuncLibs.Logger.Error(\"裂纹检查第三个下降段的卸载段柔度:\"+slope);\n\n\nDictionary<string, object> row = new()\n        {\n          { \"LW_JCJS\", 3.0 },\n          { \"LW_XZDRD\", Math.Round(slope,6) },\n          { \"LW_CKTXML\", Math.Round(cankaotanxingmuliang,2) },\n          { \"LW_JSTXML\", Math.Round(modulus,2)},\n          { \"LW_CKLWCD\", Math.Round(shijiliewenchangdu,2) },\n          { \"LW_JSLWCD\", Math.Round(crackLength,2) }\n        };\n\n// 添加数据\nLWCDJCJG_INPUT.AddRowData(row);\n// 通知ui\nLWCDJCJG_INPUT.SendValueToUI(Model.TemplateName);\n\nreturn false;", :mode "无", :type "常量", :unitTypeCode "", :varType "Text", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "脚本执行子任务5ffe79478b"}, :nameShow "脚本执行子任务", :task-type :SubTaskEvalScript, :status :ready, :id "SubTaskEvalScript-17b2ea0b-475e-438a-a9e6-987c3d6bd8d7"} {:name "脚本执行子任务", :params {:schedule {:control_input_script {:valueType "string", :variable "control_input_script", :isCheck false, :unit "", :value "Model.GetVarByName<DoubleArrayListInputVar>(\"input_lwkz_curve_doubleArrayList\").DoubleArrayListDataToDB();\r\nModel.GetVarByName<DoubleArrayInputVar>(\"input_lwcdjc_ss\").DoubleArrayDataToDB();\r\nModel.GetVarByName<DoubleArrayInputVar>(\"input_liewenchangdujieguo_doubleArray\").DoubleArrayDataToDB();\r\nreturn false;", :mode "无", :type "常量", :unitTypeCode "", :varType "Text", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "脚本执行子任务f1981d3573"}, :nameShow "脚本执行子任务", :task-type :SubTaskEvalScript, :status :ready, :id "SubTaskEvalScript-da720ab0-0182-4e66-b915-3462002a8683"} {:name "脚本执行子任务", :params {:schedule {:control_input_script {:valueType "string", :variable "control_input_script", :isCheck false, :unit "", :value "string ydfs = \"负荷\";\r\nif(Model.GetVarByName<SelectInputVar>(\"input_LWCDJC_YDFS\").Value[2] == 0){\r\n  ydfs = \"位移\";\r\n}\r\nif(Model.GetVarByName<SelectInputVar>(\"input_LWCDJC_YDFS\").Value[2] == 1){\r\n  ydfs = \"负荷\";\r\n}\r\nif(Model.GetVarByName<SelectInputVar>(\"input_LWCDJC_YDFS\").Value[2] == 2){\r\n  ydfs = \"变形\";\r\n}\r\n\r\nstring significance = \"高\";\r\nstring type = \"信息\";\r\nstring grade = \"信息\";\r\nstring content = \"平面应变断裂韧度试验 裂纹长度检查操作试验运行 \"\r\n                 + Model.CurrentInst.Name\r\n                 + \",作动器控制 作动器运动停止 \"\r\n                 + \",运动方式：\"\r\n                 + ydfs\r\n                 + \",运动速度：\"\r\n                 + Model.GetVarByName<NumberInputVar>(\"input_LWCDJC_SD\").DisplayValue\r\n                 + Model.GetVarByName<NumberInputVar>(\"input_LWCDJC_SD\").DisplayUnitName   //单位\r\n                 + \",目标值:\"\r\n                 + Model.GetVarByName<NumberInputVar>(\"input_LWCDJC_MBZ\").DisplayValue\r\n                 + Model.GetVarByName<NumberInputVar>(\"input_LWCDJC_SD\").DisplayUnitName   //单位\r\n                 ;\r\n\r\nvar r = Model.RecordLog(significance, type, grade, content);\r\n\r\nConsole.WriteLine(\"记录控件日志是否成功：\" + r);\r\n\r\nModel.PubtaskStatus=false;\r\n\r\nreturn false;", :mode "无", :type "常量", :unitTypeCode "", :varType "Text", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "脚本执行子任务4c984b53d9"}, :nameShow "脚本执行子任务", :task-type :SubTaskEvalScript, :status :ready, :id "SubTaskEvalScript-8ac6111a-8d6d-4963-9383-34d7420e558a"} {:name "结束", :params nil, :task-type :end, :status :ready, :id "end-node-action"}], :ctx-atom #object[clojure.lang.Atom 0x2f694ab9 {:status :ready, :val [{:template-info {:project_id 27}, :name "开始", :params nil, :action-name "裂纹检查新版流程图", :task-type :start, :sample-code nil, :status :finished, :id "start-node-action", :class-name "project_27", :process-id "project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee"} {:name "条件-5.9", :params nil, :task-type :if-else, :expr "int flag = 0;\r\n\r\nint deviceId = 0;\r\nint ret = Model.station.Ccss_Connected(deviceId);\r\nif (ret == 0) {\r\n  /* 已连接 */ \r\n  flag++;\r\n} else { \r\n  /* 未连接 */ \r\n  Model.GetVarByName<TextInputVar>(\"input_yzlwcsts\").Value = \"控制器连接失败，请重新连接控制器\";\r\n  return false;\r\n}\r\nint ret1 = Model.station.Ccss_DriveReady(deviceId);\r\nif (ret1 == 0) { \r\n  /* 准备就绪 */ \r\n  flag++;\r\n} else {\r\n  /* 未就绪 */ \r\n  Model.GetVarByName<TextInputVar>(\"input_yzlwcsts\").Value = \"控制器未在启动状态\";\r\n  return false;\r\n}\r\nreturn true;", :true-tasks [{:name "脚本执行子任务", :params {:schedule {:control_input_script {:valueType "string", :variable "control_input_script", :isCheck false, :unit "", :value "string ydfs = \"负荷\";\r\nif(Model.GetVarByName<SelectInputVar>(\"input_LWCDJC_YDFS\").Value[2] == 0){\r\n  ydfs = \"位移\";\r\n}\r\nif(Model.GetVarByName<SelectInputVar>(\"input_LWCDJC_YDFS\").Value[2] == 1){\r\n  ydfs = \"负荷\";\r\n}\r\nif(Model.GetVarByName<SelectInputVar>(\"input_LWCDJC_YDFS\").Value[2] == 2){\r\n  ydfs = \"变形\";\r\n}\r\n\r\nstring significance = \"高\";\r\nstring type = \"信息\";\r\nstring grade = \"信息\";\r\nstring content = \"平面应变断裂韧度试验 裂纹长度检查操作试验运行 \"\r\n                 + Model.CurrentInst.Name\r\n                 + \",作动器控制 作动器运动开始 \"\r\n                 + \",运动方式：\"\r\n                 + ydfs\r\n                 + \",运动速度：\"\r\n                 + Model.GetVarByName<NumberInputVar>(\"input_LWCDJC_SD\").DisplayValue\r\n                 + Model.GetVarByName<NumberInputVar>(\"input_LWCDJC_SD\").DisplayUnitName   //单位\r\n                 + \",目标值:\"\r\n                 + Model.GetVarByName<NumberInputVar>(\"input_LWCDJC_MBZ\").DisplayValue\r\n                 + Model.GetVarByName<NumberInputVar>(\"input_LWCDJC_SD\").DisplayUnitName   //单位\r\n                 ;\r\n\r\nvar r = Model.RecordLog(significance, type, grade, content);\r\n\r\nConsole.WriteLine(\"记录控件日志是否成功：\" + r);\r\n\r\nreturn false;", :mode "无", :type "常量", :unitTypeCode "", :varType "Text", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "脚本执行子任务709fdee762"}, :nameShow "脚本执行子任务", :task-type :SubTaskEvalScript, :status :ready, :id "SubTaskEvalScript-cc94c47e-def9-4413-a314-5379c15e2989"}], :status :finished, :id "if-else-0c336739-59ca-414f-b5af-804dc945aeee", :false-tasks [{:name "对话框子任务", :params {:schedule {:control_input_ButtonType {:valueType "string", :variable "control_input_ButtonType", :isCheck false, :unit "", :value "OK", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_Script {:valueType "string", :variable "control_input_Script", :isCheck false, :unit "", :value "return Model.GetVarByName<TextInputVar>(\"input_yzlwcsts\").Value;", :mode "无", :type "常量", :unitTypeCode "", :varType "Text", :unitCode "", :unitType ""}, :control_input_BindVarCode {:valueType "string", :variable "control_input_BindVarCode", :isCheck false, :unit "", :value "input_one", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "对话框子任务b2f3569311"}, :nameShow "对话框子任务", :task-type :SubtaskDialogBox, :status :finished, :id "SubtaskDialogBox-c57a9ae0-38a2-40a2-a47c-a56ea9f9d378"}]} {:name "脚本执行子任务", :params {:schedule {:control_input_script {:valueType "string", :variable "control_input_script", :isCheck false, :unit "", :value "\n// 全局变量赋值，记录推送给二维数组集合曲线的上一次推送的index\nModel.iVariable[\"lastTail1\"]=0;\nModel.iVariable[\"lastTail2\"]=0;\nModel.iVariable[\"lastTail3\"]=0;\nModel.SignalVars[\"signal_time\"].ResetZero(Model);\n\n\n// 实时数据二维数组集合Model.GetVarByName<DoubleArrayListInputVar>(\"input_lwkz_curve_doubleArrayList\")\nvar doubleArrayList = Model.GetVarByName<DoubleArrayListInputVar>(\"input_lwkz_curve_doubleArrayList\");\n\n\n// 清空后端数据\ndoubleArrayList.ClearDoubleArrayListData();\n// 清空ui数据\ndoubleArrayList.SendClearDataCmdToUI();\n\n\n// 裂纹长度检查结果二维数组初始化\nvar LWCDJCJG_INPUT = Model.GetVarByName<DoubleArrayInputVar>(\"input_liewenchangdujieguo_doubleArray\");\n// 清空数据\nLWCDJCJG_INPUT.ClearDoubleArrayData();\n// 清空ui数据\nLWCDJCJG_INPUT.SendClearDataCmdToUI();\n\n// 初始化一些信息，方便后面的一些公式调用，这些参数先写死\nbyte sampleType = 0; \nint res1 = 0;\nif (Model.CurrentInst.SampleType == \"sampleType_jincou\"){\n   sampleType = 0; \n   res1 =\n      TestExpert.PlaneStrainFractureToughnessMethod.Commons\n\t.PlaneStrainFractureToughnessMethod.InitializeSpecimen(\n          Model.CurrentInst.Name,\n          sampleType,\n          (double)Model.CurrentInst.Parameters[\"sampleParam_width\"].Value,\n          (double)Model.CurrentInst.Parameters[\"sampleParam_thickness\"].Value,\n          (double)Model.CurrentInst.Parameters[\"sampleParam_notchLength\"].Value,\n          (double)Model.CurrentInst.Parameters[\"sampleParam_elasticModulus\"].Value,\n          (double)Model.CurrentInst.Parameters[\"sampleParam_yieldStrength\"].Value,\n          0\n      );\n}\nif (Model.CurrentInst.SampleType == \"sampleType_danbian\"){\n   sampleType = 1; \n   res1 =\n      TestExpert.PlaneStrainFractureToughnessMethod.Commons\n\t.PlaneStrainFractureToughnessMethod.InitializeSpecimen(\n          Model.CurrentInst.Name,\n          sampleType,\n          (double)Model.CurrentInst.Parameters[\"sampleParam_width\"].Value,\n          (double)Model.CurrentInst.Parameters[\"sampleParam_thickness\"].Value,\n          (double)Model.CurrentInst.Parameters[\"sampleParam_notchLength\"].Value,\n          (double)Model.CurrentInst.Parameters[\"sampleParam_elasticModulus\"].Value,\n          (double)Model.CurrentInst.Parameters[\"sampleParam_yieldStrength\"].Value,\n          (double)Model.CurrentInst.Parameters[\"sampleParam_span\"].Value\n      );\n}\n\nif(Model.CurrentInst.SampleType == \"sampleType_danbian\"){\n  var coefficientResult = TestExpert.PlaneStrainFractureToughnessMethod.Commons\n  .PlaneStrainFractureToughnessMethod.SetCoefficient(0.99975, -3.9504, 2.9821, -3.2141, 51.516, -113.03);\n}\nif(Model.CurrentInst.SampleType == \"sampleType_jincou\"){\n  if(Model.CurrentInst.Parameters[\"sampleParam_measurementPosition\"].Value == \"-0.345\"){ //vx1\n    var coefficientResult = TestExpert.PlaneStrainFractureToughnessMethod.Commons\n  \t.PlaneStrainFractureToughnessMethod.SetCoefficient(1.0012,-4.9165,23.057,-323.91, 1798.3, -3513.2);\n  }\n  if(Model.CurrentInst.Parameters[\"sampleParam_measurementPosition\"].Value == \"-0.25\"){ //v0\n    var coefficientResult = TestExpert.PlaneStrainFractureToughnessMethod.Commons\n  \t.PlaneStrainFractureToughnessMethod.SetCoefficient(1.001,-4.6695,18.46,-236.82,1214.9,-2143.6);\n  }\n  if(Model.CurrentInst.Parameters[\"sampleParam_measurementPosition\"].Value == \"-0.1576\"){ //v1\n    var coefficientResult = TestExpert.PlaneStrainFractureToughnessMethod.Commons\n  \t.PlaneStrainFractureToughnessMethod.SetCoefficient(1.0008,-4.4473,15.4,-180.55,870.92,-1411.3);\n  }\n  if(Model.CurrentInst.Parameters[\"sampleParam_measurementPosition\"].Value == \"0\"){ //vll\n    var coefficientResult = TestExpert.PlaneStrainFractureToughnessMethod.Commons\n  \t.PlaneStrainFractureToughnessMethod.SetCoefficient(1.0002,-4.0632,11.242,-106.04,464.33,-650.68);\n  }\n\n}\n\n\nModel.dVariable[\"lwjc_all_index\"] = -1.0; \n\nModel.dVariable[\"aaa\"] = 0.0; \n\nvar ss = Model.GetVarByName<DoubleArrayInputVar>(\"input_lwcdjc_ss\");\nss.UpdateSendMode(0);\nss.ClearDoubleArrayData();\nss.SendClearDataCmdToUI();\nss.SendValueToUI(Model.TemplateName);\n\nModel.PubtaskStatus=true;\n\nreturn false;", :mode "无", :type "常量", :unitTypeCode "", :varType "Text", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "脚本执行子任务4b2834d246"}, :nameShow "脚本执行子任务", :task-type :SubTaskEvalScript, :status :finished, :id "SubTaskEvalScript-d38715eb-7301-437d-b693-3e546d3997f9"} {:branch-idxs [0], :name "并行-1.3", :params nil, :branches [[{:name "组合波开始", :params {:schedule {:control_input_deviceid {:valueType "object[]", :variable "control_input_deviceid", :isCheck false, :unit "", :value ["Servo" 0], :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_cycle {:valueType "string", :variable "control_input_cycle", :isCheck false, :unit "", :value 1, :mode "无", :type "常量", :unitTypeCode "", :varType "Number", :unitCode "", :unitType nil}}, :edition "standard", :daq_code "组合波开始19e0b17d88"}, :nameShow "组合波开始", :task-type :combinedWaveStartControl, :status :finished, :id "combinedWaveStartControl-5da849dd-b2c1-47db-8def-f3243aae6634"} {:name "斜波子任务", :params {:schedule {:control_input_targetValue {:valueType "", :variable "control_input_targetValue", :isCheck false, :unit "", :value "input_LWCDJC_MBZ", :mode "无", :type "变量", :unitTypeCode "", :varType "Number", :unitCode "", :unitType ""}, :control_input_waitpos {:valueType "string", :variable "control_input_waitpos", :isCheck false, :unit "", :value false, :mode "无", :type "常量", :unitTypeCode "", :varType "Boolean", :unitCode "", :unitType ""}, :control_input_errorCode {:valueType "string", :variable "control_input_errorCode", :isCheck false, :unit "", :value "input_isXieBo", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_controlMode {:valueType "object[]", :variable "control_input_controlMode", :isCheck false, :unit "", :value ["Servo" 0 1], :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_speed {:valueType "", :variable "control_input_speed", :isCheck false, :unit "", :value "input_LWCDJC_SD", :mode "无", :type "变量", :unitTypeCode "", :varType "Number", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "斜波子任务e6ede7931f"}, :nameShow "斜波子任务", :task-type :xiebo, :status :finished, :id "xiebo-7a9b46e2-ce29-4ac0-a55d-be7b124e697d"} {:name "斜波子任务", :params {:schedule {:control_input_targetValue {:valueType "", :variable "control_input_targetValue", :isCheck false, :unit "", :value "input_LWCUJC_QSZ", :mode "无", :type "变量", :unitTypeCode "", :varType "Number", :unitCode "", :unitType ""}, :control_input_waitpos {:valueType "string", :variable "control_input_waitpos", :isCheck false, :unit "", :value false, :mode "无", :type "常量", :unitTypeCode "", :varType "Boolean", :unitCode "", :unitType ""}, :control_input_errorCode {:valueType "string", :variable "control_input_errorCode", :isCheck false, :unit "", :value "input_isXieBo", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_controlMode {:valueType "object[]", :variable "control_input_controlMode", :isCheck false, :unit "", :value ["Servo" 0 1], :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_speed {:valueType "", :variable "control_input_speed", :isCheck false, :unit "", :value "input_LWCDJC_SD", :mode "无", :type "变量", :unitTypeCode "", :varType "Number", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "斜波子任务e146d2e9e8"}, :nameShow "斜波子任务", :task-type :xiebo, :status :finished, :id "xiebo-ec32daaf-a89d-4394-9719-b18e311efb7a"} {:name "斜波子任务", :params {:schedule {:control_input_targetValue {:valueType "", :variable "control_input_targetValue", :isCheck false, :unit "", :value "input_LWCDJC_MBZ", :mode "无", :type "变量", :unitTypeCode "", :varType "Number", :unitCode "", :unitType ""}, :control_input_waitpos {:valueType "string", :variable "control_input_waitpos", :isCheck false, :unit "", :value false, :mode "无", :type "常量", :unitTypeCode "", :varType "Boolean", :unitCode "", :unitType ""}, :control_input_errorCode {:valueType "string", :variable "control_input_errorCode", :isCheck false, :unit "", :value "input_isXieBo", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_controlMode {:valueType "object[]", :variable "control_input_controlMode", :isCheck false, :unit "", :value ["Servo" 0 1], :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_speed {:valueType "", :variable "control_input_speed", :isCheck false, :unit "", :value "input_LWCDJC_SD", :mode "无", :type "变量", :unitTypeCode "", :varType "Number", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "斜波子任务aa8a83b7fd"}, :nameShow "斜波子任务", :task-type :xiebo, :status :finished, :id "xiebo-c39d0131-3fdb-46d8-af94-0668968e4756"} {:name "斜波子任务", :params {:schedule {:control_input_targetValue {:valueType "", :variable "control_input_targetValue", :isCheck false, :unit "", :value "input_LWCUJC_QSZ", :mode "无", :type "变量", :unitTypeCode "", :varType "Number", :unitCode "", :unitType ""}, :control_input_waitpos {:valueType "string", :variable "control_input_waitpos", :isCheck false, :unit "", :value false, :mode "无", :type "常量", :unitTypeCode "", :varType "Boolean", :unitCode "", :unitType ""}, :control_input_errorCode {:valueType "string", :variable "control_input_errorCode", :isCheck false, :unit "", :value "input_isXieBo", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_controlMode {:valueType "object[]", :variable "control_input_controlMode", :isCheck false, :unit "", :value ["Servo" 0 1], :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_speed {:valueType "", :variable "control_input_speed", :isCheck false, :unit "", :value "input_LWCDJC_SD", :mode "无", :type "变量", :unitTypeCode "", :varType "Number", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "斜波子任务8517da3675"}, :nameShow "斜波子任务", :task-type :xiebo, :status :finished, :id "xiebo-b7edfcb1-c345-463c-aacd-c8d2eb206635"} {:name "斜波子任务", :params {:schedule {:control_input_targetValue {:valueType "", :variable "control_input_targetValue", :isCheck false, :unit "", :value "input_LWCDJC_MBZ", :mode "无", :type "变量", :unitTypeCode "", :varType "Number", :unitCode "", :unitType ""}, :control_input_waitpos {:valueType "string", :variable "control_input_waitpos", :isCheck false, :unit "", :value false, :mode "无", :type "常量", :unitTypeCode "", :varType "Boolean", :unitCode "", :unitType ""}, :control_input_errorCode {:valueType "string", :variable "control_input_errorCode", :isCheck false, :unit "", :value "input_isXieBo", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_controlMode {:valueType "object[]", :variable "control_input_controlMode", :isCheck false, :unit "", :value ["Servo" 0 1], :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_speed {:valueType "", :variable "control_input_speed", :isCheck false, :unit "", :value "input_LWCDJC_SD", :mode "无", :type "变量", :unitTypeCode "", :varType "Number", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "斜波子任务541540d663"}, :nameShow "斜波子任务", :task-type :xiebo, :status :finished, :id "xiebo-333a9f87-cae8-4b89-ab58-dd716d819b58"} {:name "斜波子任务", :params {:schedule {:control_input_targetValue {:valueType "", :variable "control_input_targetValue", :isCheck false, :unit "", :value "input_LWCUJC_QSZ", :mode "无", :type "变量", :unitTypeCode "", :varType "Number", :unitCode "", :unitType ""}, :control_input_waitpos {:valueType "string", :variable "control_input_waitpos", :isCheck false, :unit "", :value false, :mode "无", :type "常量", :unitTypeCode "", :varType "Boolean", :unitCode "", :unitType ""}, :control_input_errorCode {:valueType "string", :variable "control_input_errorCode", :isCheck false, :unit "", :value "input_isXieBo", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_controlMode {:valueType "object[]", :variable "control_input_controlMode", :isCheck false, :unit "", :value ["Servo" 0 1], :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_speed {:valueType "", :variable "control_input_speed", :isCheck false, :unit "", :value "input_LWCDJC_SD", :mode "无", :type "变量", :unitTypeCode "", :varType "Number", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "斜波子任务c4f736223b"}, :nameShow "斜波子任务", :task-type :xiebo, :status :finished, :id "xiebo-3756a5ec-49f2-4c21-a287-b92e938f5c45"} {:branch-idxs [0], :name "并行-8.7", :params nil, :branches [[{:name "组合波结束", :params {:schedule {:control_input_deviceid {:valueType "object[]", :variable "control_input_deviceid", :isCheck false, :unit "", :value ["Servo" 0], :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "组合波结束ca84fca62a"}, :nameShow "组合波结束", :task-type :combinedWaveEndControl, :status :running, :id "combinedWaveEndControl-07f9477f-ddab-4730-ac75-81ca40937ab5"} {:name "等待某个事件", :params {:schedule {:control_input_interval {:valueType "string", :variable "control_input_interval", :isCheck false, :unit "", :value "50", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_script {:valueType "", :variable "control_input_script", :isCheck false, :unit "", :value "var doubleArray = Model.GetVarByName<DoubleArrayInputVar>(\"input_lwcdjc_ss\");\nint index = doubleArray.GetDoubleArrayRowCount()-1;\n//当二维数组记录的负荷与起始值的差的绝对值<0.01时结束记录\nint CtrlMode = (int)Model.GetVarByName<SelectInputVar>(\"input_LWCDJC_YDFS\").Value[2];\ndouble offset = 0;\nif(CtrlMode == 1){\n  offset = Math.Abs((double)doubleArray.GetCellValue(index,\"load\") - Model.GetVarByName<NumberInputVar>(\"input_LWCUJC_QSZ\").Value);\n}else if(CtrlMode == 0){\n  offset = Math.Abs((double)doubleArray.GetCellValue(index,\"pos\") - Model.GetVarByName<NumberInputVar>(\"input_LWCUJC_QSZ\").Value);\n}else if(CtrlMode == 2){\n  offset = Math.Abs((double)doubleArray.GetCellValue(index,\"ext\") - Model.GetVarByName<NumberInputVar>(\"input_LWCUJC_QSZ\").Value);\n}\nif (offset < 0.01){\n  return true;\n}\n return false;", :mode "无", :type "常量", :unitTypeCode "", :varType "Text", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "等待某个事件2c19cb9002"}, :nameShow "等待某个事件", :task-type :waitEvent, :status :ready, :id "waitEvent-00671a03-a007-4507-85a2-e7ad8bcd2c4c"}] [{:name "DAQ", :params {:schedule {:control_input_move_interval {:valueType "string", :variable "control_input_move_interval", :isCheck false, :unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :value 0, :mode "signal_load", :type "常量", :unitTypeCode "dimension_Force", :varType "Number", :unitCode "unit_cdw13c2640", :axisType "signal_load", :unitType "56019f36-d734-40c0-8872-73b60304f80a"}, :control_input_bufferMode {:valueType "string", :variable "control_input_bufferMode", :isCheck false, :unit "", :value "RESTART", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_timeResetZero {:valueType "string", :variable "control_input_timeResetZero", :isCheck false, :unit "", :value "", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_bufferCode {:valueType "string", :variable "control_input_bufferCode", :isCheck false, :unit "", :value "input_LWCDJC_ALL_DAQ", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_keepStream {:valueType "string", :variable "control_input_keepStream", :isCheck false, :unit "", :value false, :mode "无", :type "常量", :unitTypeCode "", :varType "Boolean", :unitCode "", :unitType ""}, :control_input_load_interval {:valueType "string", :variable "control_input_load_interval", :isCheck false, :unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :value 0, :mode "signal_load", :type "常量", :unitTypeCode "dimension_Force", :varType "Number", :unitCode "unit_cdw13c2640", :axisType "signal_load", :unitType "56019f36-d734-40c0-8872-73b60304f80a"}, :control_input_saveDB {:valueType "", :variable "control_input_saveDB", :isCheck true, :unit "", :value true, :mode "无", :type "常量", :unitTypeCode "", :varType "Boolean", :unitCode "", :unitType ""}, :control_input_strain_interval {:valueType "string", :variable "control_input_strain_interval", :isCheck false, :unit "e655c1f0-26c0-41aa-9d1d-25c94c059d2e", :value 0, :mode "signal_ext", :type "常量", :unitTypeCode "dimension_Displacement", :varType "Number", :unitCode "unit_cdw4b9c1d4", :axisType "signal_pos", :unitType "6420a172-7a93-45c6-a8da-7ccb275a1aad"}, :control_input_time_interval {:valueType "string", :variable "control_input_time_interval", :isCheck true, :unit "f9b2c0be-a829-4072-878e-f5f592308f79", :value 0.001, :mode "signal_time", :type "常量", :unitTypeCode "dimension_Time", :varType "Number", :unitCode "unit_cdw3860fb2", :axisType "signal_time", :unitType "54891d75-4375-4253-8a5b-068423501a0a"}}, :edition "standard", :daq_code "DAQf413946b3d"}, :nameShow "DAQ", :task-type :daq, :status :running, :id "daq-a47ba0de-3727-4ea7-a523-2fd2fbd8fb7b"}] [{:name "等待某个事件", :params {:schedule {:control_input_interval {:valueType "string", :variable "control_input_interval", :isCheck false, :unit "", :value "500", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_script {:valueType "", :variable "control_input_script", :isCheck false, :unit "", :value "try{\r\n  // FuncLibs.Logger.Error(\"裂纹长度检查实时曲线-1\");\r\n  // 获取二维数组变量\r\n  var doubleArrayInput = Model.GetVarByName<DoubleArrayInputVar>(\"input_lwcdjc_ss\");\r\n  // 获取周期产生相关的Buffer输入变量\r\n  var periodicBufferVar = Model.GetVarByName<BufferInputVar>(\"input_LWCDJC_ALL_DAQ\");\r\n  \r\n  // --- 设置二维数组发送模式为增量 ---\r\n  doubleArrayInput.UpdateSendMode(1); // 0: 全量, 1: 增量\r\n  \r\n  // --- 管理上次成功添加到二维数组的周期号 ---\r\n  // 使用Model.dVariable来持久化上次处理的最后一个周期号\r\n  string lastProcessedCycleKey = \"lwjc_all_index\"; // 确保key的唯一性\r\n  double lastSuccessfullyAddedCycleToDoubleArray = -1; // 初始化为-1, 表示还没有处理过任何周期\r\n  \r\n  if (Model.dVariable.ContainsKey(lastProcessedCycleKey))\r\n  {\r\n      lastSuccessfullyAddedCycleToDoubleArray = Model.dVariable[lastProcessedCycleKey];\r\n  }\r\n  else\r\n  {\r\n      Model.dVariable[lastProcessedCycleKey] = -1.0; // 首次运行时初始化为-1.0 (as double)\r\n      // 注意：如果这是第一次切换到增量模式，并且希望doubleArrayInput从空开始，\r\n      // 你可能需要在这里或通过外部逻辑调用一次 doubleArrayInput.ClearDoubleArrayData();\r\n      // doubleArrayInput.ClearDoubleArrayData(); // 如果需要在首次增量运行时清空，取消此行注释\r\n  }\r\n  \r\n  // --- 从Buffer获取所需数据列 ---\r\n  // 关键的周期号数据\r\n  var extBufferData = periodicBufferVar.Value[\"signal_ext\"].GetBuffer();\r\n  var timeBufferData = periodicBufferVar.Value[\"signal_time\"].GetBuffer();\r\n  var posBufferData = periodicBufferVar.Value[\"signal_pos\"].GetBuffer();\r\n  var loadBufferData = periodicBufferVar.Value[\"signal_load\"].GetBuffer();\r\n  var mingLing = periodicBufferVar.Value[\"signal_command\"].GetBuffer();\r\n  \r\n  // FuncLibs.Logger.Error(\"裂纹长度检查实时曲线-2\");\r\n  \r\n  // 如果时间数据Buffer为空，则不进行后续处理\r\n  if (timeBufferData.Length == 0)\r\n  {\r\n      FuncLibs.Logger.Info(\"裂纹长度检查实时曲线 - 'signal_time' Buffer为空，跳过处理。\");\r\n      return false;\r\n  }\r\n  \r\n  // 计算所有相关Buffer的最小长度，以确保安全访问\r\n  int minBufferLength = timeBufferData.Length;\r\n  minBufferLength = Math.Min(minBufferLength, posBufferData.Length);\r\n  minBufferLength = Math.Min(minBufferLength, loadBufferData.Length);\r\n  minBufferLength = Math.Min(minBufferLength, extBufferData.Length);\r\n  minBufferLength = Math.Min(minBufferLength, mingLing.Length);\r\n  \r\n  if (minBufferLength == 0)\r\n  {\r\n      FuncLibs.Logger.Info(\"裂纹长度检查实时曲线 - 一个或多个数据Buffer有效长度为0，跳过处理。\");\r\n      return false;\r\n  }\r\n  \r\n  int newRowsAdded = 0;\r\n  double maxProcessedTimeInCurrentRun = lastSuccessfullyAddedCycleToDoubleArray;\r\n  \r\n  // 遍历数据点，使用minBufferLength作为安全上限\r\n  for (int i = 0; i < minBufferLength; i++)\r\n  {\r\n      // 只处理那些周期号大于上次已成功添加到二维数组的周期号的数据点\r\n      if (timeBufferData[i] > lastSuccessfullyAddedCycleToDoubleArray)\r\n      {\r\n        if(timeBufferData[i]!=0){ // 原始条件保留\r\n          // 创建要添加到二维数组的行数据\r\n          Dictionary<string, object> rowData = new()\r\n          {\r\n              { \"time\", timeBufferData[i] },\r\n              { \"pos\", posBufferData[i] },\r\n              { \"load\", loadBufferData[i] },\r\n              { \"ext\", extBufferData[i] },\r\n              { \"ml\", mingLing[i] }\r\n          };\r\n          // 向二维数组添加行数据\r\n          doubleArrayInput.AddRowData(rowData);\r\n          newRowsAdded++;\r\n          if (timeBufferData[i] > maxProcessedTimeInCurrentRun)\r\n          {\r\n              maxProcessedTimeInCurrentRun = timeBufferData[i];\r\n          }\r\n        }\r\n      }\r\n  }\r\n  // FuncLibs.Logger.Error(\"裂纹长度检查实时曲线-3\");\r\n  \r\n  // 如果有新数据加入到doubleArrayInput，则更新持久化的\"上次成功添加的周期号\"并通知UI\r\n  if (newRowsAdded > 0)\r\n  {\r\n      Model.dVariable[lastProcessedCycleKey] = maxProcessedTimeInCurrentRun; // 使用循环中实际处理的最大时间戳\r\n      doubleArrayInput.SendValueToUI(Model.TemplateName);\r\n  }\r\n  else\r\n  {\r\n      FuncLibs.Logger.Error(\"裂纹长度检查实时曲线-未更新数据\");\r\n  }\r\n  // FuncLibs.Logger.Error(\"裂纹长度检查实时曲线-4\");\r\n} catch(Exception ex){\r\n  FuncLibs.Logger.Error($\"ex.Message: {ex.Message}\");\r\n  FuncLibs.Logger.Error($\"ex.StackTrace: {ex.StackTrace}\");\r\n  System.Diagnostics.StackTrace stackTrace = new System.Diagnostics.StackTrace(ex, true);\r\n  System.Diagnostics.StackFrame frame = stackTrace.GetFrame(0);\r\n  FuncLibs.Logger.Error($\"错误发生在文件: {frame.GetFileName()}, 行号: {frame.GetFileLineNumber()}\");\r\n}\r\n\r\n\r\nreturn false;", :mode "无", :type "常量", :unitTypeCode "", :varType "Text", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "等待某个事件26c0083cbf"}, :nameShow "等待某个事件", :task-type :waitEvent, :status :running, :id "waitEvent-93759e23-f394-4cb3-9acb-381a8c9be348"}]], :task-type :parallels, :status :running, :id "parallels-2b5d2fc6-d916-46ae-8155-5fba6aade7fe"}] [{:name "条件-4.4", :params nil, :task-type :if-else, :expr "var blockline = Model.dVariable[\"blockline\"];\nif(blockline==1){\n  return true;\n}\nreturn false;", :true-tasks [], :status :finished, :id "if-else-d9b9633e-d94c-4fbd-8f35-4969fb92685d", :false-tasks [{:name "信号变量检测", :params {:schedule {:control_input_bufferCode {:valueType "string", :variable "control_input_bufferCode", :isCheck false, :unit "", :value "input_LWCDJC_ALL_DAQ", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_condition {:valueType "string", :variable "control_input_condition", :isCheck false, :unit "", :value "1", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_signalCode {:valueType "string", :variable "control_input_signalCode", :isCheck false, :unit "", :value "signal_blockline", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "信号变量检测4533546a79"}, :nameShow "信号变量检测", :task-type :creepSignalCheck, :status :ready, :id "creepSignalCheck-9a1c7b0d-1760-4d5a-90d7-af26123789a5"}]} {:name "等待某个事件", :params {:schedule {:control_input_interval {:valueType "string", :variable "control_input_interval", :isCheck false, :unit "", :value "100", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_script {:valueType "", :variable "control_input_script", :isCheck false, :unit "", :value "try{\n  var doubleArrayList = Model.GetVarByName<DoubleArrayListInputVar>(\"input_lwkz_curve_doubleArrayList\");\n  doubleArrayList.UpdateDoubleArrayListSendMode(1);\n  \n  int  lastTail = Model.iVariable[\"lastTail1\"];\n  var buffer = Model.GetVarByName<BufferInputVar>(\"input_LWCDJC_ALL_DAQ\");\n  var bufferBlockline = buffer.Value[\"signal_blockline\"].GetBuffer();\n  var valueBlockline = buffer.Value[\"signal_blockline\"].GetValue();\n  \n  var index = bufferBlockline.Length - 1;\n  if (index >= 0)\n  {\n  \tfor (int i = lastTail; i < (int)index; i++)\n  \t{\n      if (bufferBlockline[i]==1 || bufferBlockline[i]==2) {\t\t\n        var loadBufferdata =buffer.Value[\"signal_load\"].GetBuffer()[i];\n    \t\tvar extBufferdata = buffer.Value[\"signal_ext\"].GetBuffer()[i];\n    \n    \t\tDictionary<string, object> row = new()\n    \t\t{\n    \t\t\t{ \"col_x\", extBufferdata },\n    \t\t\t{ \"col_y\", loadBufferdata }\n    \t\t};\n    \t\tdoubleArrayList.AddDoubleArrayListData(0, row);\n      }\n  \t}\n  \tModel.iVariable[\"lastTail1\"] = index;\n  }\n  doubleArrayList.SendValueToUI(Model.TemplateName);\n  \n  if(valueBlockline==3){\n    return true;\n  }\n} catch (Exception e)\n{\n  FuncLibs.Logger.Error($\"裂纹长度检查，等待某个事件子任务，waitEvent-54a9826e-7e5e-4b46-a94a-3868836dcd6f脚本报错信息：{e.ToString()} , {e.StackTrace}\");\n}\n\nreturn false;", :mode "无", :type "常量", :unitTypeCode "", :varType "Text", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "等待某个事件7d2400876a"}, :nameShow "等待某个事件(负责更新曲线)", :task-type :waitEvent, :status :running, :id "waitEvent-54a9826e-7e5e-4b46-a94a-3868836dcd6f"} {:name "等待某个事件", :params {:schedule {:control_input_interval {:valueType "string", :variable "control_input_interval", :isCheck false, :unit "", :value "100", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_script {:valueType "", :variable "control_input_script", :isCheck false, :unit "", :value "\r\n\r\nvar doubleArrayList = Model.GetVarByName<DoubleArrayListInputVar>(\"input_lwkz_curve_doubleArrayList\");\r\ndoubleArrayList.UpdateDoubleArrayListSendMode(1);\r\n\r\nint  lastTail = Model.iVariable[\"lastTail2\"];\r\nvar buffer = Model.GetVarByName<BufferInputVar>(\"input_LWCDJC_ALL_DAQ\");\r\nvar bufferBlockline = buffer.Value[\"signal_blockline\"].GetBuffer();\r\nvar valueBlockline = buffer.Value[\"signal_blockline\"].GetValue();\r\n\r\nvar index = bufferBlockline.Length - 1;\r\nif (index >= 0)\r\n{\r\n\tfor (int i = lastTail; i < (int)index; i++)\r\n\t{\r\n    if (bufferBlockline[i]==3 || bufferBlockline[i]==4) {\t\t\r\n      var loadBufferdata =buffer.Value[\"signal_load\"].GetBuffer()[i];\r\n  \t\tvar extBufferdata = buffer.Value[\"signal_ext\"].GetBuffer()[i];\r\n  \r\n  \t\tDictionary<string, object> row = new()\r\n  \t\t{\r\n  \t\t\t{ \"col_x\", extBufferdata },\r\n  \t\t\t{ \"col_y\", loadBufferdata }\r\n  \t\t};\r\n  \t\tdoubleArrayList.AddDoubleArrayListData(1, row);\r\n    }\r\n\t}\r\n\tModel.iVariable[\"lastTail2\"] = index;\r\n}\r\ndoubleArrayList.SendValueToUI(Model.TemplateName);\r\n\r\nif(valueBlockline==5){\r\n  return true;\r\n}\r\nreturn false;", :mode "无", :type "常量", :unitTypeCode "", :varType "Text", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "等待某个事件e1b4d85b0d"}, :nameShow "等待某个事件", :task-type :waitEvent, :status :ready, :id "waitEvent-ffdc7080-10a2-478e-9add-ad7db595b4ee"} {:name "等待某个事件", :params {:schedule {:control_input_interval {:valueType "string", :variable "control_input_interval", :isCheck false, :unit "", :value "100", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_script {:valueType "", :variable "control_input_script", :isCheck false, :unit "", :value "var doubleArrayList = Model.GetVarByName<DoubleArrayListInputVar>(\"input_lwkz_curve_doubleArrayList\");\r\ndoubleArrayList.UpdateDoubleArrayListSendMode(1);\r\n\r\nint  lastTail = Model.iVariable[\"lastTail3\"];\r\nvar buffer = Model.GetVarByName<BufferInputVar>(\"input_LWCDJC_ALL_DAQ\");\r\nvar bufferBlockline = buffer.Value[\"signal_blockline\"].GetBuffer();\r\nvar valueBlockline = buffer.Value[\"signal_blockline\"].GetValue();\r\n\r\nvar index = bufferBlockline.Length - 1;\r\nif (index >= 0)\r\n{\r\n\tfor (int i = lastTail; i < (int)index; i++)\r\n\t{\r\n    if (bufferBlockline[i]==5 || bufferBlockline[i]==6) {\t\t\r\n      var loadBufferdata =buffer.Value[\"signal_load\"].GetBuffer()[i];\r\n  \t\tvar extBufferdata = buffer.Value[\"signal_ext\"].GetBuffer()[i];\r\n  \r\n  \t\tDictionary<string, object> row = new()\r\n  \t\t{\r\n  \t\t\t{ \"col_x\", extBufferdata },\r\n  \t\t\t{ \"col_y\", loadBufferdata }\r\n  \t\t};\r\n  \t\tdoubleArrayList.AddDoubleArrayListData(2, row);\r\n    }\r\n\t}\r\n\tModel.iVariable[\"lastTail3\"] = index;\r\n}\r\ndoubleArrayList.SendValueToUI(Model.TemplateName);\r\n\r\nreturn false;", :mode "无", :type "常量", :unitTypeCode "", :varType "Text", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "等待某个事件961148a40c"}, :nameShow "等待某个事件", :task-type :waitEvent, :status :ready, :id "waitEvent-3989f720-9718-4165-ae9b-bb1c936ce43b"}] [{:name "等待某个事件", :params {:schedule {:control_input_interval {:valueType "string", :variable "control_input_interval", :isCheck false, :unit "", :value "50", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_script {:valueType "", :variable "control_input_script", :isCheck false, :unit "", :value "try{\n  var blockline = Model.GetVarByName<BufferInputVar>(\"input_LWCDJC_ALL_DAQ\").Value[\"signal_blockline\"].GetValue();\n  if(blockline==1){\n    return true;\n  }\n} catch (Exception e){\n  FuncLibs.Logger.Error($\"裂纹长度检查，等待某个事件子任务，blockline脚本报错信息：{e.ToString()} , {e.StackTrace}\");\n}\n\nreturn false;", :mode "无", :type "常量", :unitTypeCode "", :varType "Text", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "等待某个事件f47e394a04"}, :nameShow "等待某个事件", :task-type :waitEvent, :status :running, :id "waitEvent-426ba70c-a598-491b-ae16-d5a27715763f"} {:branch-idxs [0], :name "并行-3.2", :params nil, :branches [[{:name "信号变量检测", :params {:schedule {:control_input_bufferCode {:valueType "string", :variable "control_input_bufferCode", :isCheck false, :unit "", :value "input_LWCDJC_ALL_DAQ", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_condition {:valueType "string", :variable "control_input_condition", :isCheck false, :unit "", :value "1", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_signalCode {:valueType "string", :variable "control_input_signalCode", :isCheck false, :unit "", :value "signal_blockline", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "信号变量检测b810c74e20"}, :nameShow "信号变量检测", :task-type :creepSignalCheck, :status :ready, :id "creepSignalCheck-8d313bcb-d7cc-424e-bb19-d7d82337452b"}] [{:name "DAQ", :params {:schedule {:control_input_move_interval {:valueType "string", :variable "control_input_move_interval", :isCheck false, :unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :value 0, :mode "signal_load", :type "常量", :unitTypeCode "dimension_Force", :varType "Number", :unitCode "unit_cdw13c2640", :axisType "signal_load", :unitType "56019f36-d734-40c0-8872-73b60304f80a"}, :control_input_bufferMode {:valueType "string", :variable "control_input_bufferMode", :isCheck false, :unit "", :value "RESTART", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_timeResetZero {:valueType "string", :variable "control_input_timeResetZero", :isCheck false, :unit "", :value "", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_bufferCode {:valueType "string", :variable "control_input_bufferCode", :isCheck false, :unit "", :value "input_lwjc_no1_down", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_keepStream {:valueType "string", :variable "control_input_keepStream", :isCheck false, :unit "", :value false, :mode "无", :type "常量", :unitTypeCode "", :varType "Boolean", :unitCode "", :unitType ""}, :control_input_load_interval {:valueType "string", :variable "control_input_load_interval", :isCheck false, :unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :value 0, :mode "signal_load", :type "常量", :unitTypeCode "dimension_Force", :varType "Number", :unitCode "unit_cdw13c2640", :axisType "signal_load", :unitType "56019f36-d734-40c0-8872-73b60304f80a"}, :control_input_saveDB {:valueType "", :variable "control_input_saveDB", :isCheck true, :unit "", :value true, :mode "无", :type "常量", :unitTypeCode "", :varType "Boolean", :unitCode "", :unitType ""}, :control_input_strain_interval {:valueType "string", :variable "control_input_strain_interval", :isCheck false, :unit "e655c1f0-26c0-41aa-9d1d-25c94c059d2e", :value 0, :mode "signal_ext", :type "常量", :unitTypeCode "dimension_Displacement", :varType "Number", :unitCode "unit_cdw4b9c1d4", :axisType "signal_pos", :unitType "6420a172-7a93-45c6-a8da-7ccb275a1aad"}, :control_input_time_interval {:valueType "string", :variable "control_input_time_interval", :isCheck true, :unit "f9b2c0be-a829-4072-878e-f5f592308f79", :value 0.001, :mode "signal_time", :type "常量", :unitTypeCode "dimension_Time", :varType "Number", :unitCode "unit_cdw3860fb2", :axisType "signal_time", :unitType "54891d75-4375-4253-8a5b-068423501a0a"}}, :edition "standard", :daq_code "DAQ2e9ff75f34"}, :nameShow "DAQ", :task-type :daq, :status :ready, :id "daq-0ac09652-82d1-4130-a028-2e081713c233"}]], :task-type :parallels, :status :ready, :id "parallels-065884ba-5163-40ad-80d3-d98a12352be8"} {:name "脚本执行子任务", :params {:schedule {:control_input_script {:valueType "string", :variable "control_input_script", :isCheck false, :unit "", :value "\n  // 计算第一段的值\n\n// 裂纹长度检查斜波下降段daq\nvar LWCDJC_DOWN_DAQ = Model.GetVarByName<BufferInputVar>(\"input_lwjc_no1_down\");\n\n// 客户填写的两个输入变量\nvar cankaotanxingmuliang = Model.GetVarByName<NumberInputVar>(\"input_LWCDJC_CKTXML\").Value;\nvar shijiliewenchangdu = Model.GetVarByName<NumberInputVar>(\"input_LWCDJC_SJCD\").Value;\n\n\n// 获取下降段的负荷和变形数据\n\nvar loadBufferdata = LWCDJC_DOWN_DAQ[\"signal_load\"];\nvar extBufferdata = LWCDJC_DOWN_DAQ[\"signal_ext\"];\nint length=loadBufferdata.Length;\nif(length == 0){\n  FuncLibs.Logger.Error(\"裂纹长度检查未采集到数!\");\n  return false;\n}\nfor (int i = 0; i < length; i++)\n{\n    loadBufferdata[i] = Math.Abs(loadBufferdata[i]);\n}\nFuncLibs.Logger.Error(\"裂纹检查第一个下降段的负荷数组长度:\"+length);\n// foreach(var item in loadBufferdata){\n//   FuncLibs.Logger.Error(item + \",\");\n// }\nFuncLibs.Logger.Error(\"裂纹检查第一个下降段的变形数组长度:\"+length);\n// foreach(var item in extBufferdata){\n//   FuncLibs.Logger.Error(item + \",\");\n// }\n\n  FuncLibs.Logger.Error(\"=== 第一段GetCrackLength函数参数打印 ===\");\n  FuncLibs.Logger.Error(\"参数1 - extBufferdata数组长度: \" + extBufferdata.Length);\n  FuncLibs.Logger.Error(\"参数1 - extBufferdata数组内容: [\" + string.Join(\", \", extBufferdata) + \"]\");\n  FuncLibs.Logger.Error(\"参数2 - loadBufferdata数组长度: \" + loadBufferdata.Length);\n  FuncLibs.Logger.Error(\"参数2 - loadBufferdata数组内容: [\" + string.Join(\", \", loadBufferdata) + \"]\");\n  FuncLibs.Logger.Error(\"参数3 - length: \" + length);\n  FuncLibs.Logger.Error(\"=== 参数打印完毕，开始调用函数 ===\");\n//  调用公式 计算柔度和实际裂纹长度\nvar res= TestExpert.PlaneStrainFractureToughnessMethod.Commons\n.PlaneStrainFractureToughnessMethod.GetCrackLength(extBufferdata, loadBufferdata, length,out double crackLength, out double slope);\n\n// 计算弹性模量\nvar res2 = TestExpert.PlaneStrainFractureToughnessMethod.Commons\n.PlaneStrainFractureToughnessMethod.ModulusFromCrackLength( slope, cankaotanxingmuliang,  shijiliewenchangdu, out double modulus);\n\nFuncLibs.Logger.Error(\"裂纹检查第一个下降段的弹性模量:\"+modulus);\nFuncLibs.Logger.Error(\"裂纹检查第一个下降段参考弹性模量:\"+cankaotanxingmuliang);\nFuncLibs.Logger.Error(\"裂纹检查第一个下降段的计算裂纹长度:\"+shijiliewenchangdu);\nFuncLibs.Logger.Error(\"裂纹检查第一个下降段的计算cres:\"+res);\nFuncLibs.Logger.Error(\"裂纹检查第一个下降段的参考裂纹长度:\"+crackLength);\nFuncLibs.Logger.Error(\"裂纹检查第一个下降段的卸载段柔度:\"+slope);\n\n// 裂纹长度检查结果二维数组\nvar LWCDJCJG_INPUT = Model.GetVarByName<DoubleArrayInputVar>(\"input_liewenchangdujieguo_doubleArray\");\n// 设置发送模式 0全量 1增量\nLWCDJCJG_INPUT.UpdateSendMode(1);\n\nDictionary<string, object> row = new()\n        {\n          { \"LW_JCJS\", 1.0 },\n          { \"LW_XZDRD\", Math.Round(slope,6) },\n          { \"LW_CKTXML\", Math.Round(cankaotanxingmuliang,2) },\n          { \"LW_JSTXML\", Math.Round(modulus,2) },\n          { \"LW_CKLWCD\", Math.Round(shijiliewenchangdu,2) },\n          { \"LW_JSLWCD\", Math.Round(crackLength,2) }\n        };\n\n// 添加数据\nLWCDJCJG_INPUT.AddRowData(row);\n// 通知ui\nLWCDJCJG_INPUT.SendValueToUI(Model.TemplateName);\n\n\n\n\nreturn false;", :mode "无", :type "常量", :unitTypeCode "", :varType "Text", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "脚本执行子任务4e1bea2973"}, :nameShow "脚本执行子任务", :task-type :SubTaskEvalScript, :status :ready, :id "SubTaskEvalScript-2d644e51-aac7-477a-99b7-7f3efe700e51"} {:name "等待某个事件", :params {:schedule {:control_input_interval {:valueType "string", :variable "control_input_interval", :isCheck false, :unit "", :value "50", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_script {:valueType "", :variable "control_input_script", :isCheck false, :unit "", :value "var blockline = Model.GetVarByName<BufferInputVar>(\"input_LWCDJC_ALL_DAQ\").Value[\"signal_blockline\"].GetValue();\nif(blockline==3){\n  return true;\n}\nreturn false;", :mode "无", :type "常量", :unitTypeCode "", :varType "Text", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "等待某个事件9d8f82ffd4"}, :nameShow "等待某个事件", :task-type :waitEvent, :status :ready, :id "waitEvent-5cdadc8f-47b5-4639-a8b1-d26cbf98093f"} {:branch-idxs [0], :name "并行-9.6", :params nil, :branches [[{:name "信号变量检测", :params {:schedule {:control_input_bufferCode {:valueType "string", :variable "control_input_bufferCode", :isCheck false, :unit "", :value "input_LWCDJC_ALL_DAQ", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_condition {:valueType "string", :variable "control_input_condition", :isCheck false, :unit "", :value "1", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_signalCode {:valueType "string", :variable "control_input_signalCode", :isCheck false, :unit "", :value "signal_blockline", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "信号变量检测0e5548505b"}, :nameShow "信号变量检测", :task-type :creepSignalCheck, :status :ready, :id "creepSignalCheck-71a89ec2-eaf7-4ba4-aa56-76c5a8ea8f69"}] [{:name "DAQ", :params {:schedule {:control_input_move_interval {:valueType "string", :variable "control_input_move_interval", :isCheck false, :unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :value 0, :mode "signal_load", :type "常量", :unitTypeCode "dimension_Force", :varType "Number", :unitCode "unit_cdw13c2640", :axisType "signal_load", :unitType "56019f36-d734-40c0-8872-73b60304f80a"}, :control_input_bufferMode {:valueType "string", :variable "control_input_bufferMode", :isCheck false, :unit "", :value "RESTART", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_timeResetZero {:valueType "string", :variable "control_input_timeResetZero", :isCheck false, :unit "", :value "", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_bufferCode {:valueType "string", :variable "control_input_bufferCode", :isCheck false, :unit "", :value "input_lwjc_no2_down", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_keepStream {:valueType "string", :variable "control_input_keepStream", :isCheck false, :unit "", :value false, :mode "无", :type "常量", :unitTypeCode "", :varType "Boolean", :unitCode "", :unitType ""}, :control_input_load_interval {:valueType "string", :variable "control_input_load_interval", :isCheck false, :unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :value 0, :mode "signal_load", :type "常量", :unitTypeCode "dimension_Force", :varType "Number", :unitCode "unit_cdw13c2640", :axisType "signal_load", :unitType "56019f36-d734-40c0-8872-73b60304f80a"}, :control_input_saveDB {:valueType "", :variable "control_input_saveDB", :isCheck true, :unit "", :value true, :mode "无", :type "常量", :unitTypeCode "", :varType "Boolean", :unitCode "", :unitType ""}, :control_input_strain_interval {:valueType "string", :variable "control_input_strain_interval", :isCheck false, :unit "e655c1f0-26c0-41aa-9d1d-25c94c059d2e", :value 0, :mode "signal_ext", :type "常量", :unitTypeCode "dimension_Displacement", :varType "Number", :unitCode "unit_cdw4b9c1d4", :axisType "signal_pos", :unitType "6420a172-7a93-45c6-a8da-7ccb275a1aad"}, :control_input_time_interval {:valueType "string", :variable "control_input_time_interval", :isCheck true, :unit "f9b2c0be-a829-4072-878e-f5f592308f79", :value 0.001, :mode "signal_time", :type "常量", :unitTypeCode "dimension_Time", :varType "Number", :unitCode "unit_cdw3860fb2", :axisType "signal_time", :unitType "54891d75-4375-4253-8a5b-068423501a0a"}}, :edition "standard", :daq_code "DAQ66559e853b"}, :nameShow "DAQ", :task-type :daq, :status :ready, :id "daq-43bef327-78fb-4784-9a26-7efed0ab4432"}]], :task-type :parallels, :status :ready, :id "parallels-0d858c2f-1920-4025-b7b0-b6ca1bf1a3c0"} {:name "脚本执行子任务", :params {:schedule {:control_input_script {:valueType "string", :variable "control_input_script", :isCheck false, :unit "", :value "  // 计算第二段的值\n// 裂纹长度检查斜波下降段daq\nvar LWCDJC_DOWN_DAQ = Model.GetVarByName<BufferInputVar>(\"input_lwjc_no2_down\");\n\n// 客户填写的两个输入变量\nvar cankaotanxingmuliang=Model.GetVarByName<NumberInputVar>(\"input_LWCDJC_CKTXML\").Value;\nvar shijiliewenchangdu=Model.GetVarByName<NumberInputVar>(\"input_LWCDJC_SJCD\").Value;\n// Console.WriteLine(\"裂纹检查第二个下降段cankaotanxingmuliang:\"+cankaotanxingmuliang);\n// Console.WriteLine(\"裂纹检查第二个下降段的shijiliewenchangdu:\"+shijiliewenchangdu);\n\n// 获取下降段的负荷和变形数据\n\nvar loadBufferdata = LWCDJC_DOWN_DAQ[\"signal_load\"];\nvar extBufferdata = LWCDJC_DOWN_DAQ[\"signal_ext\"];\nint length=loadBufferdata.Length;\nif(length == 0){\n  FuncLibs.Logger.Error(\"裂纹长度检查未采集到数!\");\n  return false;\n}\nfor (int i = 0; i < length; i++)\n{\n    loadBufferdata[i] = Math.Abs(loadBufferdata[i]);\n}\n// FuncLibs.Logger.Error(\"裂纹检查第二个下降段的数组长度:\"+length);\n\n  FuncLibs.Logger.Error(\"=== 第二段GetCrackLength函数参数打印 ===\");\n  FuncLibs.Logger.Error(\"参数1 - extBufferdata数组长度: \" + extBufferdata.Length);\n  FuncLibs.Logger.Error(\"参数1 - extBufferdata数组内容: [\" + string.Join(\", \", extBufferdata) + \"]\");\n  FuncLibs.Logger.Error(\"参数2 - loadBufferdata数组长度: \" + loadBufferdata.Length);\n  FuncLibs.Logger.Error(\"参数2 - loadBufferdata数组内容: [\" + string.Join(\", \", loadBufferdata) + \"]\");\n  FuncLibs.Logger.Error(\"参数3 - length: \" + length);\n  FuncLibs.Logger.Error(\"=== 参数打印完毕，开始调用函数 ===\");\n\n//  调用公式 计算柔度和实际裂纹长度\nvar res= TestExpert.PlaneStrainFractureToughnessMethod.Commons\n.PlaneStrainFractureToughnessMethod.GetCrackLength(extBufferdata, loadBufferdata, length,out double crackLength, out double slope);\n// Console.WriteLine(\"裂纹检查第二个下降段的计算cres:\"+res);\n// FuncLibs.Logger.Error(\"裂纹检查第二个下降段的计算crackLength:\"+crackLength);\n// Console.WriteLine(\"裂纹检查第二个下降段的slope:\"+slope);\n\n// 计算弹性模量\nTestExpert.PlaneStrainFractureToughnessMethod.Commons\n.PlaneStrainFractureToughnessMethod.ModulusFromCrackLength( slope, cankaotanxingmuliang,  shijiliewenchangdu, out double modulus);\n// 裂纹长度检查结果二维数组\nvar LWCDJCJG_INPUT = Model.GetVarByName<DoubleArrayInputVar>(\"input_liewenchangdujieguo_doubleArray\");\n// 设置发送模式 0全量 1增量\nLWCDJCJG_INPUT.UpdateSendMode(1);\n\nFuncLibs.Logger.Error(\"裂纹检查第二个下降段的数组长度:\"+length);\n\nFuncLibs.Logger.Error(\"裂纹检查第二个下降段的弹性模量:\"+modulus);\nFuncLibs.Logger.Error(\"裂纹检查第二个下降段参考弹性模量:\"+cankaotanxingmuliang);\nFuncLibs.Logger.Error(\"裂纹检查第二个下降段的计算裂纹长度:\"+shijiliewenchangdu);\nFuncLibs.Logger.Error(\"裂纹检查第二个下降段的计算cres:\"+res);\nFuncLibs.Logger.Error(\"裂纹检查第二个下降段的参考裂纹长度:\"+crackLength);\nFuncLibs.Logger.Error(\"裂纹检查第二个下降段的卸载段柔度:\"+slope);\n\nDictionary<string, object> row = new()\n        {\n          { \"LW_JCJS\", 2.0 },\n          { \"LW_XZDRD\", Math.Round(slope,6) },\n          { \"LW_CKTXML\", Math.Round(cankaotanxingmuliang,2) },\n          { \"LW_JSTXML\", Math.Round(modulus,2)},\n          { \"LW_CKLWCD\", Math.Round(shijiliewenchangdu,2) },\n          { \"LW_JSLWCD\", Math.Round(crackLength,2) }\n        };\n\n// 添加数据\nLWCDJCJG_INPUT.AddRowData(row);\n// 通知ui\nLWCDJCJG_INPUT.SendValueToUI(Model.TemplateName);\n\n\nreturn false;", :mode "无", :type "常量", :unitTypeCode "", :varType "Text", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "脚本执行子任务2781750658"}, :nameShow "脚本执行子任务", :task-type :SubTaskEvalScript, :status :ready, :id "SubTaskEvalScript-153bfad7-49bc-43e7-a801-219b119c9104"} {:name "等待某个事件", :params {:schedule {:control_input_interval {:valueType "string", :variable "control_input_interval", :isCheck false, :unit "", :value "50", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_script {:valueType "", :variable "control_input_script", :isCheck false, :unit "", :value "var blockline = Model.GetVarByName<BufferInputVar>(\"input_LWCDJC_ALL_DAQ\").Value[\"signal_blockline\"].GetValue();\nif(blockline==5){\n  return true;\n}\nreturn false;", :mode "无", :type "常量", :unitTypeCode "", :varType "Text", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "等待某个事件d374a3bbf8"}, :nameShow "等待某个事件", :task-type :waitEvent, :status :ready, :id "waitEvent-911d94cb-a26d-4dda-bb0b-281270094ee5"} {:branch-idxs [0], :name "并行-2.5", :params nil, :branches [[{:name "信号变量检测", :params {:schedule {:control_input_bufferCode {:valueType "string", :variable "control_input_bufferCode", :isCheck false, :unit "", :value "input_LWCDJC_ALL_DAQ", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_condition {:valueType "string", :variable "control_input_condition", :isCheck false, :unit "", :value "1", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_signalCode {:valueType "string", :variable "control_input_signalCode", :isCheck false, :unit "", :value "signal_blockline", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "信号变量检测effacf4311"}, :nameShow "信号变量检测", :task-type :creepSignalCheck, :status :ready, :id "creepSignalCheck-05ef462b-d544-4857-9945-538a503ecb00"}] [{:name "DAQ", :params {:schedule {:control_input_move_interval {:valueType "string", :variable "control_input_move_interval", :isCheck false, :unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :value 0, :mode "signal_load", :type "常量", :unitTypeCode "dimension_Force", :varType "Number", :unitCode "unit_cdw13c2640", :axisType "signal_load", :unitType "56019f36-d734-40c0-8872-73b60304f80a"}, :control_input_bufferMode {:valueType "string", :variable "control_input_bufferMode", :isCheck false, :unit "", :value "RESTART", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_timeResetZero {:valueType "string", :variable "control_input_timeResetZero", :isCheck false, :unit "", :value "", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_bufferCode {:valueType "string", :variable "control_input_bufferCode", :isCheck false, :unit "", :value "input_lwjc_no3_down", :mode "无", :type "常量", :unitTypeCode "", :varType "Select", :unitCode "", :unitType ""}, :control_input_keepStream {:valueType "string", :variable "control_input_keepStream", :isCheck false, :unit "", :value false, :mode "无", :type "常量", :unitTypeCode "", :varType "Boolean", :unitCode "", :unitType ""}, :control_input_load_interval {:valueType "string", :variable "control_input_load_interval", :isCheck false, :unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :value 0, :mode "signal_load", :type "常量", :unitTypeCode "dimension_Force", :varType "Number", :unitCode "unit_cdw13c2640", :axisType "signal_load", :unitType "56019f36-d734-40c0-8872-73b60304f80a"}, :control_input_saveDB {:valueType "", :variable "control_input_saveDB", :isCheck true, :unit "", :value true, :mode "无", :type "常量", :unitTypeCode "", :varType "Boolean", :unitCode "", :unitType ""}, :control_input_strain_interval {:valueType "string", :variable "control_input_strain_interval", :isCheck false, :unit "e655c1f0-26c0-41aa-9d1d-25c94c059d2e", :value 0, :mode "signal_ext", :type "常量", :unitTypeCode "dimension_Displacement", :varType "Number", :unitCode "unit_cdw4b9c1d4", :axisType "signal_pos", :unitType "6420a172-7a93-45c6-a8da-7ccb275a1aad"}, :control_input_time_interval {:valueType "string", :variable "control_input_time_interval", :isCheck true, :unit "f9b2c0be-a829-4072-878e-f5f592308f79", :value 0.001, :mode "signal_time", :type "常量", :unitTypeCode "dimension_Time", :varType "Number", :unitCode "unit_cdw3860fb2", :axisType "signal_time", :unitType "54891d75-4375-4253-8a5b-068423501a0a"}}, :edition "standard", :daq_code "DAQ3ee5451f4d"}, :nameShow "DAQ", :task-type :daq, :status :ready, :id "daq-32891850-ce61-48d8-98cd-20634cf737c7"}]], :task-type :parallels, :status :ready, :id "parallels-084dfc84-6967-43f6-b73e-4bff0ca007fb"}]], :task-type :parallels, :status :running, :id "parallels-bbf6bb05-ccf9-4d31-b694-68ee26cce7d1"} {:name "脚本执行子任务", :params {:schedule {:control_input_script {:valueType "string", :variable "control_input_script", :isCheck false, :unit "", :value " // 计算第三段的值\n\n// 裂纹长度检查斜波下降段daq\nvar LWCDJC_DOWN_DAQ = Model.GetVarByName<BufferInputVar>(\"input_lwjc_no3_down\");\n\n// 客户填写的两个输入变量\nvar cankaotanxingmuliang=Model.GetVarByName<NumberInputVar>(\"input_LWCDJC_CKTXML\").Value;\nvar shijiliewenchangdu=Model.GetVarByName<NumberInputVar>(\"input_LWCDJC_SJCD\").Value;\n// Console.WriteLine(\"裂纹检查第三个下降段cankaotanxingmuliang:\"+cankaotanxingmuliang);\n// Console.WriteLine(\"裂纹检查第三个下降段的shijiliewenchangdu:\"+shijiliewenchangdu);\n\n// 获取下降段的负荷和变形数据\n\nvar loadBufferdata = LWCDJC_DOWN_DAQ[\"signal_load\"];\nvar extBufferdata = LWCDJC_DOWN_DAQ[\"signal_ext\"];\nint length=loadBufferdata.Length;\nif(length == 0){\n  FuncLibs.Logger.Error(\"裂纹长度检查未采集到数!\");\n  return false;\n}\nfor (int i = 0; i < length; i++)\n{\n    loadBufferdata[i] = Math.Abs(loadBufferdata[i]);\n}\n\n\n  FuncLibs.Logger.Error(\"=== 第三段GetCrackLength函数参数打印 ===\");\n  FuncLibs.Logger.Error(\"参数1 - extBufferdata数组长度: \" + extBufferdata.Length);\n  FuncLibs.Logger.Error(\"参数1 - extBufferdata数组内容: [\" + string.Join(\", \", extBufferdata) + \"]\");\n  FuncLibs.Logger.Error(\"参数2 - loadBufferdata数组长度: \" + loadBufferdata.Length);\n  FuncLibs.Logger.Error(\"参数2 - loadBufferdata数组内容: [\" + string.Join(\", \", loadBufferdata) + \"]\");\n  FuncLibs.Logger.Error(\"参数3 - length: \" + length);\n  FuncLibs.Logger.Error(\"=== 参数打印完毕，开始调用函数 ===\");\n//  调用公式 计算柔度和实际裂纹长度\nvar res= TestExpert.PlaneStrainFractureToughnessMethod.Commons\n.PlaneStrainFractureToughnessMethod.GetCrackLength(extBufferdata, loadBufferdata, length,out double crackLength, out double slope);\n// Console.WriteLine(\"裂纹检查第三个下降段的计算cres:\"+res);\n// Console.WriteLine(\"裂纹检查第三个下降段的计算crackLength:\"+crackLength);\n// Console.WriteLine(\"裂纹检查第三个下降段的slope:\"+slope);\n\n// 计算弹性模量\nTestExpert.PlaneStrainFractureToughnessMethod.Commons\n.PlaneStrainFractureToughnessMethod.ModulusFromCrackLength( slope, cankaotanxingmuliang,  shijiliewenchangdu, out double modulus);\n// 裂纹长度检查结果二维数组\nvar LWCDJCJG_INPUT = Model.GetVarByName<DoubleArrayInputVar>(\"input_liewenchangdujieguo_doubleArray\");\n// 设置发送模式 0全量 1增量\nLWCDJCJG_INPUT.UpdateSendMode(1);\nFuncLibs.Logger.Error(\"裂纹检查第三个下降段的数组长度:\"+length);\nFuncLibs.Logger.Error(\"裂纹检查第三个下降段的弹性模量:\"+modulus);\nFuncLibs.Logger.Error(\"裂纹检查第三个下降段参考弹性模量:\"+cankaotanxingmuliang);\nFuncLibs.Logger.Error(\"裂纹检查第三个下降段的计算裂纹长度:\"+shijiliewenchangdu);\nFuncLibs.Logger.Error(\"裂纹检查第三个下降段的计算cres:\"+res);\nFuncLibs.Logger.Error(\"裂纹检查第三个下降段的参考裂纹长度:\"+crackLength);\nFuncLibs.Logger.Error(\"裂纹检查第三个下降段的卸载段柔度:\"+slope);\n\n\nDictionary<string, object> row = new()\n        {\n          { \"LW_JCJS\", 3.0 },\n          { \"LW_XZDRD\", Math.Round(slope,6) },\n          { \"LW_CKTXML\", Math.Round(cankaotanxingmuliang,2) },\n          { \"LW_JSTXML\", Math.Round(modulus,2)},\n          { \"LW_CKLWCD\", Math.Round(shijiliewenchangdu,2) },\n          { \"LW_JSLWCD\", Math.Round(crackLength,2) }\n        };\n\n// 添加数据\nLWCDJCJG_INPUT.AddRowData(row);\n// 通知ui\nLWCDJCJG_INPUT.SendValueToUI(Model.TemplateName);\n\nreturn false;", :mode "无", :type "常量", :unitTypeCode "", :varType "Text", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "脚本执行子任务5ffe79478b"}, :nameShow "脚本执行子任务", :task-type :SubTaskEvalScript, :status :ready, :id "SubTaskEvalScript-17b2ea0b-475e-438a-a9e6-987c3d6bd8d7"} {:name "脚本执行子任务", :params {:schedule {:control_input_script {:valueType "string", :variable "control_input_script", :isCheck false, :unit "", :value "Model.GetVarByName<DoubleArrayListInputVar>(\"input_lwkz_curve_doubleArrayList\").DoubleArrayListDataToDB();\r\nModel.GetVarByName<DoubleArrayInputVar>(\"input_lwcdjc_ss\").DoubleArrayDataToDB();\r\nModel.GetVarByName<DoubleArrayInputVar>(\"input_liewenchangdujieguo_doubleArray\").DoubleArrayDataToDB();\r\nreturn false;", :mode "无", :type "常量", :unitTypeCode "", :varType "Text", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "脚本执行子任务f1981d3573"}, :nameShow "脚本执行子任务", :task-type :SubTaskEvalScript, :status :ready, :id "SubTaskEvalScript-da720ab0-0182-4e66-b915-3462002a8683"} {:name "脚本执行子任务", :params {:schedule {:control_input_script {:valueType "string", :variable "control_input_script", :isCheck false, :unit "", :value "string ydfs = \"负荷\";\r\nif(Model.GetVarByName<SelectInputVar>(\"input_LWCDJC_YDFS\").Value[2] == 0){\r\n  ydfs = \"位移\";\r\n}\r\nif(Model.GetVarByName<SelectInputVar>(\"input_LWCDJC_YDFS\").Value[2] == 1){\r\n  ydfs = \"负荷\";\r\n}\r\nif(Model.GetVarByName<SelectInputVar>(\"input_LWCDJC_YDFS\").Value[2] == 2){\r\n  ydfs = \"变形\";\r\n}\r\n\r\nstring significance = \"高\";\r\nstring type = \"信息\";\r\nstring grade = \"信息\";\r\nstring content = \"平面应变断裂韧度试验 裂纹长度检查操作试验运行 \"\r\n                 + Model.CurrentInst.Name\r\n                 + \",作动器控制 作动器运动停止 \"\r\n                 + \",运动方式：\"\r\n                 + ydfs\r\n                 + \",运动速度：\"\r\n                 + Model.GetVarByName<NumberInputVar>(\"input_LWCDJC_SD\").DisplayValue\r\n                 + Model.GetVarByName<NumberInputVar>(\"input_LWCDJC_SD\").DisplayUnitName   //单位\r\n                 + \",目标值:\"\r\n                 + Model.GetVarByName<NumberInputVar>(\"input_LWCDJC_MBZ\").DisplayValue\r\n                 + Model.GetVarByName<NumberInputVar>(\"input_LWCDJC_SD\").DisplayUnitName   //单位\r\n                 ;\r\n\r\nvar r = Model.RecordLog(significance, type, grade, content);\r\n\r\nConsole.WriteLine(\"记录控件日志是否成功：\" + r);\r\n\r\nModel.PubtaskStatus=false;\r\n\r\nreturn false;", :mode "无", :type "常量", :unitTypeCode "", :varType "Text", :unitCode "", :unitType ""}}, :edition "standard", :daq_code "脚本执行子任务4c984b53d9"}, :nameShow "脚本执行子任务", :task-type :SubTaskEvalScript, :status :ready, :id "SubTaskEvalScript-8ac6111a-8d6d-4963-9383-34d7420e558a"} {:name "结束", :params nil, :task-type :end, :status :ready, :id "end-node-action"}]}], :channel #object[clojure.core.async.impl.channels.ManyToManyChannel 0x210ae760 "clojure.core.async.impl.channels.ManyToManyChannel@210ae760"], :tick 200, :available-atom #object[clojure.lang.Atom 0x35513a2f {:status :ready, :val true}], :path-idx #object[clojure.lang.Atom 0x2226293e {:status :ready, :val {"creepSignalCheck-8d313bcb-d7cc-424e-bb19-d7d82337452b" (3 :branches 2 1 :branches 0 0), "parallels-0d858c2f-1920-4025-b7b0-b6ca1bf1a3c0" (3 :branches 2 4), "daq-0ac09652-82d1-4130-a028-2e081713c233" (3 :branches 2 1 :branches 1 0), "waitEvent-5cdadc8f-47b5-4639-a8b1-d26cbf98093f" (3 :branches 2 3), "SubTaskEvalScript-da720ab0-0182-4e66-b915-3462002a8683" (5), "xiebo-333a9f87-cae8-4b89-ab58-dd716d819b58" (3 :branches 0 5), "parallels-2b5d2fc6-d916-46ae-8155-5fba6aade7fe" (3 :branches 0 7), "daq-32891850-ce61-48d8-98cd-20634cf737c7" (3 :branches 2 7 :branches 1 0), "waitEvent-3989f720-9718-4165-ae9b-bb1c936ce43b" (3 :branches 1 3), "creepSignalCheck-71a89ec2-eaf7-4ba4-aa56-76c5a8ea8f69" (3 :branches 2 4 :branches 0 0), "SubTaskEvalScript-d38715eb-7301-437d-b693-3e546d3997f9" (2), "xiebo-b7edfcb1-c345-463c-aacd-c8d2eb206635" (3 :branches 0 4), "xiebo-c39d0131-3fdb-46d8-af94-0668968e4756" (3 :branches 0 3), "SubTaskEvalScript-2d644e51-aac7-477a-99b7-7f3efe700e51" (3 :branches 2 2), "if-else-0c336739-59ca-414f-b5af-804dc945aeee" (1), "xiebo-ec32daaf-a89d-4394-9719-b18e311efb7a" (3 :branches 0 2), "parallels-084dfc84-6967-43f6-b73e-4bff0ca007fb" (3 :branches 2 7), "xiebo-3756a5ec-49f2-4c21-a287-b92e938f5c45" (3 :branches 0 6), "SubtaskDialogBox-c57a9ae0-38a2-40a2-a47c-a56ea9f9d378" (1 :false-tasks 0), "waitEvent-426ba70c-a598-491b-ae16-d5a27715763f" (3 :branches 2 0), "SubTaskEvalScript-8ac6111a-8d6d-4963-9383-34d7420e558a" (6), "parallels-bbf6bb05-ccf9-4d31-b694-68ee26cce7d1" (3), "daq-a47ba0de-3727-4ea7-a523-2fd2fbd8fb7b" (3 :branches 0 7 :branches 1 0), "creepSignalCheck-05ef462b-d544-4857-9945-538a503ecb00" (3 :branches 2 7 :branches 0 0), "if-else-d9b9633e-d94c-4fbd-8f35-4969fb92685d" (3 :branches 1 0), "end-node-action" (7), "daq-43bef327-78fb-4784-9a26-7efed0ab4432" (3 :branches 2 4 :branches 1 0), "parallels-065884ba-5163-40ad-80d3-d98a12352be8" (3 :branches 2 1), "waitEvent-911d94cb-a26d-4dda-bb0b-281270094ee5" (3 :branches 2 6), "waitEvent-ffdc7080-10a2-478e-9add-ad7db595b4ee" (3 :branches 1 2), "SubTaskEvalScript-cc94c47e-def9-4413-a314-5379c15e2989" (1 :true-tasks 0), "SubTaskEvalScript-153bfad7-49bc-43e7-a801-219b119c9104" (3 :branches 2 5), "combinedWaveStartControl-5da849dd-b2c1-47db-8def-f3243aae6634" (3 :branches 0 0), "start-node-action" (0), "creepSignalCheck-9a1c7b0d-1760-4d5a-90d7-af26123789a5" (3 :branches 1 0 :false-tasks 0), "waitEvent-54a9826e-7e5e-4b46-a94a-3868836dcd6f" (3 :branches 1 1), "xiebo-7a9b46e2-ce29-4ac0-a55d-be7b124e697d" (3 :branches 0 1), "combinedWaveEndControl-07f9477f-ddab-4730-ac75-81ca40937ab5" (3 :branches 0 7 :branches 0 0), "waitEvent-00671a03-a007-4507-85a2-e7ad8bcd2c4c" (3 :branches 0 7 :branches 0 1), "waitEvent-93759e23-f394-4cb3-9acb-381a8c9be348" (3 :branches 0 7 :branches 2 0), "SubTaskEvalScript-17b2ea0b-475e-438a-a9e6-987c3d6bd8d7" (4)}}], :parent nil}
25-09-03 09:23:34:514 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【关闭项目时默认执行动作】"}
25-09-03 09:23:34:518 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "running"} 
 =============================================================

25-09-03 09:23:34:587 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【关闭项目重置参数】"}
25-09-03 09:23:34:589 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "running"} 
 =============================================================

25-09-03 09:23:34:708 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-common-service:85] - 项目关闭执行动作---完成
25-09-03 09:23:34:709 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "删除了【关闭项目 project_27】项目"}
25-09-03 09:23:34:715 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【打开项目默认执行动作】"}
25-09-03 09:23:34:723 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【裂纹检查新版流程图】"}
25-09-03 09:23:34:724 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "finished"} 
 =============================================================

25-09-03 09:23:34:773 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【联机】"}
25-09-03 09:23:34:775 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "finished"} 
 =============================================================

25-09-03 09:23:34:793 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:632] - Handling :finalize-project-quit-save event for project {:event :finalize-project-quit-save, :project_id 27, :user_id 1}
25-09-03 09:23:34:908 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 项目数据库 数据库缓存
25-09-03 09:23:34:925 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【启动】"}
25-09-03 09:23:34:930 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【终止裂纹长度检查】"}
25-09-03 09:23:34:931 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :State "finished"} 
 =============================================================

25-09-03 09:23:35:010 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【关闭项目时默认执行动作】"}
25-09-03 09:23:35:013 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "finished"} 
 =============================================================

25-09-03 09:23:35:015 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 系统数据库 数据库缓存
25-09-03 09:23:35:016 DESKTOP-3BSREDP WARN [clj-backend.modules.project.project-service:416] - 项目缓存刷新失败，但继续执行保存操作: {:success false, :message "部分缓存刷新失败", :details {:project true, :project-data false, :system true}}
25-09-03 09:23:35:018 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "保存了项目"}
25-09-03 09:23:35:036 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【横梁停止】"}
25-09-03 09:23:35:037 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-da399466-da97-449d-b998-0d7a0823cdd0", :State "finished"} 
 =============================================================

25-09-03 09:23:35:114 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 D:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1756707875343_project_27_copy.db 成功
25-09-03 09:23:35:146 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:639] - Project event listener started.
25-09-03 09:23:35:193 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【关闭项目时默认执行动作】"}
25-09-03 09:23:35:198 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【关闭项目重置参数】"}
25-09-03 09:23:35:199 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "finished"} 
 =============================================================

25-09-03 09:23:35:402 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_27", :ProcessID "project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :SubTaskID "onlyAction-7ab30145-6f3a-4ac1-a4c1-6d9ff9ba49cf", :MsgBody {:Cmd "abort", :InstCode "sample_14785d372", :ActionID "da399466-da97-449d-b998-0d7a0823cdd0"}}
25-09-03 09:23:35:404 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【横梁停止】"}
25-09-03 09:23:36:995 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 D:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1756707875343_project_27.db 成功
25-09-03 09:23:37:077 DESKTOP-3BSREDP WARN [clj-backend.db.connections:40] - 已为数据库连接 project_27 应用性能优化配置
25-09-03 09:23:37:078 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - 创建 project_27 项目库连接
25-09-03 09:26:07:444 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/host/instance 
 - 参数: 
 {:Hosts [{:HostId "2d06ec3a-9ecd-422c-a76a-1afc16b47b24", :HostName "1", :ConfigId nil, :StationHardware nil} {:HostId "da28d67a-f0fc-46a6-ab37-d412e6ab9f26", :HostName "2", :ConfigId nil, :StationHardware nil}]} 
 =============================================================

25-09-03 09:26:07:725 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/hardware 
 - 参数: 
 {:HardwareDtos {"hardware_simulator-0000-hw-ccss-id-0000" {:DaChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :AdChannels [{:Sensors ["传感器0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0}], :CreepAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 10, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 11, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 12, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 13, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 14, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 15, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 16, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 17, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 18, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 19, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}], :HandBoxChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil}], :Devices ({:Hwkey "hardware_simulator", :SubId 0, :DeviceId "9cdeaaab-9005-4fb9-b11a-279864d5c0cd"}), :ServoAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 7, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0}], :UpDirectValue 5, :TensileUpOrDown 7, :PressUpOrDown 6} {:Hwkey "hardware_simulator", :RealIndex 3, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0}], :UpDirectValue 5, :TensileUpOrDown 5, :PressUpOrDown 5}], :InputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :OutputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :TempAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 10, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}]}}} 
 =============================================================

25-09-03 09:26:07:829 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/api/appserver/config/info 
 - 参数: 
 {:AppServerIp "*************", :AppServerPort 3000, :ComputerId "MP2QAVV0", :AppServerApiPort 3000} 
 =============================================================

25-09-03 09:26:07:866 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 登录"}
25-09-03 09:26:11:097 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "打开项目 project_27"}
25-09-03 09:26:11:119 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:861] - 模板开始实例化
25-09-03 09:26:15:175 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "9d67f609-db17-48c4-993c-a32894d0ad21", :code 0, :msg "模板生成成功"}
25-09-03 09:26:15:175 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-9d67f609-db17-48c4-993c-a32894d0ad21 中添加消息 {:ProcessId "9d67f609-db17-48c4-993c-a32894d0ad21", :code 0, :msg "模板生成成功"}
25-09-03 09:26:15:176 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "9d67f609-db17-48c4-993c-a32894d0ad21", :code 0, :msg "模板生成成功"}
25-09-03 09:26:16:559 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【打开项目默认执行动作】"}
25-09-03 09:26:16:561 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "running"} 
 =============================================================

25-09-03 09:26:17:277 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【打开项目默认执行动作】"}
25-09-03 09:26:17:278 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "finished"} 
 =============================================================

25-09-03 09:26:27:056 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【联机】"}
25-09-03 09:26:27:057 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "running"} 
 =============================================================

25-09-03 09:26:50:078 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【启动】"}
25-09-03 09:26:50:080 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-679f70fa-d870-40d9-b121-c759b28044ed", :State "running"} 
 =============================================================

25-09-03 09:26:50:150 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【启动】"}
25-09-03 09:26:50:152 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-679f70fa-d870-40d9-b121-c759b28044ed", :State "finished"} 
 =============================================================

25-09-03 09:26:51:859 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【裂纹检查新版流程图】"}
25-09-03 09:26:51:863 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "running"} 
 =============================================================

25-09-03 09:26:55:836 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_27", :ScriptId "bed2daf7-25be-4f37-8368-0b9633df2040", :Result true}
25-09-03 09:26:55:837 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-bed2daf7-25be-4f37-8368-0b9633df2040 中添加消息 {:ProcessId "project_27", :ScriptId "bed2daf7-25be-4f37-8368-0b9633df2040", :Result true}
25-09-03 09:26:55:838 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_27", :ScriptId "bed2daf7-25be-4f37-8368-0b9633df2040", :Result true}
25-09-03 09:26:56:400 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_27", :ScriptId "c08486fe-4515-4d0e-a4fc-27d350808116", :Result true}
25-09-03 09:26:56:400 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-c08486fe-4515-4d0e-a4fc-27d350808116 中添加消息 {:ProcessId "project_27", :ScriptId "c08486fe-4515-4d0e-a4fc-27d350808116", :Result true}
25-09-03 09:26:56:401 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_27", :ScriptId "c08486fe-4515-4d0e-a4fc-27d350808116", :Result true}
25-09-03 09:30:23:109 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "【超级管理员】admin: 保存了【裂纹检查新版流程图】动作"}
25-09-03 09:30:23:222 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "【超级管理员】admin: 执行了【abort】操作"}
25-09-03 09:30:23:233 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【裂纹检查新版流程图】"}
25-09-03 09:30:23:235 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "finished"} 
 =============================================================

25-09-03 09:30:24:920 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "【超级管理员】admin: 保存了【裂纹检查新版流程图】动作"}
25-09-03 09:30:25:064 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【裂纹检查新版流程图】"}
25-09-03 09:30:25:069 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "running"} 
 =============================================================

25-09-03 09:30:25:174 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_27", :ScriptId "75007c53-729b-49df-a672-8690396b8472", :Result true}
25-09-03 09:30:25:175 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-75007c53-729b-49df-a672-8690396b8472 中添加消息 {:ProcessId "project_27", :ScriptId "75007c53-729b-49df-a672-8690396b8472", :Result true}
25-09-03 09:30:25:176 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_27", :ScriptId "75007c53-729b-49df-a672-8690396b8472", :Result true}
25-09-03 09:30:25:339 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_27", :ScriptId "2a48300a-8a98-4c9b-b4ff-dafbf2bf7ce9", :Result false}
25-09-03 09:30:25:339 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-2a48300a-8a98-4c9b-b4ff-dafbf2bf7ce9 中添加消息 {:ProcessId "project_27", :ScriptId "2a48300a-8a98-4c9b-b4ff-dafbf2bf7ce9", :Result false}
25-09-03 09:30:25:340 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_27", :ScriptId "2a48300a-8a98-4c9b-b4ff-dafbf2bf7ce9", :Result false}
25-09-03 09:30:38:864 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "【超级管理员】admin: 保存了【裂纹检查新版流程图】动作"}
25-09-03 09:30:38:954 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "【超级管理员】admin: 执行了【abort】操作"}
25-09-03 09:30:38:963 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【裂纹检查新版流程图】"}
25-09-03 09:30:38:965 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "finished"} 
 =============================================================

25-09-03 09:30:41:839 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "【超级管理员】admin: 保存了【裂纹检查新版流程图】动作"}
25-09-03 09:30:41:991 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【裂纹检查新版流程图】"}
25-09-03 09:30:41:995 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "running"} 
 =============================================================

25-09-03 09:30:42:095 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_27", :ScriptId "7d568da2-8a66-447f-8479-43ec7eba9f1b", :Result true}
25-09-03 09:30:42:095 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-7d568da2-8a66-447f-8479-43ec7eba9f1b 中添加消息 {:ProcessId "project_27", :ScriptId "7d568da2-8a66-447f-8479-43ec7eba9f1b", :Result true}
25-09-03 09:30:42:096 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_27", :ScriptId "7d568da2-8a66-447f-8479-43ec7eba9f1b", :Result true}
25-09-03 09:30:42:466 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_27", :ScriptId "221747b9-8d06-4e8b-8176-116fba83c933", :Result true}
25-09-03 09:30:42:467 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-221747b9-8d06-4e8b-8176-116fba83c933 中添加消息 {:ProcessId "project_27", :ScriptId "221747b9-8d06-4e8b-8176-116fba83c933", :Result true}
25-09-03 09:30:42:467 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_27", :ScriptId "221747b9-8d06-4e8b-8176-116fba83c933", :Result true}
25-09-03 09:31:03:880 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  PUT  调用外部接口:  http://localhost:5002/template/subtask 
 - 参数: 
 {:ClassName "project_27", :Subtask {:SubTaskId "daq-a47ba0de-3727-4ea7-a523-2fd2fbd8fb7b", :ActionId "d2a28ac5-6be5-4a4c-806f-424fc39131ee", :TemplateName "project_27", :InputVars {:control_input_move_interval {:Unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :Dimension "56019f36-d734-40c0-8872-73b60304f80a", :Mode "signal_load", :InstName "", :ContactInputCode "", :Code "control_input_move_interval", :Value 0, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_bufferMode {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_bufferMode", :Value "RESTART", :IsConstant false, :Type "Select", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_timeResetZero {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_timeResetZero", :Value nil, :IsConstant false, :Type "Select", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_bufferCode {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_bufferCode", :Value "input_LWCDJC_ALL_DAQ", :IsConstant false, :Type "Select", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_keepStream {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_keepStream", :Value true, :IsConstant false, :Type "Boolean", :ValueType "string", :IsOverall false, :IsCheck true}, :control_input_load_interval {:Unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :Dimension "56019f36-d734-40c0-8872-73b60304f80a", :Mode "signal_load", :InstName "", :ContactInputCode "", :Code "control_input_load_interval", :Value 0, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_saveDB {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_saveDB", :Value true, :IsConstant false, :Type "Boolean", :ValueType "", :IsOverall false, :IsCheck true}, :control_input_strain_interval {:Unit "e655c1f0-26c0-41aa-9d1d-25c94c059d2e", :Dimension "6420a172-7a93-45c6-a8da-7ccb275a1aad", :Mode "signal_ext", :InstName "", :ContactInputCode "", :Code "control_input_strain_interval", :Value 0, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_time_interval {:Unit "f9b2c0be-a829-4072-878e-f5f592308f79", :Dimension "54891d75-4375-4253-8a5b-068423501a0a", :Mode "signal_time", :InstName "", :ContactInputCode "", :Code "control_input_time_interval", :Value 0.001, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck true}}}, :ActionId "d2a28ac5-6be5-4a4c-806f-424fc39131ee"} 
 =============================================================

25-09-03 09:31:04:883 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "【超级管理员】admin: 保存了【裂纹检查新版流程图】动作"}
25-09-03 09:31:04:969 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "【超级管理员】admin: 执行了【abort】操作"}
25-09-03 09:31:04:979 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【裂纹检查新版流程图】"}
25-09-03 09:31:04:981 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "finished"} 
 =============================================================

25-09-03 09:31:06:069 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "【超级管理员】admin: 保存了【裂纹检查新版流程图】动作"}
25-09-03 09:31:06:232 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【裂纹检查新版流程图】"}
25-09-03 09:31:06:236 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "running"} 
 =============================================================

25-09-03 09:31:06:346 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_27", :ScriptId "3a8541ae-97e1-4b0a-9e06-3f685adf0b62", :Result true}
25-09-03 09:31:06:347 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-3a8541ae-97e1-4b0a-9e06-3f685adf0b62 中添加消息 {:ProcessId "project_27", :ScriptId "3a8541ae-97e1-4b0a-9e06-3f685adf0b62", :Result true}
25-09-03 09:31:06:347 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_27", :ScriptId "3a8541ae-97e1-4b0a-9e06-3f685adf0b62", :Result true}
25-09-03 09:31:06:569 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_27", :ScriptId "cd4265cb-8ae2-4a8f-a0a4-3a6276c5b2bc", :Result true}
25-09-03 09:31:06:570 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-cd4265cb-8ae2-4a8f-a0a4-3a6276c5b2bc 中添加消息 {:ProcessId "project_27", :ScriptId "cd4265cb-8ae2-4a8f-a0a4-3a6276c5b2bc", :Result true}
25-09-03 09:31:06:571 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_27", :ScriptId "cd4265cb-8ae2-4a8f-a0a4-3a6276c5b2bc", :Result true}
25-09-03 09:31:46:349 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "【超级管理员】admin: 保存了【裂纹检查新版流程图】动作"}
25-09-03 09:31:46:451 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "【超级管理员】admin: 执行了【abort】操作"}
25-09-03 09:31:46:459 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【裂纹检查新版流程图】"}
25-09-03 09:31:46:461 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "finished"} 
 =============================================================

25-09-03 09:34:06:246 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "【超级管理员】admin: 保存了【裂纹检查新版流程图】动作"}
25-09-03 09:34:06:429 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【裂纹检查新版流程图】"}
25-09-03 09:34:06:436 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "running"} 
 =============================================================

25-09-03 09:34:06:516 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_27", :ScriptId "a17832f3-215e-4a70-b64e-6c8adc4189b0", :Result true}
25-09-03 09:34:06:517 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-a17832f3-215e-4a70-b64e-6c8adc4189b0 中添加消息 {:ProcessId "project_27", :ScriptId "a17832f3-215e-4a70-b64e-6c8adc4189b0", :Result true}
25-09-03 09:34:06:518 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_27", :ScriptId "a17832f3-215e-4a70-b64e-6c8adc4189b0", :Result true}
25-09-03 09:34:06:737 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_27", :ScriptId "a9bee452-6c5c-4e08-9f89-9e7b11733ea7", :Result false}
25-09-03 09:34:06:738 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-a9bee452-6c5c-4e08-9f89-9e7b11733ea7 中添加消息 {:ProcessId "project_27", :ScriptId "a9bee452-6c5c-4e08-9f89-9e7b11733ea7", :Result false}
25-09-03 09:34:06:739 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_27", :ScriptId "a9bee452-6c5c-4e08-9f89-9e7b11733ea7", :Result false}
25-09-03 09:34:16:077 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "【超级管理员】admin: 保存了【裂纹检查新版流程图】动作"}
25-09-03 09:34:16:173 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "【超级管理员】admin: 执行了【abort】操作"}
25-09-03 09:34:16:180 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【裂纹检查新版流程图】"}
25-09-03 09:34:16:182 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "finished"} 
 =============================================================

