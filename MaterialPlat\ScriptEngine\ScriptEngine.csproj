﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <NoWarn>$(NoWarn);CS8600;CS8601;CS8602;CS8603;CS8604;CS8625;CS8618</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CsvHelper" Version="33.0.1" />
    <PackageReference Include="Microsoft.CodeAnalysis.CSharp" Version="4.4.0" />
    <PackageReference Include="Microsoft.CodeAnalysis.Scripting.Common" Version="4.4.0" />
	<PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="7.0.4" />
    <PackageReference Include="NLog" Version="5.1.1" />
    <PackageReference Include="System.Reactive" Version="5.0.0" />
  </ItemGroup>
   <ItemGroup>
    <PackageReference Include="NPOI" Version="2.7.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\DBUtils.Test\DBUtils.Test.csproj" />
    <ProjectReference Include="..\Logger\Logger.csproj" />
    <ProjectReference Include="..\MQ\MQ.csproj" />
    <ProjectReference Include="..\SignalVar\SignalVar.csproj" />
    <ProjectReference Include="..\HardwareSim\HwSim\HwSim16.csproj" />
  </ItemGroup>

</Project>
