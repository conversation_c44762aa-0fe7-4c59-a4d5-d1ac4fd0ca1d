25-09-03 09:11:09:819 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - TEMPLATE_INST_RSP
25-09-03 09:11:09:821 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"62795a17-93cc-4736-b188-23ffa1b927ee","code":0,"msg":"\u6A21\u677F\u751F\u6210\u6210\u529F"}
25-09-03 09:11:18:760 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-6a579b71-e5fb-4bef-9569-33de699c61ea 初始化
25-09-03 09:11:18:907 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:11:18:908 DESKTOP-3BSRE<PERSON> INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"start-node-action","MsgBody":{}}
25-09-03 09:11:19:267 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:11:19:268 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:11:19:274 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:11:19:275 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:11:19:280 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:11:19:282 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"SubTaskEvalScript-b339a41f-5402-476c-99f1-aac17677e715","MsgBody":{}}
25-09-03 09:11:19:740 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:11:19:742 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_F5\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:11:19:747 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:11:19:748 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_Fmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:11:19:753 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:11:19:754 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_FQ\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:11:19:761 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:11:19:763 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_KQ\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:11:19:769 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:11:19:770 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_KR\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:11:19:778 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:11:19:780 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_pmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:11:19:793 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:11:19:795 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_jzsl\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:11:19:805 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:11:19:807 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: -1,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: -1,\r\n  \u0022Code\u0022: \u0022input_type\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: \u0022\u0022,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:11:19:818 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:11:19:820 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"SubTaskEvalScript-0de23181-201f-40db-ba18-1836ed9ee3fd","MsgBody":{}}
25-09-03 09:11:20:006 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:11:20:007 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"end-node-action","MsgBody":{}}
25-09-03 09:11:20:009 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-03 09:11:20:120 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-03 09:17:22:449 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee 初始化
25-09-03 09:17:22:515 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:17:22:516 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"start-node-action","MsgBody":{}}
25-09-03 09:17:22:518 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:int flag = 0;

int deviceId = 0;
int ret = Model.station.Ccss_Connected(deviceId);
if (ret == 0) {
  /* 已连接 */ 
  flag++;
} else { 
  /* 未连接 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器连接失败，请重新连接控制器";
  return false;
}
int ret1 = Model.station.Ccss_DriveReady(deviceId);
if (ret1 == 0) { 
  /* 准备就绪 */ 
  flag++;
} else {
  /* 未就绪 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器未在启动状态";
  return false;
}
return true;
25-09-03 09:17:22:681 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:17:22:681 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u63A7\\u5236\\u5668\\u8FDE\\u63A5\\u5931\\u8D25\\uFF0C\\u8BF7\\u91CD\\u65B0\\u8FDE\\u63A5\\u63A7\\u5236\\u5668\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwcsts\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:17:22:687 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-03 09:17:22:688 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_27","ScriptId":"73f7de68-e31e-4aea-9ff5-578f4a8bfe5a","Result":false}
25-09-03 09:17:22:694 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:int flag = 0;

int deviceId = 0;
int ret = Model.station.Ccss_Connected(deviceId);
if (ret == 0) {
  /* 已连接 */ 
  flag++;
} else { 
  /* 未连接 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器连接失败，请重新连接控制器";
  return false;
}
int ret1 = Model.station.Ccss_DriveReady(deviceId);
if (ret1 == 0) { 
  /* 准备就绪 */ 
  flag++;
} else {
  /* 未就绪 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器未在启动状态";
  return false;
}
return true;
25-09-03 09:17:22:712 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:17:22:713 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u63A7\\u5236\\u5668\\u8FDE\\u63A5\\u5931\\u8D25\\uFF0C\\u8BF7\\u91CD\\u65B0\\u8FDE\\u63A5\\u63A7\\u5236\\u5668\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwcsts\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:17:22:717 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-03 09:17:22:718 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_27","ScriptId":"722d6276-38e4-42ba-96f5-e33fda25a871","Result":false}
25-09-03 09:17:24:739 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:17:24:740 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_one\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:17:24:745 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:17:24:746 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"SubtaskDialogBox-c57a9ae0-38a2-40a2-a47c-a56ea9f9d378","MsgBody":{}}
25-09-03 09:17:26:130 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1 初始化
25-09-03 09:17:28:379 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-679f70fa-d870-40d9-b121-c759b28044ed 初始化
25-09-03 09:17:29:987 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:17:29:988 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"SubTaskEvalScript-d38715eb-7301-437d-b693-3e546d3997f9","MsgBody":{}}
25-09-03 09:17:29:989 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:17:29:990 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"start-node-action","MsgBody":{}}
25-09-03 09:17:29:990 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:17:29:992 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"start-node-action","MsgBody":{}}
25-09-03 09:17:29:996 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:var blockline = Model.dVariable["blockline"];
if(blockline==1){
  return true;
}
return false;
25-09-03 09:17:30:047 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:17:30:048 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskEvalScript-38c7f155-9116-43d2-97c4-c0952ddf8d40","MsgBody":{}}
25-09-03 09:17:30:071 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:17:30:072 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"cmdConnection-d024261f-2793-4b35-bc63-6661078f2b6e","MsgBody":{}}
25-09-03 09:17:30:075 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:17:30:076 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"cmdConnection-d024261f-2793-4b35-bc63-6661078f2b6e","MsgBody":{}}
25-09-03 09:17:30:078 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:cmdConnection-d024261f-2793-4b35-bc63-6661078f2b6e不在执行状态, 而是 :finished
25-09-03 09:17:30:094 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:17:30:096 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"combinedWaveStartControl-5da849dd-b2c1-47db-8def-f3243aae6634","MsgBody":{}}
25-09-03 09:17:30:202 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-03 09:17:30:203 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_27","ScriptId":"8cb9aa23-8453-4c9d-ae6e-a5b86d3ee2df","Result":false}
25-09-03 09:17:33:694 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:17:33:695 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-03 09:17:33:698 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:17:33:699 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-03 09:17:33:700 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037不在执行状态, 而是 :finished
25-09-03 09:17:33:760 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:17:33:761 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_ifopendivice\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:17:33:783 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:17:33:784 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"end-node-action","MsgBody":{}}
25-09-03 09:17:33:786 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-679f70fa-d870-40d9-b121-c759b28044ed  Stop: {:parent nil}
25-09-03 09:17:33:843 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-03 09:17:34:018 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:17:34:019 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:17:34:204 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:17:34:205 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"creepSignalCheck-9a1c7b0d-1760-4d5a-90d7-af26123789a5","MsgBody":{}}
25-09-03 09:17:34:270 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:17:34:271 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-7a9b46e2-ce29-4ac0-a55d-be7b124e697d","MsgBody":{}}
25-09-03 09:17:34:507 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:17:34:509 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:17:34:746 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:17:34:747 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-ec32daaf-a89d-4394-9719-b18e311efb7a","MsgBody":{}}
25-09-03 09:17:34:756 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:17:34:757 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:17:34:989 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:17:34:990 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-c39d0131-3fdb-46d8-af94-0668968e4756","MsgBody":{}}
25-09-03 09:17:34:995 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:17:34:996 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:17:35:234 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:17:35:236 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-b7edfcb1-c345-463c-aacd-c8d2eb206635","MsgBody":{}}
25-09-03 09:17:35:243 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:17:35:245 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:17:35:481 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:17:35:482 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-333a9f87-cae8-4b89-ab58-dd716d819b58","MsgBody":{}}
25-09-03 09:17:35:491 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:17:35:492 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:17:35:731 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:17:35:732 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-3756a5ec-49f2-4c21-a287-b92e938f5c45","MsgBody":{}}
25-09-03 09:17:37:847 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9 初始化
25-09-03 09:17:37:918 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:17:37:918 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9","SubTaskID":"start-node-action","MsgBody":{}}
25-09-03 09:17:37:932 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:17:37:933 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_27","ProcessID":"project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9","SubTaskID":"onlyAction-0fd5b761-7c8e-432f-b8e5-5b70cd023b0d","MsgBody":{"Cmd":"abort","InstCode":"sample_14785d372","ActionID":"d2a28ac5-6be5-4a4c-806f-424fc39131ee"}}
25-09-03 09:17:37:940 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-03 09:17:37:941 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee  Stop: {:parent nil}
25-09-03 09:17:37:988 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-03 09:17:37:989 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:17:37:991 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9","SubTaskID":"onlyAction-0fd5b761-7c8e-432f-b8e5-5b70cd023b0d","MsgBody":{}}
25-09-03 09:17:37:993 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:17:37:994 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"daq-a47ba0de-3727-4ea7-a523-2fd2fbd8fb7b","MsgBody":{}}
25-09-03 09:17:37:999 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:17:38:001 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_27","ProcessID":"project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9","SubTaskID":"onlyAction-7ab30145-6f3a-4ac1-a4c1-6d9ff9ba49cf","MsgBody":{"Cmd":"start","InstCode":"sample_14785d372","ActionID":"da399466-da97-449d-b998-0d7a0823cdd0"}}
25-09-03 09:17:38:005 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-da399466-da97-449d-b998-0d7a0823cdd0 初始化
25-09-03 09:17:38:100 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:17:38:101 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-da399466-da97-449d-b998-0d7a0823cdd0","SubTaskID":"start-node-action","MsgBody":{}}
25-09-03 09:17:38:103 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:return true;
25-09-03 09:17:38:144 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-03 09:17:38:145 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_27","ScriptId":"0866a284-1bdc-47a7-8c61-16f140f5237d","Result":true}
25-09-03 09:18:38:973 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee 初始化
25-09-03 09:18:39:030 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:18:39:031 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"start-node-action","MsgBody":{}}
25-09-03 09:18:39:033 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:int flag = 0;

int deviceId = 0;
int ret = Model.station.Ccss_Connected(deviceId);
if (ret == 0) {
  /* 已连接 */ 
  flag++;
} else { 
  /* 未连接 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器连接失败，请重新连接控制器";
  return false;
}
int ret1 = Model.station.Ccss_DriveReady(deviceId);
if (ret1 == 0) { 
  /* 准备就绪 */ 
  flag++;
} else {
  /* 未就绪 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器未在启动状态";
  return false;
}
return true;
25-09-03 09:18:39:070 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:18:39:071 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u63A7\\u5236\\u5668\\u672A\\u5728\\u542F\\u52A8\\u72B6\\u6001\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwcsts\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:18:39:077 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-03 09:18:39:078 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_27","ScriptId":"df594848-6331-4a6d-8866-9671aaa8ccee","Result":false}
25-09-03 09:18:40:917 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:18:40:918 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_one\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:18:40:922 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:18:40:923 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"SubtaskDialogBox-c57a9ae0-38a2-40a2-a47c-a56ea9f9d378","MsgBody":{}}
25-09-03 09:18:41:231 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:18:41:232 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"SubTaskEvalScript-d38715eb-7301-437d-b693-3e546d3997f9","MsgBody":{}}
25-09-03 09:18:41:236 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:var blockline = Model.dVariable["blockline"];
if(blockline==1){
  return true;
}
return false;
25-09-03 09:18:41:297 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:18:41:298 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"combinedWaveStartControl-5da849dd-b2c1-47db-8def-f3243aae6634","MsgBody":{}}
25-09-03 09:18:41:321 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-03 09:18:41:323 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_27","ScriptId":"bb321806-4edd-4bf4-90c1-e354e537f636","Result":true}
25-09-03 09:18:41:386 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:18:41:387 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:18:41:477 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:18:41:478 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-7a9b46e2-ce29-4ac0-a55d-be7b124e697d","MsgBody":{}}
25-09-03 09:18:41:611 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:18:41:612 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:18:41:727 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:18:41:728 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-ec32daaf-a89d-4394-9719-b18e311efb7a","MsgBody":{}}
25-09-03 09:18:41:734 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:18:41:735 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:18:41:834 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:18:41:836 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-c39d0131-3fdb-46d8-af94-0668968e4756","MsgBody":{}}
25-09-03 09:18:41:923 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:18:41:924 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:18:42:015 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:18:42:016 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-b7edfcb1-c345-463c-aacd-c8d2eb206635","MsgBody":{}}
25-09-03 09:18:42:022 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:18:42:023 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:18:42:122 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:18:42:123 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-333a9f87-cae8-4b89-ab58-dd716d819b58","MsgBody":{}}
25-09-03 09:18:42:129 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:18:42:130 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:18:42:230 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:18:42:233 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-3756a5ec-49f2-4c21-a287-b92e938f5c45","MsgBody":{}}
25-09-03 09:18:42:250 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-679f70fa-d870-40d9-b121-c759b28044ed 初始化
25-09-03 09:18:42:472 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:18:42:473 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"start-node-action","MsgBody":{}}
25-09-03 09:18:42:499 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:18:42:500 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"cmdConnection-d024261f-2793-4b35-bc63-6661078f2b6e","MsgBody":{}}
25-09-03 09:18:42:552 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:18:42:553 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"end-node-action","MsgBody":{}}
25-09-03 09:18:42:554 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-679f70fa-d870-40d9-b121-c759b28044ed  Stop: {:parent nil}
25-09-03 09:18:42:634 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-03 09:23:34:510 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45 初始化
25-09-03 09:23:34:585 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-63526397-7d5a-4d20-8840-4fa2ceee596d 初始化
25-09-03 09:23:34:590 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:23:34:590 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"start-node-action","MsgBody":{}}
25-09-03 09:23:34:712 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-03 09:23:34:713 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-03 09:23:34:717 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-03 09:23:34:720 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-03 09:23:34:720 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee  Stop: {:parent nil}
25-09-03 09:23:34:768 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-03 09:23:34:771 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-03 09:23:34:771 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1  Stop: {:parent nil}
25-09-03 09:23:34:790 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SAVE_PROJECT
25-09-03 09:23:34:791 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProjectID":27}
25-09-03 09:23:34:792 DESKTOP-3BSREDP INFO [clj-scheduler.mq:47] - Received SAVE_PROJECT ZMQ message, dispatching to project channel: {:ProjectID 27}
25-09-03 09:23:34:793 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:23:34:793 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4","MsgBody":{}}
25-09-03 09:23:34:795 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:23:34:796 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"start-node-action","MsgBody":{}}
25-09-03 09:23:34:921 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-03 09:23:34:922 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-03 09:23:34:923 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-679f70fa-d870-40d9-b121-c759b28044ed  Stop: {:parent nil}
25-09-03 09:23:34:927 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-03 09:23:34:928 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-03 09:23:34:928 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9  Stop: {:parent nil}
25-09-03 09:23:34:933 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:23:34:935 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"daq-a47ba0de-3727-4ea7-a523-2fd2fbd8fb7b","MsgBody":{}}
25-09-03 09:23:34:936 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:23:34:937 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-da399466-da97-449d-b998-0d7a0823cdd0","SubTaskID":"stop-4e11b297-545d-46fc-9d3e-816533d9cbb8","MsgBody":{}}
25-09-03 09:23:34:952 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:23:34:953 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"daq-d5711df9-3137-4a67-9444-060b64552e70","MsgBody":{}}
25-09-03 09:23:34:960 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:23:34:961 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"daqRmc-9c1f5247-99e5-4a62-8446-73b344f248a2","MsgBody":{}}
25-09-03 09:23:35:006 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:23:35:007 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"end-node-action","MsgBody":{}}
25-09-03 09:23:35:008 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent nil}
25-09-03 09:23:35:033 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-03 09:23:35:034 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-03 09:23:35:035 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-da399466-da97-449d-b998-0d7a0823cdd0  Stop: {:parent {:Type "process-action", :ClassName "project_27", :ProcessID "project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :SubTaskID "onlyAction-7ab30145-6f3a-4ac1-a4c1-6d9ff9ba49cf", :MsgBody {:Cmd "start", :InstCode "sample_14785d372", :ActionID "da399466-da97-449d-b998-0d7a0823cdd0"}}}
25-09-03 09:23:35:172 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-03 09:23:35:188 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-03 09:23:35:190 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-03 09:23:35:191 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent nil}
25-09-03 09:23:35:194 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-03 09:23:35:196 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-03 09:23:35:196 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-63526397-7d5a-4d20-8840-4fa2ceee596d  Stop: {:parent nil}
25-09-03 09:23:35:225 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:23:35:226 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ifopendivice\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:23:35:235 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:23:35:236 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:23:35:241 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:23:35:242 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"SubTaskEvalScript-8e9f3ddc-e65c-429c-8106-cfe857f6f11a","MsgBody":{}}
25-09-03 09:23:35:243 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:SubTaskEvalScript-8e9f3ddc-e65c-429c-8106-cfe857f6f11a不在执行状态, 而是 :aborted
25-09-03 09:23:35:247 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-03 09:23:35:400 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:23:35:401 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_27","ProcessID":"project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9","SubTaskID":"onlyAction-7ab30145-6f3a-4ac1-a4c1-6d9ff9ba49cf","MsgBody":{"Cmd":"abort","InstCode":"sample_14785d372","ActionID":"da399466-da97-449d-b998-0d7a0823cdd0"}}
25-09-03 09:23:35:402 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-03 09:23:35:403 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-da399466-da97-449d-b998-0d7a0823cdd0  Stop: {:parent {:Type "process-action", :ClassName "project_27", :ProcessID "project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :SubTaskID "onlyAction-7ab30145-6f3a-4ac1-a4c1-6d9ff9ba49cf", :MsgBody {:Cmd "start", :InstCode "sample_14785d372", :ActionID "da399466-da97-449d-b998-0d7a0823cdd0"}}}
25-09-03 09:23:35:405 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-03 09:23:35:425 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:23:35:426 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-da399466-da97-449d-b998-0d7a0823cdd0","SubTaskID":"end-node-action","MsgBody":{}}
25-09-03 09:23:35:611 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:23:35:611 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9","SubTaskID":"onlyAction-7ab30145-6f3a-4ac1-a4c1-6d9ff9ba49cf","MsgBody":{}}
25-09-03 09:23:35:879 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:23:35:880 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"SubTaskEvalScript-8e9f3ddc-e65c-429c-8106-cfe857f6f11a","MsgBody":{}}
25-09-03 09:26:15:172 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - TEMPLATE_INST_RSP
25-09-03 09:26:15:174 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"9d67f609-db17-48c4-993c-a32894d0ad21","code":0,"msg":"\u6A21\u677F\u751F\u6210\u6210\u529F"}
25-09-03 09:26:16:557 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-6a579b71-e5fb-4bef-9569-33de699c61ea 初始化
25-09-03 09:26:16:680 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:26:16:681 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"start-node-action","MsgBody":{}}
25-09-03 09:26:16:765 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:26:16:766 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:26:16:770 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:26:16:771 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:26:16:775 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:26:16:775 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"SubTaskEvalScript-b339a41f-5402-476c-99f1-aac17677e715","MsgBody":{}}
25-09-03 09:26:17:212 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:26:17:213 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_F5\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:26:17:218 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:26:17:219 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_Fmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:26:17:222 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:26:17:223 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_FQ\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:26:17:228 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:26:17:228 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_KQ\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:26:17:233 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:26:17:234 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_KR\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:26:17:237 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:26:17:238 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_pmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:26:17:243 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:26:17:244 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: null,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: null,\r\n  \u0022Code\u0022: \u0022input_jzsl\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:26:17:252 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:26:17:253 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: -1,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: -1,\r\n  \u0022Code\u0022: \u0022input_type\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: \u0022\u0022,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:26:17:263 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:26:17:264 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"SubTaskEvalScript-0de23181-201f-40db-ba18-1836ed9ee3fd","MsgBody":{}}
25-09-03 09:26:17:271 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:26:17:273 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"end-node-action","MsgBody":{}}
25-09-03 09:26:17:274 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-03 09:26:17:329 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-03 09:26:27:054 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1 初始化
25-09-03 09:26:27:095 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:26:27:096 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"start-node-action","MsgBody":{}}
25-09-03 09:26:27:128 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:26:27:129 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskEvalScript-38c7f155-9116-43d2-97c4-c0952ddf8d40","MsgBody":{}}
25-09-03 09:26:30:469 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:26:30:470 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-03 09:26:30:476 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:26:30:477 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-03 09:26:30:478 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037不在执行状态, 而是 :finished
25-09-03 09:26:30:512 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:26:30:513 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_ifopendivice\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:26:50:076 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-679f70fa-d870-40d9-b121-c759b28044ed 初始化
25-09-03 09:26:50:126 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:26:50:126 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"start-node-action","MsgBody":{}}
25-09-03 09:26:50:140 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:26:50:141 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"cmdConnection-d024261f-2793-4b35-bc63-6661078f2b6e","MsgBody":{}}
25-09-03 09:26:50:146 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:26:50:147 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"end-node-action","MsgBody":{}}
25-09-03 09:26:50:148 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-679f70fa-d870-40d9-b121-c759b28044ed  Stop: {:parent nil}
25-09-03 09:26:50:193 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-03 09:26:51:857 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee 初始化
25-09-03 09:26:51:906 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:26:51:908 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"start-node-action","MsgBody":{}}
25-09-03 09:26:51:909 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:int flag = 0;

int deviceId = 0;
int ret = Model.station.Ccss_Connected(deviceId);
if (ret == 0) {
  /* 已连接 */ 
  flag++;
} else { 
  /* 未连接 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器连接失败，请重新连接控制器";
  return false;
}
int ret1 = Model.station.Ccss_DriveReady(deviceId);
if (ret1 == 0) { 
  /* 准备就绪 */ 
  flag++;
} else {
  /* 未就绪 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器未在启动状态";
  return false;
}
return true;
25-09-03 09:26:55:834 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-03 09:26:55:836 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_27","ScriptId":"bed2daf7-25be-4f37-8368-0b9633df2040","Result":true}
25-09-03 09:26:56:094 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:26:56:096 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"SubTaskEvalScript-cc94c47e-def9-4413-a314-5379c15e2989","MsgBody":{}}
25-09-03 09:26:56:291 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:26:56:293 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"SubTaskEvalScript-d38715eb-7301-437d-b693-3e546d3997f9","MsgBody":{}}
25-09-03 09:26:56:295 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:var blockline = Model.dVariable["blockline"];
if(blockline==1){
  return true;
}
return false;
25-09-03 09:26:56:361 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:26:56:361 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"combinedWaveStartControl-5da849dd-b2c1-47db-8def-f3243aae6634","MsgBody":{}}
25-09-03 09:26:56:399 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-03 09:26:56:399 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_27","ScriptId":"c08486fe-4515-4d0e-a4fc-27d350808116","Result":true}
25-09-03 09:26:56:499 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:26:56:500 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:26:56:609 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:26:56:610 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-7a9b46e2-ce29-4ac0-a55d-be7b124e697d","MsgBody":{}}
25-09-03 09:26:56:743 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:26:56:744 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:26:56:839 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:26:56:840 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-ec32daaf-a89d-4394-9719-b18e311efb7a","MsgBody":{}}
25-09-03 09:26:56:847 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:26:56:848 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:26:56:945 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:26:56:945 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-c39d0131-3fdb-46d8-af94-0668968e4756","MsgBody":{}}
25-09-03 09:26:56:952 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:26:56:952 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:26:57:050 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:26:57:051 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-b7edfcb1-c345-463c-aacd-c8d2eb206635","MsgBody":{}}
25-09-03 09:26:57:127 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:26:57:128 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:26:57:216 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:26:57:217 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-333a9f87-cae8-4b89-ab58-dd716d819b58","MsgBody":{}}
25-09-03 09:26:57:223 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:26:57:224 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:26:57:327 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:26:57:328 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-3756a5ec-49f2-4c21-a287-b92e938f5c45","MsgBody":{}}
25-09-03 09:27:35:135 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:27:35:137 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"combinedWaveEndControl-07f9477f-ddab-4730-ac75-81ca40937ab5","MsgBody":{}}
25-09-03 09:30:23:229 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-03 09:30:23:231 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee  Stop: {:parent nil}
25-09-03 09:30:23:249 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:30:23:251 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"daq-a47ba0de-3727-4ea7-a523-2fd2fbd8fb7b","MsgBody":{}}
25-09-03 09:30:23:252 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:daq-a47ba0de-3727-4ea7-a523-2fd2fbd8fb7b不在执行状态, 而是 :aborted
25-09-03 09:30:23:315 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-03 09:30:25:057 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee 初始化
25-09-03 09:30:25:145 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:30:25:145 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"start-node-action","MsgBody":{}}
25-09-03 09:30:25:146 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:int flag = 0;

int deviceId = 0;
int ret = Model.station.Ccss_Connected(deviceId);
if (ret == 0) {
  /* 已连接 */ 
  flag++;
} else { 
  /* 未连接 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器连接失败，请重新连接控制器";
  return false;
}
int ret1 = Model.station.Ccss_DriveReady(deviceId);
if (ret1 == 0) { 
  /* 准备就绪 */ 
  flag++;
} else {
  /* 未就绪 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器未在启动状态";
  return false;
}
return true;
25-09-03 09:30:25:172 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-03 09:30:25:173 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_27","ScriptId":"75007c53-729b-49df-a672-8690396b8472","Result":true}
25-09-03 09:30:25:199 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:30:25:200 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"SubTaskEvalScript-cc94c47e-def9-4413-a314-5379c15e2989","MsgBody":{}}
25-09-03 09:30:25:260 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:30:25:260 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"SubTaskEvalScript-d38715eb-7301-437d-b693-3e546d3997f9","MsgBody":{}}
25-09-03 09:30:25:262 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:var blockline = Model.dVariable["blockline"];
if(blockline==1){
  return true;
}
return false;
25-09-03 09:30:25:318 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:30:25:319 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"combinedWaveStartControl-5da849dd-b2c1-47db-8def-f3243aae6634","MsgBody":{}}
25-09-03 09:30:25:337 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-03 09:30:25:338 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_27","ScriptId":"2a48300a-8a98-4c9b-b4ff-dafbf2bf7ce9","Result":false}
25-09-03 09:30:25:397 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:30:25:397 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:30:25:488 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:30:25:490 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-7a9b46e2-ce29-4ac0-a55d-be7b124e697d","MsgBody":{}}
25-09-03 09:30:25:495 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:30:25:496 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:30:25:596 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:30:25:597 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-ec32daaf-a89d-4394-9719-b18e311efb7a","MsgBody":{}}
25-09-03 09:30:25:605 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:30:25:607 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:30:25:706 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:30:25:707 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-c39d0131-3fdb-46d8-af94-0668968e4756","MsgBody":{}}
25-09-03 09:30:25:747 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:30:25:748 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:30:25:878 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:30:25:879 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-b7edfcb1-c345-463c-aacd-c8d2eb206635","MsgBody":{}}
25-09-03 09:30:25:884 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:30:25:885 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:30:25:986 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:30:25:986 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-333a9f87-cae8-4b89-ab58-dd716d819b58","MsgBody":{}}
25-09-03 09:30:26:046 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:30:26:046 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:30:26:154 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:30:26:154 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-3756a5ec-49f2-4c21-a287-b92e938f5c45","MsgBody":{}}
25-09-03 09:30:26:469 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:30:26:470 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"creepSignalCheck-9a1c7b0d-1760-4d5a-90d7-af26123789a5","MsgBody":{}}
25-09-03 09:30:38:958 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-03 09:30:38:960 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee  Stop: {:parent nil}
25-09-03 09:30:39:020 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-03 09:30:41:988 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee 初始化
25-09-03 09:30:42:069 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:30:42:070 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"start-node-action","MsgBody":{}}
25-09-03 09:30:42:071 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:int flag = 0;

int deviceId = 0;
int ret = Model.station.Ccss_Connected(deviceId);
if (ret == 0) {
  /* 已连接 */ 
  flag++;
} else { 
  /* 未连接 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器连接失败，请重新连接控制器";
  return false;
}
int ret1 = Model.station.Ccss_DriveReady(deviceId);
if (ret1 == 0) { 
  /* 准备就绪 */ 
  flag++;
} else {
  /* 未就绪 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器未在启动状态";
  return false;
}
return true;
25-09-03 09:30:42:093 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-03 09:30:42:094 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_27","ScriptId":"7d568da2-8a66-447f-8479-43ec7eba9f1b","Result":true}
25-09-03 09:30:42:118 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:30:42:119 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"SubTaskEvalScript-cc94c47e-def9-4413-a314-5379c15e2989","MsgBody":{}}
25-09-03 09:30:42:353 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:30:42:354 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"SubTaskEvalScript-d38715eb-7301-437d-b693-3e546d3997f9","MsgBody":{}}
25-09-03 09:30:42:360 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:var blockline = Model.dVariable["blockline"];
if(blockline==1){
  return true;
}
return false;
25-09-03 09:30:42:418 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:30:42:420 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"combinedWaveStartControl-5da849dd-b2c1-47db-8def-f3243aae6634","MsgBody":{}}
25-09-03 09:30:42:465 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-03 09:30:42:466 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_27","ScriptId":"221747b9-8d06-4e8b-8176-116fba83c933","Result":true}
25-09-03 09:30:42:525 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:30:42:526 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:30:42:630 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:30:42:632 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-7a9b46e2-ce29-4ac0-a55d-be7b124e697d","MsgBody":{}}
25-09-03 09:30:42:689 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:30:42:690 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:30:42:784 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:30:42:785 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-ec32daaf-a89d-4394-9719-b18e311efb7a","MsgBody":{}}
25-09-03 09:30:42:794 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:30:42:795 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:30:42:892 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:30:42:893 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-c39d0131-3fdb-46d8-af94-0668968e4756","MsgBody":{}}
25-09-03 09:30:42:905 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:30:42:905 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:30:43:002 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:30:43:003 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-b7edfcb1-c345-463c-aacd-c8d2eb206635","MsgBody":{}}
25-09-03 09:30:43:009 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:30:43:010 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:30:43:106 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:30:43:108 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-333a9f87-cae8-4b89-ab58-dd716d819b58","MsgBody":{}}
25-09-03 09:30:43:114 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:30:43:115 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:30:43:217 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:30:43:217 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-3756a5ec-49f2-4c21-a287-b92e938f5c45","MsgBody":{}}
25-09-03 09:31:04:976 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-03 09:31:04:977 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee  Stop: {:parent nil}
25-09-03 09:31:04:987 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:31:04:988 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"daq-a47ba0de-3727-4ea7-a523-2fd2fbd8fb7b","MsgBody":{}}
25-09-03 09:31:04:989 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:daq-a47ba0de-3727-4ea7-a523-2fd2fbd8fb7b不在执行状态, 而是 :aborted
25-09-03 09:31:05:055 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-03 09:31:06:229 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee 初始化
25-09-03 09:31:06:319 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:31:06:320 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"start-node-action","MsgBody":{}}
25-09-03 09:31:06:322 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:int flag = 0;

int deviceId = 0;
int ret = Model.station.Ccss_Connected(deviceId);
if (ret == 0) {
  /* 已连接 */ 
  flag++;
} else { 
  /* 未连接 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器连接失败，请重新连接控制器";
  return false;
}
int ret1 = Model.station.Ccss_DriveReady(deviceId);
if (ret1 == 0) { 
  /* 准备就绪 */ 
  flag++;
} else {
  /* 未就绪 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器未在启动状态";
  return false;
}
return true;
25-09-03 09:31:06:344 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-03 09:31:06:345 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_27","ScriptId":"3a8541ae-97e1-4b0a-9e06-3f685adf0b62","Result":true}
25-09-03 09:31:06:367 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:31:06:368 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"SubTaskEvalScript-cc94c47e-def9-4413-a314-5379c15e2989","MsgBody":{}}
25-09-03 09:31:06:497 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:31:06:498 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"SubTaskEvalScript-d38715eb-7301-437d-b693-3e546d3997f9","MsgBody":{}}
25-09-03 09:31:06:500 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:var blockline = Model.dVariable["blockline"];
if(blockline==1){
  return true;
}
return false;
25-09-03 09:31:06:555 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:31:06:556 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"combinedWaveStartControl-5da849dd-b2c1-47db-8def-f3243aae6634","MsgBody":{}}
25-09-03 09:31:06:568 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-03 09:31:06:569 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_27","ScriptId":"cd4265cb-8ae2-4a8f-a0a4-3a6276c5b2bc","Result":true}
25-09-03 09:31:06:617 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:31:06:617 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:31:06:718 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:31:06:720 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-7a9b46e2-ce29-4ac0-a55d-be7b124e697d","MsgBody":{}}
25-09-03 09:31:06:787 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:31:06:787 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:31:06:882 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:31:06:882 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-ec32daaf-a89d-4394-9719-b18e311efb7a","MsgBody":{}}
25-09-03 09:31:06:891 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:31:06:892 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:31:06:990 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:31:06:991 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-c39d0131-3fdb-46d8-af94-0668968e4756","MsgBody":{}}
25-09-03 09:31:06:997 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:31:06:999 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:31:07:097 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:31:07:098 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-b7edfcb1-c345-463c-aacd-c8d2eb206635","MsgBody":{}}
25-09-03 09:31:07:103 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:31:07:104 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:31:07:206 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:31:07:207 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-333a9f87-cae8-4b89-ab58-dd716d819b58","MsgBody":{}}
25-09-03 09:31:07:215 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:31:07:216 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:31:07:314 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:31:07:315 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-3756a5ec-49f2-4c21-a287-b92e938f5c45","MsgBody":{}}
25-09-03 09:31:41:419 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:31:41:421 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"combinedWaveEndControl-07f9477f-ddab-4730-ac75-81ca40937ab5","MsgBody":{}}
25-09-03 09:31:41:422 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:31:41:424 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"combinedWaveEndControl-07f9477f-ddab-4730-ac75-81ca40937ab5","MsgBody":{}}
25-09-03 09:31:41:424 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:31:41:424 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:combinedWaveEndControl-07f9477f-ddab-4730-ac75-81ca40937ab5不在执行状态, 而是 :finished
25-09-03 09:31:41:425 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"combinedWaveEndControl-07f9477f-ddab-4730-ac75-81ca40937ab5","MsgBody":{}}
25-09-03 09:31:41:426 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:31:41:426 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:combinedWaveEndControl-07f9477f-ddab-4730-ac75-81ca40937ab5不在执行状态, 而是 :finished
25-09-03 09:31:41:426 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"combinedWaveEndControl-07f9477f-ddab-4730-ac75-81ca40937ab5","MsgBody":{}}
25-09-03 09:31:41:426 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:combinedWaveEndControl-07f9477f-ddab-4730-ac75-81ca40937ab5不在执行状态, 而是 :finished
25-09-03 09:31:46:455 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-03 09:31:46:457 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee  Stop: {:parent nil}
25-09-03 09:31:46:481 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:31:46:482 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"daq-a47ba0de-3727-4ea7-a523-2fd2fbd8fb7b","MsgBody":{}}
25-09-03 09:31:46:483 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:daq-a47ba0de-3727-4ea7-a523-2fd2fbd8fb7b不在执行状态, 而是 :aborted
25-09-03 09:31:46:542 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-03 09:34:06:426 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee 初始化
25-09-03 09:34:06:494 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:34:06:494 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"start-node-action","MsgBody":{}}
25-09-03 09:34:06:496 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:int flag = 0;

int deviceId = 0;
int ret = Model.station.Ccss_Connected(deviceId);
if (ret == 0) {
  /* 已连接 */ 
  flag++;
} else { 
  /* 未连接 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器连接失败，请重新连接控制器";
  return false;
}
int ret1 = Model.station.Ccss_DriveReady(deviceId);
if (ret1 == 0) { 
  /* 准备就绪 */ 
  flag++;
} else {
  /* 未就绪 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器未在启动状态";
  return false;
}
return true;
25-09-03 09:34:06:514 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-03 09:34:06:514 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_27","ScriptId":"a17832f3-215e-4a70-b64e-6c8adc4189b0","Result":true}
25-09-03 09:34:06:538 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:34:06:539 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"SubTaskEvalScript-cc94c47e-def9-4413-a314-5379c15e2989","MsgBody":{}}
25-09-03 09:34:06:662 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:34:06:664 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"SubTaskEvalScript-d38715eb-7301-437d-b693-3e546d3997f9","MsgBody":{}}
25-09-03 09:34:06:666 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:var blockline = Model.dVariable["blockline"];
if(blockline==1){
  return true;
}
return false;
25-09-03 09:34:06:716 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:34:06:719 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"combinedWaveStartControl-5da849dd-b2c1-47db-8def-f3243aae6634","MsgBody":{}}
25-09-03 09:34:06:733 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-03 09:34:06:735 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_27","ScriptId":"a9bee452-6c5c-4e08-9f89-9e7b11733ea7","Result":false}
25-09-03 09:34:06:836 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:34:06:838 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:34:06:933 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:34:06:934 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-7a9b46e2-ce29-4ac0-a55d-be7b124e697d","MsgBody":{}}
25-09-03 09:34:06:948 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:34:06:949 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:34:07:055 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:34:07:056 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-ec32daaf-a89d-4394-9719-b18e311efb7a","MsgBody":{}}
25-09-03 09:34:07:090 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:34:07:091 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:34:07:225 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:34:07:226 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-c39d0131-3fdb-46d8-af94-0668968e4756","MsgBody":{}}
25-09-03 09:34:07:232 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:34:07:232 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:34:07:341 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:34:07:341 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-b7edfcb1-c345-463c-aacd-c8d2eb206635","MsgBody":{}}
25-09-03 09:34:07:390 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:34:07:391 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:34:07:489 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:34:07:490 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-333a9f87-cae8-4b89-ab58-dd716d819b58","MsgBody":{}}
25-09-03 09:34:07:530 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-03 09:34:07:531 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_27\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-03 09:34:07:630 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:34:07:630 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-3756a5ec-49f2-4c21-a287-b92e938f5c45","MsgBody":{}}
25-09-03 09:34:08:081 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:34:08:081 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"creepSignalCheck-9a1c7b0d-1760-4d5a-90d7-af26123789a5","MsgBody":{}}
25-09-03 09:34:16:177 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-03 09:34:16:178 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee  Stop: {:parent nil}
25-09-03 09:34:16:187 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-03 09:34:16:188 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_27","ProcessID":"project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"daq-a47ba0de-3727-4ea7-a523-2fd2fbd8fb7b","MsgBody":{}}
25-09-03 09:34:16:189 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:daq-a47ba0de-3727-4ea7-a523-2fd2fbd8fb7b不在执行状态, 而是 :aborted
25-09-03 09:34:16:242 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
