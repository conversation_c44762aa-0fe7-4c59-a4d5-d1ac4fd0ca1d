# 阶段一：分析问题（HardwareConnector 迁移到 .NET Framework 4.8.1）

本文档用于梳理 HardwareConnector 从 .NET 6.0（Windows）迁移到 .NET Framework 4.8.1 的可行性、影响范围、风险点与候选方案，作为后续方案评审与实施的依据。

## 背景与目标
- 背景：当前 HardwareConnector 为 Windows Forms 程序，使用 ASP.NET Core Minimal API 承载 HTTP 接口与 Swagger，依赖多个 .NET 6 项目与 NuGet 包。
- 目标：将 HardwareConnector 迁移至 .NET Framework 4.8.1（x86），在不改变外部功能与接口行为（尽可能保持兼容）的前提下，完成编译与运行。

## 当前工程与依赖概览（节选）
- HardwareConnector（Exe，WinForms）
  - TargetFramework: net6.0-windows
  - 关键依赖（packages）：
    - Swashbuckle.AspNetCore 6.5.0（ASP.NET Core Swagger）
    - NetMQ 4.0.1.12
    - System.IO.Ports 8.0.0（包版本为 .NET 6/8 生态）
    - System.Reactive 5.0.0
    - NLog 5.1.1
    - Newtonsoft.Json 13.0.3
    - CommandLineParser 2.9.1
  - 项目引用：HwSim16（net6.0）、IHardware（net6.0）、Logger（net6.0）、FuncLibs（net6.0）
  - 使用特性：ASP.NET Core Minimal API（WebApplication）、Kestrel、自托管 Swagger UI、WinForms 的 ApplicationConfiguration.Initialize()

- IHardware（Library）
  - TargetFramework: net6.0
  - 包：MessagePack 3.1.3（支持 netstandard2.0）

- HwSim16（Library）
  - TargetFramework: net6.0
  - 包：Newtonsoft.Json 13.0.3


> 补充：客户侧 HwSim16 源码为 .NET Framework 4.8 版本
> - 这意味着我们可以将本地 HwSim16 项目切换为引用客户提供的 net48 版 DLL（或将本项目改为 multi-target 包含 net48），从而降低迁移阻力。
> - 若 HwSim16 保持 net48，则 HardwareConnector 迁移到 net48 时不会因该依赖受阻，但 IHardware/Logger/FuncLibs 仍需提供兼容目标（net48 或 netstandard2.0）。


> 关键结论：
> - 现有 HardwareConnector（.NET 6）无法加载 .NET Framework 4.8 的 HwSim16 DLL（跨运行时不兼容），即便使用反射也不可行。
> - 如需直接加载客户 HwSim16（net48），宿主进程也需为 .NET Framework（即方案A/C）。
> - 同时需确保 IHardware 接口程序集的“身份”兼容（名称、版本、公钥标识），否则即使接口签名一致也会因程序集标识不同导致类型不匹配；必要时可采用：
>   1) 统一使用客户侧的 IHardware（net48）作为接口契约；
>   2) 为本地 IHardware 提供 net48 目标并与客户DLL对齐；
>   3) 或者使用弱类型/消息协议适配（例如通过 NetMQ/HTTP 的 DTO 交互）以避免程序集身份耦合（对应方案B）。

- Logger（Library）
  - TargetFramework: net6.0
  - 包：Microsoft.Extensions.Configuration 6.0.0（一般支持 netstandard2.0）

- FuncLibs（Library）
  - TargetFramework: net6.0
  - 包：Microsoft.Data.Sqlite 7.0.1（通常为 net6/netstandard2.1，可能不支持 net48）；其余多为 netstandard2.0 友好

- 其他参考：HardwareConnector.Tests（net6.0-windows）

## 与 .NET Framework 4.8.1 的潜在不兼容点
1. ASP.NET Core Minimal API/Kestrel（Swashbuckle.AspNetCore 依赖 ASP.NET Core）
   - .NET Framework 4.8.1 无法直接使用 .NET 6 的 Minimal API/Kestrel 管道与中间件体系。
   - 若需要在 .NET Framework 上提供 HTTP + Swagger，通常选用 OWIN Self-Host + Web API 2 + Swashbuckle（非 AspNetCore 版）或使用 HttpListener 自行实现。

2. System.IO.Ports 8.0.0 包
   - 该版本面向 .NET 6/8。迁移到 .NET Framework 时，应改用 .NET Framework 自带的 System.IO.Ports（不通过 NuGet）。

3. WinForms 启动样板
   - .NET 6 使用 ApplicationConfiguration.Initialize() 等新模板；迁移至 .NET Framework 需恢复为传统 Program.Main + Application.EnableVisualStyles()/SetCompatibleTextRenderingDefault() 模式。

4. 项目间引用的 TFM 不匹配
   - HardwareConnector 当前引用的 IHardware、HwSim16、Logger、FuncLibs 全为 net6.0，不能被 .NET Framework 直接引用。
   - 需要这些库提供 net48 或 netstandard2.0 资产（后者需确认所有 API/包均兼容）。

5. 部分依赖包的 TFM 支持情况
   - Microsoft.Data.Sqlite 7.0.1：高概率仅支持 net6/netstandard2.1，不适配 net48；如需在 net48 使用，通常需降级到 5.x（netstandard2.0）或采用其他实现。
   - NetMQ、CommandLineParser、NLog、MessagePack、Newtonsoft.Json 大多有 netstandard2.0 支持，可在 net48 使用。

6. 代码层面的 API 差异
   - Minimal API（WebApplication、Results 等）不可用，需要重写为 Web API 2（OWIN）或 HttpListener。
   - 部分现代 C# 语言/库特性（隐式 using、可空性注解）需要在 .NET Framework CSProj 中手动配置或调整代码风格。

## 影响范围评估
- 最小影响也需要：
  - 重写 HardwareConnector 中 Web Server（ApiServer.cs）部分（路由、Swagger、中间件）
  - 替换 System.IO.Ports 的获取方式
  - 调整 WinForms 启动代码
- 若要求保留现有公共库（Logger、FuncLibs、IHardware、HwSim16）供引用：
  - 这些库至少需要 multi-target 到 net48 或 netstandard2.0；
  - FuncLibs 需处理 Microsoft.Data.Sqlite 的兼容性（降级或条件引用）。

## 初步方案（3 个）
### 方案 A：全量迁移至 .NET Framework 4.8.1（推荐在能接受改动较大的前提下）
- 内容：
  - HardwareConnector 改为 .NET Framework 4.8.1（WinForms + OWIN Self-Host WebApi2 + Swashbuckle for WebApi）
  - IHardware、HwSim16、Logger、FuncLibs 改为 multi-target（新增 net48 或 netstandard2.0 目标）；
  - 针对 FuncLibs 的 Microsoft.Data.Sqlite 进行降级或条件包引用（仅 net48 场景），确保编译通过；
  - System.IO.Ports 改用框架自带；
  - 替换 Minimal API，按现有路由/返回结构重写。
- 优点：
  - 最终单进程，部署形态与原先相近；
  - 各库统一 TFM 适配，长期维护清晰。
- 风险/成本：
  - 代码改动面较大（Web 层、依赖库多目标化、个别包降级与兼容处理）。

### 方案 B：进程解耦（.NET Framework 壳 + 现有 .NET 6 服务，通过 NetMQ/HTTP 通信）
- 内容：
  - 新建 HardwareConnector.FW（.NET Framework 4.8.1，WinForms/HttpListener 简化版），承载对厂商 DLL 的调用与数据交互；
  - 保留现有 HardwareConnector(.NET 6) 的 Web/Swagger 能力，由 .NET 6 进程继续对外提供 API；
  - 两进程通过 NetMQ/命名管道/本地 HTTP 通信，协议尽量复用现有消息格式。
- 优点：
  - 对现有代码侵入最小（Web 与大部分逻辑保持不动）；
  - 快速满足“需要 .NET Framework 承载某些 DLL”的诉求。
- 风险/成本：
  - 运维复杂度提高（两个进程的生命周期与健壮性管理）；
  - 不完全符合“将 HardwareConnector 转为 .NET Framework”的字面目标（属于旁路方案）。

### 方案 C：仅 HardwareConnector 迁移，依赖通过 netstandard2.0 适配层
- 内容：
  - HardwareConnector 迁移至 net48，Web 改为 OWIN/HttpListener；
  - 各被引用库尽量提供 netstandard2.0 资产（兼容 net48），避免全量迁移；
  - 对不兼容的库（如 FuncLibs 的 Sqlite）做条件编译或在 net48 下裁剪功能。
- 优点：
  - 较 A 改动面更小（部分库不必提供 net48，提供 netstandard2.0 即可）。
- 风险/成本：
  - 受限于个别包（Microsoft.Data.Sqlite 7.0.1）的 TFM，仍需降级与适配；
  - 需要严格控制 API 使用，避免 .NET 6 专属特性；
  - 仍然需要重写 Web 层。

## 风险与技术债
- Web 层迁移是主要工作量来源（Minimal API -> WebApi2/OWIN）。
- 依赖库多目标化涉及到 CI/打包流程调整，需新增测试矩阵（net6 与 net48）。
- 个别包降级（如 Sqlite）可能引入行为差异或性能影响，需要回归测试。

## 结论与建议
- 若确需将 HardwareConnector 本体转 .NET Framework 4.8.1，且可接受较大重构，优先考虑 方案 A。
- 若时间紧且可接受“两进程”架构，方案 B 能快速落地，但需明确长期演进路径。
- 方案 C 介于两者之间，但受限于 Sqlite 等包的兼容性，实施复杂度不一定更低。

## 待确认问题（请答复）
1. 是否要求继续提供 Swagger UI？若是，是否接受从 AspNetCore 版切换为 WebApi2 + Swashbuckle（非 AspNetCore）？
2. 是否可以接受引入“两进程”模式（方案 B）作为过渡？
3. 依赖库（尤其 FuncLibs）是否可以接受为 net48 增加兼容实现/降级包版本？
4. 是否必须维持平台为 x86？（目前 csproj 指定 x86）
5. 测试侧是否需要同步迁移（HardwareConnector.Tests）或改为手工/集成测试？

---
本文档由 Augment Agent 自动生成，用于后续方案选择与细化。
