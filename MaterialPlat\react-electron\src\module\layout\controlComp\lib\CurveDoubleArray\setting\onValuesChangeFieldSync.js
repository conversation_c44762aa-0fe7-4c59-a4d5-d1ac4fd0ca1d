import { getAllSample } from '@/module/layout/controlComp/lib/CurveDaqBuffer/utils/sample'
import { CURVE_STYLE } from './constants'

const updateCurves = ({
    isBufferCurve, channels, chartCurveGroup
}) => {
    const res = {}

    const curves = chartCurveGroup?.curves || {}
    const ySignalArray = chartCurveGroup?.ySignal || []

    Object.keys(curves).forEach((dataSourceKey) => {
        const dataSourceConfig = curves[dataSourceKey]

        let sampleColor
        if (isBufferCurve) {
            const sample = getAllSample().find(s => s.code === dataSourceKey)
            sampleColor = sample?.color
        }
        res[dataSourceKey] = {
            name: dataSourceConfig.name,
            lines: ySignalArray.map((code, index) => {
                const lineId = `${chartCurveGroup.name}-${dataSourceKey}-${code}`

                return {
                    // 先保证默认样式
                    ...CURVE_STYLE,
                    // 试样颜色
                    ...(sampleColor ? { color: sampleColor } : {}),
                    // 再继承之前样式
                    ...(dataSourceConfig?.lines?.find(i => i?.id === lineId) ?? {}),
                    yUnit: channels.find(c => c.code === code)?.unitId,
                    // 更新code
                    code,
                    yName: channels.find(c => c.code === code)?.name,
                    xName: channels.find(c => c.code === chartCurveGroup.xSignal)?.name,
                    // 曲线id ： 所在y轴名称-数据源的下标-通道的key
                    id: lineId,
                    // 曲线name 标签使用 ：所在曲线组名称-数据源的名称-通道的名称
                    name: `${chartCurveGroup?.name}-${dataSourceConfig?.name ?? ''}-${channels.find(c => c.code === code)?.name || code}`
                }
            })
        }
    })

    return res
}

const handleValuesChange = (changedValues, allValues, form, channels, isBufferCurve) => {
    // 1. x轴信号同步：任一curveGroup的x轴变化都同步到另一个curveGroup
    if (changedValues?.curveGroup?.yAxis?.xSignal) {
        // curveGroup.yAxis的x轴变化，同步到y2Axis
        const newXSignal = changedValues.curveGroup?.yAxis.xSignal
        form.setFieldValue(['curveGroup', 'y2Axis', 'xSignal'], newXSignal)
        form.setFieldValue(['xAxis', 'unit'], channels.find(c => c.code === newXSignal)?.unitId)
    }

    if (changedValues?.curveGroup?.y2Axis?.xSignal) {
        // curveGroup.y2Axis的x轴变化，同步到yAxis
        const newXSignal = changedValues.curveGroup?.y2Axis.xSignal
        form.setFieldValue(['curveGroup', 'yAxis', 'xSignal'], newXSignal)
        form.setFieldValue(['xAxis', 'unit'], channels.find(c => c.code === newXSignal)?.unitId)
    }

    // x轴信号变化 同步修改x轴名称
    if (changedValues?.curveGroup?.yAxis?.xSignal || changedValues?.curveGroup?.y2Axis?.xSignal) {
        const xSignal = changedValues?.curveGroup?.yAxis?.xSignal || changedValues?.curveGroup?.y2Axis?.xSignal
        // 同步修改曲线组的x轴信号
        form.setFieldValue(['curveGroup', 'yAxis', 'xSignal'], xSignal)
        form.setFieldValue(['curveGroup', 'y2Axis', 'xSignal'], xSignal)

        // 修改x轴名称
        form.setFieldValue(['xAxis', 'name'], channels.find(c => c.code === xSignal)?.name)
    }

    if (changedValues?.xAxis?.unit) {
        form.setFieldValue(['curveGroup', 'yAxis', 'xUnit'], changedValues?.xAxis?.unit)
        form.setFieldValue(['curveGroup', 'y2Axis', 'xUnit'], changedValues?.xAxis?.unit)
    }

    // y轴信号变化 修改y1轴名称
    if (changedValues?.curveGroup?.yAxis?.ySignal) {
        const y1Name = changedValues?.curveGroup?.yAxis?.ySignal.map(i => channels.find(c => c.code === i)?.name).join('/')

        form.setFieldValue(['yAxis', 'name'], y1Name)
    }

    // y2轴信号变化 修改y2轴名称
    if (changedValues?.curveGroup?.y2Axis?.ySignal) {
        const y2Name = changedValues?.curveGroup?.y2Axis?.ySignal.map(i => channels.find(c => c.code === i)?.name).join('/')

        form.setFieldValue(['y2Axis', 'name'], y2Name)
    }

    // 2. y轴变化时同步到对应curveGroup的curves的code中
    if (changedValues?.curveGroup?.yAxis?.ySignal) {
        // 更新每个curve组中的code
        const updatedCurves = updateCurves({
            isBufferCurve,
            channels,
            chartCurveGroup: allValues.curveGroup?.yAxis
        })

        form.setFieldValue(['curveGroup', 'yAxis', 'curves'], updatedCurves)
    }

    if (changedValues?.curveGroup?.y2Axis?.ySignal) {
        const updatedCurves = updateCurves({
            isBufferCurve,
            channels,
            chartCurveGroup: allValues.curveGroup?.y2Axis
        })

        form.setFieldValue(['curveGroup', 'y2Axis', 'curves'], updatedCurves)
    }
}

export default handleValuesChange

export {
    updateCurves
}
