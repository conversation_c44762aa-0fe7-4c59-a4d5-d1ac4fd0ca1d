import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useSelector } from 'react-redux'
import { message } from 'antd'

import ContextMenu from '@/components/contextMenu2'
import { getExportCSVDoubelArray, getHardwareMapping } from '@/utils/services'
import { getProcessID } from '@/utils/utils'
import store from '@/redux/store/index'

import ExportModal from '@/components/ExportModal/index'

const ContextMenuRightClick = ({
    id, layoutConfig, setOpen, config
}) => {
    const { t } = useTranslation()
    const openExperiment = useSelector(state => state.subTask.openExperiment)
    const systemConfig = useSelector(state => state.global.systemConfig)
    const stationList = useSelector(state => state.global.stationList)
    const cfgList = useSelector(state => state.global.cfgList)
    const projectList = useSelector(state => state.system.projectList)
    const projectId = useSelector(state => state.project.projectId)
    const [uploadModalOpen, setUploadModalOpen] = useState(false)

    const handleUploadModalOk = async (path, fileName) => {
        // 这里可以添加实际的导出逻辑
        setUploadModalOpen(false)

        const { cfgId } = await getHardwareMapping()

        const { stationId } = cfgList.find(c => c.cfgId === cfgId)

        const { stationName } = stationList.find(s => s.id === stationId)

        const { project_name } = projectList.find(p => p.project_id === Number(projectId))

        const basePath = path.at(-1) === '\\' ? path : `${path}\\`

        const exportPath = `${basePath}${stationName}_${stationId}\\${project_name}_${projectId}\\`

        const dimensionList = store.getState().global.unitList

        const codes = config?.colsConfig.filter(c => c.show !== false).map(c => {
            const unit = dimensionList?.map(i => i.units)?.flat()?.find(i => i.id === c?.typeParam?.unitId)

            return {
                code: c.code,
                name: c.showName,
                unit: unit?.name ?? '',
                proportion: unit?.proportion ?? 1
            }
        })

        await getExportCSVDoubelArray({
            templateName: getProcessID(),
            type: 'DoubleArray',
            arrayCode: config?.dataSourceCode,
            codes,
            path: exportPath,
            fileName
        })
    }

    const handleUploadModalCancel = () => {
        setUploadModalOpen(false)
    }

    return (
        <>
            <ContextMenu
                domId={id}
                layoutConfig={layoutConfig}
            >
                <div className="unique-content" onClick={() => setOpen(true)}>
                    {t('编辑二维数组表格')}
                </div>
                {
                    !openExperiment && (
                        <div
                            className="unique-content"
                            onClick={() => {
                                if (config?.dataSourceCode) {
                                    setUploadModalOpen(true)
                                } else {
                                    message.error('未选择数据源')
                                }
                            }}
                        >
                            {t('导出csv')}
                        </div>
                    )
                }
            </ContextMenu>

            <ExportModal
                open={uploadModalOpen}
                title="导出csv"
                defaultPath={systemConfig?.project_directory}
                onOk={handleUploadModalOk}
                onCancel={handleUploadModalCancel}
            />
        </>
    )
}

export default ContextMenuRightClick
