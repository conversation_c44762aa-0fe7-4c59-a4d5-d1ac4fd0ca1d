import React, {
    useLayoutEffect,
    useMemo,
    useRef,
    useImperativeHandle,
    forwardRef
} from 'react'
import {
    Form, Space, Select, Checkbox, Row, Col, Input
} from 'antd'
import { useTranslation } from 'react-i18next'
import { useSelector } from 'react-redux'

import useSelectInputVariable from '@/hooks/project/inputVariable/useSelectInputVariable'
import VTable from '@/components/vTable'
import { INPUT_VAIABLE_SELECT_OPTIONS_TYPE } from '@/utils/constants'
import { SOURCE_TYPE } from '../../../constants/constants'

const { Item, useWatch, useFormInstance } = Form

const TableComponent = ({
    axleSource = [], value = [], onChange, sourceType
}) => {
    const { t } = useTranslation()
    const unitList = useSelector(state => state.global.unitList)

    // 根据通道获取对应的单位选项
    const getUnitsForChannel = (channelCode) => {
        if (!channelCode || !axleSource || !unitList) return []

        const channel = axleSource.find(ch => ch.code === channelCode)
        if (!channel || !channel.dimensionId) return []

        const dimension = unitList.find(dim => dim.id === channel.dimensionId)
        return dimension?.units || []
    }

    // 生成轴名称（通道名称+单位名称）
    const generateAxisName = (channelCode, unitId) => {
        if (!channelCode) return ''

        const channel = axleSource.find(ch => ch.code === channelCode)
        const channelName = channel?.name || ''

        if (!unitId) return channelName

        const units = getUnitsForChannel(channelCode)
        const unit = units.find(u => u.id === unitId)
        const unitName = unit?.name || ''

        return unitName ? `${channelName}(${unitName})` : channelName
    }

    // 生成Y轴名称（多个通道时用/连接）
    const generateYAxisName = (yAxisChannels, yUnit) => {
        if (!yAxisChannels || yAxisChannels.length === 0) return ''

        if (Array.isArray(yAxisChannels) && yAxisChannels.length > 1) {
            // 多选时只显示通道名称，用/连接
            return yAxisChannels.map(code => {
                const channel = axleSource.find(ch => ch.code === code)
                return channel?.name || ''
            }).filter(Boolean).join('/')
        }
        // 单选时显示通道名称+单位
        const channelCode = Array.isArray(yAxisChannels) ? yAxisChannels[0] : yAxisChannels
        return generateAxisName(channelCode, yUnit)
    }

    const handleFieldChange = (rowIndex, field, newValue, option) => {
        const newData = [...value]
        newData[rowIndex] = {
            ...newData[rowIndex],
            [field]: newValue
        }

        // 如果y轴通道是多选（数组），则清空y轴单位
        if (field === 'ySignal' && Array.isArray(newValue) && newValue.length > 1) {
            newData[rowIndex].yUnit = ''
        }

        // 选中默认单位
        if (field === 'xSignal') {
            newData[rowIndex].xUnit = option.unitId
        }

        if (field === 'ySignal') {
            newData[rowIndex].yUnit = option.unitId
        }

        // 自动生成轴名称
        if (field === 'xSignal' || field === 'xUnit') {
            const xSignal = field === 'xSignal' ? newValue : newData[rowIndex].xSignal
            const xUnit = field === 'xUnit' ? newValue : newData[rowIndex].xUnit
            newData[rowIndex].xName = generateAxisName(xSignal, xUnit)
        }

        if (field === 'ySignal' || field === 'yUnit') {
            const ySignal = field === 'ySignal' ? newValue : newData[rowIndex].ySignal
            const yUnit = field === 'yUnit' ? newValue : newData[rowIndex].yUnit
            newData[rowIndex].yName = generateYAxisName(ySignal, yUnit)
        }

        onChange(newData)
    }

    const columns = [
        {
            title: t('选项'),
            dataIndex: 'label',
            width: 120,
            render: (text) => text
        },
        {
            title: t('X轴通道'),
            dataIndex: 'xSignal',
            width: 150,
            render: (v, row, index) => (
                <Select
                    options={axleSource}
                    fieldNames={{ label: 'name', value: 'code' }}
                    value={v}
                    style={{ width: '100%' }}
                    onChange={(newValue, option) => handleFieldChange(index, 'xSignal', newValue, option)}
                    placeholder={t('请选择X轴通道')}
                />
            )
        },
        {
            title: t('X轴单位'),
            dataIndex: 'xUnit',
            width: 120,
            render: (v, row, index) => {
                const xUnits = getUnitsForChannel(row.xSignal)
                return (
                    <Select
                        options={xUnits}
                        fieldNames={{ label: 'name', value: 'id' }}
                        value={v}
                        style={{ width: '100%' }}
                        onChange={(newValue) => handleFieldChange(index, 'xUnit', newValue)}
                        placeholder={t('请选择X轴单位')}
                        allowClear
                    />
                )
            }
        },
        {
            title: t('X轴名称'),
            dataIndex: 'xName',
            width: 120,
            render: (v, row, index) => (
                <Input
                    value={v}
                    onChange={(e) => handleFieldChange(index, 'xName', e.target.value)}
                    placeholder={t('请输入X轴名称')}
                />
            )
        },
        {
            title: t('Y轴通道'),
            dataIndex: 'ySignal',
            width: 150,
            render: (v, row, index) => (
                <Select
                    mode="multiple"
                    options={axleSource}
                    fieldNames={{ label: 'name', value: 'code' }}
                    // eslint-disable-next-line no-nested-ternary
                    value={Array.isArray(v) ? v : (v ? [v] : [])}
                    style={{ width: '100%' }}
                    onChange={(nv) => handleFieldChange(index, 'ySignal', nv)}
                    placeholder={t('请选择Y轴通道')}
                    maxTagCount="responsive"
                    maxCount={sourceType === SOURCE_TYPE.多数据源 ? 1 : undefined}
                />
            )
        },
        {
            title: t('Y轴单位'),
            dataIndex: 'yUnit',
            width: 120,
            render: (v, row, index) => {
                const isMultipleYAxis = Array.isArray(row.ySignal) && row.ySignal.length > 1
                const yChannelCode = Array.isArray(row.ySignal) ? row.ySignal[0] : row.ySignal
                const yUnits = getUnitsForChannel(yChannelCode)

                return (
                    <Select
                        options={yUnits}
                        fieldNames={{ label: 'name', value: 'id' }}
                        value={v}
                        style={{ width: '100%' }}
                        onChange={(newValue) => handleFieldChange(index, 'yUnit', newValue)}
                        placeholder={t('请选择Y轴单位')}
                        disabled={isMultipleYAxis}
                        title={isMultipleYAxis ? t('多选Y轴通道时不能设置单位') : ''}
                        allowClear
                    />
                )
            }
        },
        {
            title: t('Y轴名称'),
            dataIndex: 'yName',
            width: 120,
            render: (v, row, index) => (
                <Input
                    value={v}
                    onChange={(e) => handleFieldChange(index, 'yName', e.target.value)}
                    placeholder={t('请输入Y轴名称')}
                />
            )
        }
    ]

    return (
        <div className="table-layout" style={{ width: '100%' }}>
            <VTable
                rowKey="id"
                dataSource={value}
                bordered
                columns={columns}
                scroll={{ y: 400, x: 'max-content' }}
                pagination={false}
                style={{ width: '100%' }}
            />
        </div>
    )
}

const StepDefineAxis = ({ channels }) => {
    const { t } = useTranslation()
    const inputVariableSelect = useSelectInputVariable()
    const form = useFormInstance()
    const base = Form.useWatch(['base'], form)

    const inputVariableSelectCustom = useMemo(
        () => inputVariableSelect.filter(v => v.select_tab.selection === INPUT_VAIABLE_SELECT_OPTIONS_TYPE.列表中的单选项.value),
        []
    )

    return (
        <div className="step-define-axis">
            <Item name={['defineAxis', 'isDefineAxis']} valuePropName="checked">
                <Checkbox>
                    {t('启动曲线选项(勾选后单独设置的XY轴显示将失效)')}
                </Checkbox>
            </Item>
            <Row>
                <Col span={24}>
                    <Item
                        name={['defineAxis', 'inputCode']}
                        label={t('曲线变量源')}
                        labelCol={{ span: 3 }}
                        wrapperCol={{ span: 8 }}
                    >
                        <Select
                            options={inputVariableSelectCustom}
                            fieldNames={{ label: 'name', value: 'code' }}
                            onChange={(v, o) => {
                                const source = form.getFieldValue(['defineAxis', 'source']) ?? []

                                const temp = o?.select_tab?.items
                                    ?.map((m, index) => ({
                                        id: m.id || Date.now() + index,
                                        label: m.label,
                                        value: m.value,
                                        ySignal: [],
                                        yUnit: '',
                                        yName: '',
                                        xSignal: '',
                                        xUnit: '',
                                        xName: '',
                                        ...source.find(f => f.value === m.value)
                                    }))

                                form.setFieldValue(['defineAxis', 'source'], temp)
                            }}
                        />
                    </Item>
                </Col>
            </Row>
            <Row style={{ marginTop: 16 }}>
                <Col span={24}>
                    <Item name={['defineAxis', 'source']} noStyle>
                        <TableComponent
                            axleSource={channels}
                            sourceType={base?.sourceType}
                        />
                    </Item>
                </Col>
            </Row>
        </div>
    )
}

export default StepDefineAxis
